import * as Sentry from '@sentry/sveltekit';


Sentry.init({
  release: 'lycoris.cafe@2.0.0',
  dsn: 'https://<EMAIL>/4508156592455760',

  profilesSampleRate: 0.1,
  tracePropagationTargets: ['localhost', /^https:\/\/lycoris\.cafe/],

  replaysSessionSampleRate: 0.0,
  replaysOnErrorSampleRate: 0.0,

  integrations: [
    Sentry.replayIntegration({
      maskAllText: false,
      blockAllMedia: false
    })
  ],
  tracesSampleRate: 0.1,
  profilesSampler: (samplingContext) => {
    return 0.1;
  },
  beforeSend(event) {
    if (event.request && event.request.headers && event.request.headers['User-Agent']) {
      const userAgent = event.request.headers['User-Agent'].toLowerCase();

      const filteredKeywords = ['iOS', 'iphone', 'ipad', 'ipod', 'macintosh', 'safari', 'apple', 'playstation', 'ps4', 'ps5'];

      if (filteredKeywords.some((keyword) => userAgent.includes(keyword))) {
        return null;
      }
    }

    if (event.tags) {
      const filteredTags = ['iOS', 'iPhone', 'iPad', 'Safari', 'PlayStation'];
      if (filteredTags.some((tag) => event.tags.device === tag || event.tags.browser === tag)) {
        return null;
      }
    }

    return event;
  }
});

export const handleError = Sentry.handleErrorWithSentry();
