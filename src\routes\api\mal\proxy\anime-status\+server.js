// src/routes/api/mal/proxy/anime-status/+server.js
import { json } from '@sveltejs/kit';

export async function POST({ request }) {
  const { animeId, token } = await request.json();

  if (!animeId || !token) {
    return json({ error: 'Missing required parameters' }, { status: 400 });
  }

  try {
    // Make the request to MAL API through the server
    const response = await fetch(`https://api.myanimelist.net/v2/anime/${animeId}?fields=my_list_status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || `MAL API error: ${response.status}`);
      } catch (e) {
        throw new Error(`MAL API error: ${response.status} - ${errorText || 'No details available'}`);
      }
    }

    const data = await response.json();
    return json(data);
  } catch (error) {
    console.error('Error fetching anime status from MAL:', error);
    return json({ error: error.message }, { status: 500 });
  }
}