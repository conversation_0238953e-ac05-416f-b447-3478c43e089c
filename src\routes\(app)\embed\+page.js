import { error } from '@sveltejs/kit';

export async function load({ url, fetch }) {
  const searchParams = new URLSearchParams(url.search);

  try {
    const response = await fetch(`/api/embed?${searchParams.toString()}`);
    // <PERSON><PERSON> redirects from the API
    if (response.redirected) {
      return { location: response.url };
    }

    if (!response.ok) {
      const errorData = await response.json();
      // Use SvelteKit's error function to properly throw errors with status codes
      throw error(response.status, {
        message: errorData.error || 'Failed to load episode data',
        details: errorData.details || {}
      });
    }

    const data = await response.json();
    return data;

  } catch (err) {
    console.error('Error in page load function:', err);

    // If it's already a SvelteKit error with status, pass it through
    if (err.status && err.body) {
      throw err;
    }

    // If it has a status property but isn't a SvelteKit error
    if (err.status) {
      throw error(err.status, {
        message: err.message || 'Failed to load episode data',
        details: err.details || {}
      });
    }

    // Default to 500 for unexpected errors
    throw error(500, {
      message: err.message || 'Failed to load episode data',
      details: err.details || {}
    });
  }
}