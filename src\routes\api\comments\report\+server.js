// src/routes/api/comments/report/+server.js
import { json, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request, locals }) {
  try {
    const { commentId, reason } = await request.json();

    if (!commentId || !reason) {
      throw error(400, 'Comment ID and reason are required');
    }

    // Get user if authenticated, but don't require it
    const { session, user } = await locals.safeGetSession();
    const reporterId = user?.id || null;

    // If user is authenticated, check for existing report
    if (reporterId) {
      const { data: existing, error: checkError } = await supabase
        .from('comment_reports')
        .select('id')
        .eq('comment_id', commentId)
        .eq('reporter_id', reporterId)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGNF') {
        throw checkError;
      }

      if (existing) {
        return json({ message: 'Comment already reported' });
      }
    }

    // Create report
    const { error: insertError } = await supabase
      .from('comment_reports')
      .insert({
        comment_id: commentId,
        reporter_id: reporterId,
        reason: reason,
        status: 'pending',
        created_at: new Date().toISOString(),
      });

    if (insertError) throw insertError;

    return json({ success: true, message: 'Report submitted successfully' });
  } catch (err) {
    console.error('Error reporting comment:', err);
    throw error(err.status || 500, err.message || 'Error reporting comment');
  }
}