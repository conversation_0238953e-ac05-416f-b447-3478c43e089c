<!-- components/Modal.svelte -->
<script>
	import { modalStore } from '$lib/stores/modal';
	import FullscreenModal from '$lib/components/sections/comments/FullscreenModal.svelte';
	import * as Dialog from '$lib/components/ui/dialog';

	$: currentModal = {
		fullscreen: FullscreenModal
	}[$modalStore.type];

	function handleClose() {
		modalStore.close();
	}
</script>

<Dialog.Root open={$modalStore.isOpen} onOpenChange={handleClose}>
	<Dialog.Portal>
		<Dialog.Overlay class="fixed inset-0 bg-background/80 backdrop-blur-xs" />
		<Dialog.Content>
			{#if currentModal}
				<svelte:component this={currentModal} {...$modalStore.props} close={handleClose} />
			{/if}
		</Dialog.Content>
	</Dialog.Portal>
</Dialog.Root>
