// src/routes/api/comments/edit/+server.js
import { json, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function PUT({ request, locals }) {
  try {
    // Verify user is authenticated
    const { session, user } = await locals.safeGetSession();
    if (!session || !user) {
      throw error(401, 'Must be logged in to edit comments');
    }

    const { commentId, content, isSpoiler } = await request.json();

    if (!content?.trim()) {
      throw error(400, 'Comment content is required');
    }

    // Verify comment exists and belongs to user
    const { data: comment, error: selectError } = await supabase
      .from('comments')
      .select('user_id')
      .eq('id', commentId)
      .single();

    if (selectError || !comment) {
      throw error(404, 'Comment not found');
    }

    if (comment.user_id !== user.id) {
      throw error(403, 'Not authorized to edit this comment');
    }

    // Update the comment
    const { error: updateError } = await supabase
      .from('comments')
      .update({
        content: content.trim(),
        is_spoiler: isSpoiler || false,
        edited_at: new Date().toISOString()
      })
      .eq('id', commentId);

    if (updateError) throw updateError;

    return json({ success: true });
  } catch (err) {
    console.error('Error editing comment:', err);
    throw error(err.status || 500, err.message || 'Error editing comment');
  }
}