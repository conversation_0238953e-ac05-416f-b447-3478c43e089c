<script>
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { ThumbsUp, ThumbsDown, MoreVertical, MessageCircle, Maximize2 } from 'lucide-svelte';
	import { formatDistanceToNow } from 'date-fns';
	import { modalStore } from '$lib/stores/modal';
	import { onMount } from 'svelte';
	import { userStore } from '$lib/stores/userLogin';
	import { pl } from 'date-fns/locale';
	import ReportModal from '$lib/components/sections/comments/ReportModal.svelte';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	import { page } from '$app/stores';
	import { toast } from 'svelte-sonner';
	import { fade } from 'svelte/transition';
	import { progressStore } from '$lib/stores/progressStore';
	import { getPreferredTitle } from '$lib/utils/titleHelper';

	export let comment;
	export let context = 'home';
	export let depth = 0;
	export let maxDepth = 4;
	export let enforceMaxDepth = true;
	export let hideSubtitle = false;
	export let parentThreadLines = [];
	export let onContinueThread = undefined;
	export let bindCancelReply = undefined;
	export let preferRomaji;
	let disableReactButton = false;
	let showReportModal = false;
	let showUserModal = false;
	let collapsed = false;
	let expandedContent = false;
	let localEnforceMaxDepth = enforceMaxDepth;
	let isReplying = false;
	let draftReply = '';
	let contentElement;
	let showExpandButton = false;
	let isSubmittingReply = false;
	let isSpoilerReply = false;
	let isEditing = false;
	let editContent = '';
	let isSpoilerEdit = false;
	onMount(() => {
		if (bindCancelReply) {
			bindCancelReply(cancelReply);
		}
	});

	let showSpoilerContent = false;
	$: isSpoiler = comment.is_spoiler || comment.is_ai_detected_spoiler;

	$: user = $page.data.user;
	$: currentThreadColor = 'rgb(59, 130, 246)';
	$: baseSpacing = window.innerWidth >= 768 ? 16 : 8;
	$: calculatedMargin = depth === 0 ? 0 : baseSpacing;
	$: truncatedUsername = comment.author?.length > 25 ? comment.author.slice(0, 22) + '...' : comment.author;
	$: formattedTimestamp = formatDistanceToNow(new Date(comment.created_at), { addSuffix: true, locale: pl });
	$: fullTimestamp = new Date(comment.created_at).toLocaleString();
	$: hasDeepReplies = localEnforceMaxDepth && depth >= maxDepth && comment.replies?.length > 0;
	$: hasReplies = comment.replies?.length > 0;
	$: showFullContent = expandedContent;
	$: isCommentOwner = $userStore?.id === comment.user_id;
	$: isCommentEditable = isCommentOwner && !comment.deleted_at;
	$: isEdited = Boolean(comment.edited_at);
	// Add reactive variables for permissions
	$: isLoggedIn = $userStore?.role === 'authenticated';
	$: isCommentOwner = isLoggedIn && $userStore?.id === comment.user_id;
	$: canModifyComment = isCommentOwner && !comment.deleted_at;
	$: canReportComment = !isCommentOwner && !comment.deleted_at;
	$: canReact = isLoggedIn && !comment.deleted_at;

	// Add reaction state tracking
	$: hasLiked = comment.user_reactions?.includes('like');
	$: hasDisliked = comment.user_reactions?.includes('dislike');
	$: subtitleText = (() => {
		if (depth > 0 || hideSubtitle) return null;
		switch (context) {
			case 'home':
				return `${getPreferredTitle(comment, preferRomaji)} | Episode ${comment.episode_number}`;
			case 'anime':
				return `Odcinek ${comment.episode_number}`;
			case 'episode':
				return null;
			default:
				return null;
		}
	})();

	$: shouldAutoRevealSpoiler = (() => {
		if (!comment.is_spoiler && !comment.is_ai_detected_spoiler) {
			return false;
		}

		// If no specific episode is referenced, check if user has watched beyond the commented episode
		if (comment.episode_number) {
			return hasWatchedEpisode(comment.anilist_id, comment.episode_number);
		}

		return false;
	})();
	$: showSpoilerContent = shouldAutoRevealSpoiler || showSpoilerContent;

	function handleCloseModal() {
		showUserModal = false;
	}

	function hasWatchedEpisode(animeId, episodeNumber) {
		const progress = $progressStore[animeId]?.[episodeNumber]?.progress || 0;
		return progress >= 85;
	}

	async function handleReact(type) {
		if (!isLoggedIn) {
			showUserModal = true;
			return;
		}

		if (!canReact) return;
		disableReactButton = true;
		try {
			const endpoint = type === 'like' ? '/api/comments/like' : '/api/comments/dislike';
			const response = await fetch(endpoint, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					commentId: comment.id
				})
			});

			if (!response.ok) throw new Error('Failed to process reaction');

			const data = await response.json();
			comment.likes = data.likes;
			comment.dislikes = data.dislikes;

			// Update user reactions
			if (!comment.user_reactions) {
				comment.user_reactions = [];
			}

			// Handle toggling reactions
			const hasReaction = comment.user_reactions.includes(type);

			// Remove existing reactions first
			comment.user_reactions = comment.user_reactions.filter((r) => r !== 'like' && r !== 'dislike');

			// Add the new reaction if it wasn't already there (toggling behavior)
			if (!hasReaction) {
				comment.user_reactions.push(type);
			}

			// toast.success(type === 'like' ? 'Polubiono komentarz' : 'Oznaczono komentarz jako nielubiany');
		} catch (error) {
			console.error(`Error ${type}ing comment:`, error);
			toast.error('Nie udało się przetworzyć reakcji');
		} finally {
			disableReactButton = false;
		}
	}

	function checkContentHeight(node) {
		function calculateTextHeight(text, width, styles) {
			// Create temporary div for measurement
			const temp = document.createElement('div');
			temp.style.position = 'absolute';
			temp.style.visibility = 'hidden';
			temp.style.width = `${width}px`;

			// Copy relevant styles
			Object.assign(temp.style, {
				fontFamily: styles.fontFamily,
				fontSize: styles.fontSize,
				lineHeight: styles.lineHeight,
				padding: styles.padding,
				margin: styles.margin,
				border: styles.border,
				boxSizing: styles.boxSizing
			});

			temp.textContent = text;
			document.body.appendChild(temp);
			const height = temp.offsetHeight;
			document.body.removeChild(temp);

			return height;
		}

		function updateExpandButton() {
			if (!node) return;

			const styles = window.getComputedStyle(node);
			const lineHeight = parseFloat(styles.lineHeight);
			const maxLines = 3;
			const maxHeight = lineHeight * maxLines;

			// Calculate actual text height
			const width = node.offsetWidth;
			const text = node.textContent || '';
			const actualHeight = calculateTextHeight(text, width, styles);

			// Update button visibility
			showExpandButton = actualHeight > maxHeight;
		}

		// Create ResizeObserver to handle both window resize and content changes
		const resizeObserver = new ResizeObserver(() => {
			requestAnimationFrame(updateExpandButton);
		});

		// Observe both the node and its parent for size changes
		resizeObserver.observe(node);
		if (node.parentElement) {
			resizeObserver.observe(node.parentElement);
		}

		// Initial check
		updateExpandButton();

		return {
			destroy() {
				resizeObserver.disconnect();
			}
		};
	}

	function toggleCollapse(event) {
		event?.preventDefault();
		event?.stopPropagation();
		collapsed = !collapsed;
	}

	function toggleContent(event) {
		event?.preventDefault();
		expandedContent = !expandedContent;
	}

	async function startReply() {
		if (!isLoggedIn) {
			showUserModal = true;
			return;
		}
		isReplying = true;
	}

	function cancelReply() {
		isReplying = false;
		draftReply = '';
		isSpoilerReply = false;
	}

	async function submitReply() {
		if (!draftReply.trim() || !$userStore || isSubmittingReply) return;

		isSubmittingReply = true;

		try {
			toast.info('Dodawanie odpowiedzi...');
			const response = await fetch('/api/comments/reply', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					parentId: comment.id,
					content: draftReply,
					anilistId: comment.anilist_id,
					episodeNumber: comment.episode_number,
					isSpoiler: isSpoilerReply
				})
			});

			const data = await response.json();

			if (!response.ok) {
				if (response.status === 403 && data.message?.includes('zbanowane')) {
					const banReason = data.details || 'brak';
					throw new Error(`${data.message} ${banReason}`.trim());
				}
				throw new Error(data.message || 'Failed to submit reply');
			}

			if (!comment.replies) comment.replies = [];
			comment.replies = [...comment.replies, data];

			if (data.is_pending_review) {
				toast.warning('Odpowiedź została dodana i oczekuje na moderację');
			} else {
				toast.success('Odpowiedź została dodana');
			}

			cancelReply();
		} catch (error) {
			console.error('Error submitting reply:', error);
			toast.error(error.message || 'Nie udało się dodać odpowiedzi');
		} finally {
			isSubmittingReply = false;
		}
	}

	function startEdit() {
		editContent = comment.content;
		isSpoilerEdit = comment.is_spoiler || false;
		isEditing = true;
	}

	function cancelEdit() {
		isEditing = false;
		editContent = '';
	}

	async function saveEdit() {
		if (!isCommentEditable || (editContent.trim() === comment.content && isSpoilerEdit === comment.is_spoiler)) {
			cancelEdit();
			return;
		}

		try {
			const response = await fetch('/api/comments/edit', {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					commentId: comment.id,
					content: editContent.trim(),
					isSpoiler: isSpoilerEdit
				})
			});

			if (!response.ok) throw new Error('Failed to edit comment');

			comment = {
				...comment,
				content: editContent.trim(),
				is_spoiler: isSpoilerEdit,
				edited_at: new Date().toISOString()
			};

			toast.success('Komentarz został zaktualizowany');
			cancelEdit();
		} catch (error) {
			console.error('Error updating comment:', error);
			toast.error('Nie udało się zaktualizować komentarza');
		}
	}

	async function handleLike() {
		if (!$userStore) {
			modalStore.open('login');
			return;
		}

		try {
			const response = await fetch('/api/comments/like', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					commentId: comment.id,
					userId: $userStore.id
				})
			});

			if (!response.ok) throw new Error('Failed to like comment');
			const data = await response.json();
			comment.likes = data.likes;
		} catch (error) {
			console.error('Error liking comment:', error);
		}
	}

	async function handleDislike() {
		if (!$userStore) {
			modalStore.open('login');
			return;
		}

		try {
			const response = await fetch('/api/comments/dislike', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					commentId: comment.id,
					userId: $userStore.id
				})
			});

			if (!response.ok) throw new Error('Failed to dislike comment');
			const data = await response.json();
			comment.dislikes = data.dislikes;
		} catch (error) {
			console.error('Error disliking comment:', error);
		}
	}

	async function handleDelete() {
		if (!isCommentOwner) return;

		try {
			const response = await fetch('/api/comments/delete', {
				method: 'DELETE',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ commentId: comment.id })
			});

			if (!response.ok) throw new Error('Failed to delete comment');

			comment = {
				...comment,
				deleted_at: new Date().toISOString(),
				author: '[deleted]',
				content: '[redacted]',
				likes: 0,
				dislikes: 0,
				user_id: null,
				avatar: '/default-avatar.svg'
			};

			toast.success('Komentarz został usunięty');
		} catch (error) {
			console.error('Error deleting comment:', error);
			toast.error('Nie udało się usunąć komentarza');
		}
	}

	async function handleReport(commentId, reason) {
		try {
			const response = await fetch('/api/comments/report', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					commentId,
					reason
				})
			});

			if (!response.ok) throw new Error('Failed to report comment');
			toast.success('Zgłoszenie zostało wysłane');
		} catch (error) {
			console.error('Error reporting comment:', error);
			toast.error('Nie udało się wysłać zgłoszenia');
		}
	}

	function openFullscreenDialog() {
		modalStore.open('fullscreen', {
			comment,
			context,
			maxDepth,
			enforceMaxDepth: localEnforceMaxDepth
		});
	}

	function handleKeyDown(event) {
		if (event.key === 'Escape' && isEditing) {
			cancelEdit();
		} else if (event.key === 'Enter' && event.ctrlKey && isEditing) {
			saveEdit();
		}
	}

	function handleContinueThread() {
		if (depth === 0) {
			openFullscreenDialog();
		} else if (onContinueThread) {
			onContinueThread(comment);
		}
	}
</script>

<div class="comment-thread relative pb-2">
	<!-- Thread lines for nested comments -->
	{#if depth > 0}
		<div class="thread-lines absolute top-0 bottom-2 left-0 flex h-full">
			<div class="relative">
				<div
					class="mobile-thread-line absolute inset-y-0 w-[2px] cursor-pointer transition-all duration-200 hover:w-[3px] hover:opacity-100"
					style="background-color: {currentThreadColor}; opacity: 0.5; left: 0;"
					role="button"
					tabindex="0"
					on:click={toggleCollapse}
					on:keydown={handleKeyDown}
					aria-label="Przełącz zwinięcie wątku"
				/>
			</div>
		</div>
	{/if}

	<!-- Comment Article -->
	<article class="relative {depth === 0 ? 'bg-gray-800/50 p-2 sm:p-4' : ''} rounded-lg" style="margin-left: {calculatedMargin}px;">
		<!-- Comment Header -->
		<header class="mb-1 flex flex-col py-2 sm:flex-row sm:items-center sm:justify-between">
			<div class="flex min-w-0 items-center">
				<img src={comment.deleted_at ? '/default-avatar.svg' : comment.avatar} alt="" class="mr-2 h-6 w-6 shrink-0 rounded-full" />
				<div class="flex flex-row flex-wrap items-center">
					<!-- Username with tooltip -->
					<Tooltip.Root>
						<Tooltip.Trigger asChild>
							<button class="max-w-[150px] cursor-pointer truncate text-sm font-semibold text-white hover:underline focus:outline-hidden" on:click={toggleCollapse} aria-expanded={!collapsed}>
								{comment.deleted_at ? '[usunięto]' : truncatedUsername}
							</button>
						</Tooltip.Trigger>
						{#if !comment.deleted_at && comment.author?.length > 25}
							<Tooltip.Content>
								<p>{comment.author}</p>
							</Tooltip.Content>
						{/if}
					</Tooltip.Root>

					<!-- Timestamp with tooltip -->
					<Tooltip.Root>
						<Tooltip.Trigger>
							<time datetime={comment.created_at} class="ml-2 text-xs text-gray-400">
								{formattedTimestamp}
							</time>
							{#if isEdited}
								<span class="ml-1 text-xs text-gray-400">(edytowano)</span>
							{/if}
						</Tooltip.Trigger>
						<Tooltip.Content>
							<p>{fullTimestamp}</p>
						</Tooltip.Content>
					</Tooltip.Root>
				</div>
			</div>
		</header>

		{#if subtitleText}
			<p class="mb-2 text-xs text-gray-300">
				{#if context === 'home' && comment.anime_title}
					<a href="/anime/{comment.anilist_id}/{comment.anime_title.toLowerCase().replace(/\s+/g, '_')}/watch/{comment.episode_number}" class="text-blue-400 hover:underline">
						{subtitleText}
					</a>
				{:else}
					{subtitleText}
				{/if}
			</p>
		{/if}

		{#if !collapsed}
			<!-- Comment Content -->
			<div class="mb-2 text-sm text-white">
				<div bind:this={contentElement} use:checkContentHeight class="content-wrapper relative {!showFullContent ? 'line-clamp-3' : ''}">
					{#if comment.deleted_at}
						<p class="text-gray-400 italic">Ten komentarz został usunięty</p>
					{:else if isEditing}
						<Textarea
							bind:value={editContent}
							on:keydown={handleKeyDown}
							class="min-h-[100px] w-full resize-y rounded-md border border-gray-700 bg-transparent p-2 text-sm text-white"
							placeholder="Edytuj swój komentarz..."
						/>
						<div class="mt-2 flex justify-end gap-2">
							<div class="mt-[7px] mr-auto">
								<label class="flex items-center gap-2 text-sm text-gray-300">
									<input type="checkbox" bind:checked={isSpoilerEdit} class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500" />
									Oznacz jako spoiler
								</label>
							</div>
							<Button variant="ghost" size="sm" class="cursor-pointer" on:click={cancelEdit}>Anuluj</Button>
							<Button variant="primary" size="sm" class="cursor-pointer hover:bg-gray-800" on:click={saveEdit} disabled={!editContent.trim() || editContent.trim() === comment.content}>
								Zapisz zmiany
							</Button>
						</div>
					{:else}
						<div class="relative">
							{#if (comment.is_spoiler || comment.is_ai_detected_spoiler) && !showSpoilerContent && !shouldAutoRevealSpoiler}
								<!-- Spoiler blur overlay -->
								<!-- svelte-ignore a11y-click-events-have-key-events -->
								<!-- svelte-ignore a11y-no-static-element-interactions -->
								<div class="absolute inset-0 z-10 cursor-pointer overflow-hidden rounded-lg bg-gray-950" on:click={() => (showSpoilerContent = true)} transition:fade={{ duration: 300 }}>
									<div class="absolute inset-0 rounded-lg backdrop-blur-md transition-all duration-300"></div>
								</div>
							{/if}
							<div class={(comment.is_spoiler || comment.is_ai_detected_spoiler) && !showSpoilerContent ? 'invisible' : ''}>
								{comment.content}
							</div>
						</div>
					{/if}
				</div>

				{#if !comment.deleted_at && showExpandButton && !isEditing}
					<button class="mt-1 text-xs text-blue-400 hover:underline focus:outline-hidden" on:click={toggleContent} aria-expanded={showFullContent}>
						{showFullContent ? 'Pokaż mniej' : 'Pokaż więcej'}
					</button>
				{/if}
			</div>

			{#if !comment.deleted_at}
				<!-- Comment Actions -->
				<div class="mt-2 flex flex-wrap items-center justify-between gap-2">
					<div class="flex items-center gap-2">
						<!-- Action Menu -->
						{#if canModifyComment || canReportComment}
							<DropdownMenu.Root>
								<DropdownMenu.Trigger>
									<Button variant="ghost" size="sm" class="cursor-pointer p-2">
										<MoreVertical class="h-3 w-3" />
										<span class="sr-only">Więcej opcji</span>
									</Button>
								</DropdownMenu.Trigger>
								<DropdownMenu.Content class="!z-[9999]">
									{#if canModifyComment}
										<DropdownMenu.Item on:click={startEdit}>
											<button class="w-full cursor-pointer text-left">Edytuj komentarz</button>
										</DropdownMenu.Item>
										<DropdownMenu.Item on:click={handleDelete}>
											<button class="w-full cursor-pointer text-left">Usuń komentarz</button>
										</DropdownMenu.Item>
									{:else if canReportComment}
										<DropdownMenu.Item on:click={() => (showReportModal = true)}>
											<button class="w-full cursor-pointer text-left">Zgłoś nadużycie</button>
										</DropdownMenu.Item>
									{/if}
								</DropdownMenu.Content>
							</DropdownMenu.Root>
						{/if}

						<!-- Fullscreen button for root comments -->
						{#if depth === 0}
							<Button variant="ghost" size="sm" class="cursor-pointer p-2 text-gray-400 hover:text-white" on:click={openFullscreenDialog}>
								<Maximize2 class="h-3 w-3" />
								<span class="ml-1 hidden sm:inline">Pełny ekran</span>
							</Button>
						{/if}

						<!-- Reply button -->
						<Button variant="ghost" size="sm" class="cursor-pointer p-2 text-gray-400 hover:text-white" on:click={startReply}>
							<MessageCircle class="h-3 w-3" />
							<span class="ml-1 hidden sm:inline">Odpowiedz</span>
						</Button>
					</div>

					<!-- Reaction buttons -->
					<div class="flex items-center gap-2">
						<Button
							disabled={disableReactButton}
							variant="ghost"
							size="sm"
							class="cursor-pointer p-2 {hasLiked ? 'text-blue-400' : 'text-gray-400'} hover:text-white"
							on:click={() => handleReact('like')}
						>
							<ThumbsUp class="h-3 w-3" />
							<span class="ml-1">{comment.likes}</span>
						</Button>
						<Button
							disabled={disableReactButton}
							variant="ghost"
							size="sm"
							class="cursor-pointer p-2 {hasDisliked ? 'text-red-400' : 'text-gray-400'} hover:text-white"
							on:click={() => handleReact('dislike')}
						>
							<ThumbsDown class="h-3 w-3" />
							<span class="ml-1">{comment.dislikes}</span>
						</Button>
					</div>
				</div>
			{/if}

			<!-- Reply form -->
			<!-- Reply form -->
			{#if isReplying}
				<div class="mt-4 border-l-2 border-blue-500/50 pl-4">
					<article class="rounded-lg bg-gray-800/30 p-3">
						<header class="mb-2 flex items-center">
							<img src={$userStore.avatar || '/default-avatar.svg'} alt="" class="mr-2 h-6 w-6 rounded-full" />
							<span class="text-sm font-semibold text-white/70"> Podgląd twojej odpowiedzi </span>
						</header>

						<textarea bind:value={draftReply} placeholder="Napisz swoją odpowiedź..." class="min-h-[100px] w-full resize-y rounded-md border border-gray-700 bg-transparent p-2 text-sm text-white" />

						<div class="mt-2 flex justify-end gap-2">
							<!-- Add the spoiler checkbox here -->
							<div class="mt-[7px] mr-auto">
								<label class="flex items-center gap-2 text-sm text-gray-300">
									<input type="checkbox" bind:checked={isSpoilerReply} class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500" />
									Oznacz jako spoiler
								</label>
							</div>

							<Button variant="ghost" size="sm" class="cursor-pointer" on:click={cancelReply}>Anuluj</Button>
							<Button variant="primary" size="sm" class="cursor-pointer" on:click={submitReply} disabled={!draftReply.trim() || isSubmittingReply}>
								{isSubmittingReply ? 'Wysyłanie...' : 'Wyślij odpowiedź'}
							</Button>
						</div>
					</article>
				</div>
			{/if}

			<!-- Nested replies handling -->
			{#if hasDeepReplies}
				<div class="mt-4 w-full">
					<Button variant="secondary" size="sm" on:click={onContinueThread} class="flex w-full items-center justify-center gap-2 sm:w-auto">
						<Maximize2 class="h-3 w-3" />
						Kontynuuj ten wątek ({comment.replies.length}
						{comment.replies.length === 1 ? 'odpowiedź' : 'odpowiedzi'})
					</Button>
				</div>
			{:else if hasReplies}
				<div class="replies mt-4">
					{#each comment.replies as reply}
						{#if !(reply.deleted_at && (!reply.replies || reply.replies.length === 0))}
							<svelte:self comment={reply} {context} depth={depth + 1} {maxDepth} enforceMaxDepth={localEnforceMaxDepth} {hideSubtitle} {parentThreadLines} {onContinueThread} {bindCancelReply} />
						{/if}
					{/each}
				</div>
			{/if}
		{/if}
	</article>
</div>

<UserProfileModal bind:open={showUserModal} {user} onClose={handleCloseModal} />

{#if showReportModal}
	<ReportModal commentId={comment.id} onSubmit={handleReport} close={() => (showReportModal = false)} />
{/if}

<style>
	.thread-lines {
		pointer-events: none;
		z-index: 0;
		padding-left: 2px;
	}

	.thread-lines > div {
		pointer-events: auto;
	}

	div[role='button']:focus-visible {
		outline: 2px solid rgb(59, 130, 246);
		outline-offset: 2px;
		border-radius: 2px;
	}

	article {
		position: relative;
		z-index: 1;
	}

	@media (max-width: 640px) {
		.thread-lines {
			left: 0;
			padding-bottom: 8px;
		}

		.mobile-thread-line {
			bottom: 8px;
		}
	}

	:global(.content-wrapper) {
		overflow: hidden;
	}

	:global(.line-clamp-3) {
		-webkit-line-clamp: 3;
		line-clamp: 3;
	}

	.replies {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	:global(.content-wrapper.line-clamp-3) {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
		line-clamp: 3;
	}

	.backdrop-blur-md {
		backdrop-filter: blur(8px);
		-webkit-backdrop-filter: blur(8px);
	}

	.backdrop-blur-md:hover {
		backdrop-filter: blur(6px);
		-webkit-backdrop-filter: blur(6px);
	}
</style>
