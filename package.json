{"name": "shadcn-poc", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:prod": "vite build && node scripts/upload-sourcemaps.js", "upload-sourcemaps": "node scripts/upload-sourcemaps.js", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@internationalized/date": "^3.7.0", "@posthog/cli": "^0.3.1", "@skeletonlabs/skeleton": "^2.10.2", "@skeletonlabs/tw-plugin": "^0.4.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.13", "@types/eslint": "^8.56.7", "bits-ui": "^0.22.0", "cmdk-sv": "^0.0.18", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "lucide-svelte": "^0.475.0", "mode-watcher": "^0.5.1", "postcss": "^8.4.38", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "svelte-sonner": "^0.3.28", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "type": "module", "dependencies": {"@anthropic-ai/sdk": "^0.36.3", "@ethercorps/sveltekit-og": "^3.0.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@resvg/resvg-js": "^2.6.2", "@sentry/sveltekit": "^9.5.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@sveltejs/adapter-node": "^5.2.12", "ably": "^2.6.2", "axios": "^1.7.9", "cheerio": "^1.0.0", "clsx": "^2.1.1", "colorthief": "^2.4.0", "css-tree": "^3.1.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "fast-xml-parser": "^5.2.1", "html2canvas": "^1.4.1", "https-proxy-agent": "^7.0.6", "isomorphic-dompurify": "^2.21.0", "isomorphic-fetch": "^3.0.0", "node-fetch": "^2.7.0", "posthog-js": "^1.224.1", "random-useragent": "^0.5.0", "svelte-infinite-loading": "^1.4.0", "tailwind-merge": "^2.4.0", "tailwind-variants": "^0.2.1", "tinycolor2": "^1.6.0"}}