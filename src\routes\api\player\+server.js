import { json } from '@sveltejs/kit';
import { <PERSON><PERSON><PERSON> } from 'jsdom';
import randomUseragent from 'random-useragent';

const VK_URL = "https://vk.com";

/**
 * Logger utility for the player API
 */
const logger = {
  info: (message, data = {}) => {
    console.log(`[PLAYER API] [INFO] ${message}`, data);
  },
  warn: (message, data = {}) => {
    console.warn(`[PLAYER API] [WARN] ${message}`, data);
  },
  error: (message, error, data = {}) => {
    console.error(`[PLAYER API] [ERROR] ${message}`, error, data);
  },
  debug: (message, data = {}) => {
    console.debug(`[PLAYER API] [DEBUG] ${message}`, data);
  }
};

/**
 * Get a random user agent using the random-useragent library
 * @param {string} filter - Optional filter for user agent type
 * @returns {string} A random user agent string
 */
function getRandomUserAgent(filter = null) {
  try {
    // Get a random user agent with optional filter
    if (filter) {
      return randomUseragent.getRandom(ua => ua.browserName === filter);
    }
    return randomUseragent.getRandom();
  } catch (error) {
    logger.error('Error generating random user agent', error);
    // Fallback to a common user agent if the library fails
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }
}

/**
 * Extract video qualities from script content containing playerParams.
 * Uses regex to find "url<quality>" patterns.
 * @param {string} data - The script content
 * @returns {Array} Array of objects with quality (string) and URL (unescaped)
 */
function extractQualitiesFromScript(data) {
  // Regex to find the relevant playerParams assignment
  const playerParamsMatch = data.match(/var playerParams\s*=\s*({.*?});/s);
  if (!playerParamsMatch || !playerParamsMatch[1]) {
    logger.warn('Could not find playerParams object definition in script.');
    return [];
  }
  const paramsString = playerParamsMatch[1];

  // Regex to find "url<digits>" patterns within the paramsString
  // Example: "url1080":"https:\/\/..."
  const urlPattern = /"url(\d+)"\s*:\s*"([^"]+)"/g;
  const qualities = [];
  let match;

  while ((match = urlPattern.exec(paramsString)) !== null) {
    const [, quality, streamUrl] = match;
    // Check if it's a valid numeric quality
    if (!isNaN(parseInt(quality))) {
      qualities.push({
        quality: quality, // Keep as string '1080', '720' etc.
        url: streamUrl.replace(/\\\//g, "/") // Replace escaped slashes
      });
    }
  }

  if (qualities.length === 0) {
     logger.warn('No "url<digits>" patterns found within playerParams.', { paramsPreview: paramsString.substring(0, 200) });
  } else {
     logger.debug('Extracted qualities from playerParams script', { count: qualities.length });
  }

  return qualities;
}


/**
 * Extract the 1080p video URL if available, otherwise the highest quality video URL from VK HTML content.
 * @param {string} htmlContent - The HTML content from VK player page
 * @returns {Object} Object containing the URL and quality string (e.g., '1080') of the video
 */
function extractVideoInfo(htmlContent) {
  const dom = new JSDOM(htmlContent);
  const document = dom.window.document;

  const scripts = document.querySelectorAll('script');

  for (const script of scripts) {
    // Look for the script containing 'var playerParams =' which holds the URLs
    if (script.textContent && script.textContent.includes('var playerParams =')) {
      const qualities = extractQualitiesFromScript(script.textContent);

      if (qualities && qualities.length > 0) {
        // --- MODIFIED LOGIC ---
        // 1. Prioritize 1080p
        const quality1080 = qualities.find(q => q.quality === '1080');
        if (quality1080) {
          logger.debug('Found preferred 1080p quality.', { urlPreview: quality1080.url.substring(0, 50) + '...' });
          return {
            url: quality1080.url,
            quality: quality1080.quality // Return '1080'
          };
        }

        // 2. Fallback: If 1080p not found, sort by quality (descending) and get the highest available
        logger.warn('1080p quality not found, falling back to highest available quality.');
        const highestQuality = qualities.sort((a, b) =>
          parseInt(b.quality) - parseInt(a.quality) // Sort numerically descending
        )[0];

        logger.debug('Highest available quality found.', { quality: highestQuality.quality, urlPreview: highestQuality.url.substring(0, 50) + '...' });
        return {
          url: highestQuality.url,
          quality: highestQuality.quality // Return the highest found (e.g., '720', '480')
        };
        // --- END OF MODIFIED LOGIC ---
      } else {
         logger.warn('extractQualitiesFromScript returned empty array for script containing playerParams.');
      }
    }
  }
  logger.error('No video qualities found in any script tag containing playerParams.');
  return { url: null, quality: null };
}


/**
 * Get direct video URL from VK player
 * @param {string} url - The VK player URL
 * @param {Object} request - The original request object
 * @returns {Promise<Object>} Object with video URL, quality, and headers
 */
async function getVideoFromVkPlayer(url, request) {
  logger.info('Processing VK player request', { url });

  const referer = request.headers.get('Referer');
  let userAgent = request.headers.get('User-Agent');

  // Use a random Chrome user agent for better compatibility with VK
  if (!referer || !referer.includes('web.stremio.com')) {
    userAgent = getRandomUserAgent('Chrome');
    logger.debug('Using random Chrome user agent', { userAgent });
  } else {
    logger.debug('Using original user agent', { userAgent });
  }

  const requestHeaders = {
    "User-Agent": userAgent,
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
    // Adding common headers that might help bypass simple bot detection
    "Accept-Language": "en-US,en;q=0.9",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "Cache-Control": "max-age=0"
  };

  const videoHeaders = {
    request: {
      "User-Agent": userAgent,
      "Accept": "*/*",
      "Origin": VK_URL,
      "Referer": `${VK_URL}/`, // Use VK_URL as referer for video requests
    }
  };

  // Convert video_ext to video_embed if needed - Keeping original logic
  let processedUrl = url;
  // Note: The prompt example uses video_ext.php. Let's ensure the logic handles it.
  // The original code didn't actually replace video_ext, let's assume the URL is already correct or doesn't need changing.
  // If conversion *was* needed, it would be like:
  // if (url.includes("/video_ext.php")) {
  //   processedUrl = url.replace("/video_ext.php", "/video_embed.php");
  //   logger.debug('Converted video_ext to video_embed', { originalUrl: url, processedUrl });
  // }

  try {
    logger.info('Fetching VK page', { url: processedUrl });
    const startTime = Date.now();
    const response = await fetch(processedUrl, { headers: requestHeaders });
    const fetchTime = Date.now() - startTime;

    logger.debug('VK page fetch completed', {
      status: response.status,
      statusText: response.statusText,
      fetchTimeMs: fetchTime
    });

    if (!response.ok) {
       const errorText = await response.text().catch(() => 'Could not read error response body');
       logger.error('Failed to fetch VK page', { status: response.status, statusText: response.statusText, responseBodyPreview: errorText.substring(0, 500) });
       throw new Error(`Failed to fetch VK page: ${response.status} ${response.statusText}`);
    }

    const text = await response.text();
    logger.debug('VK page content received', {
      contentLength: text.length
    });
    // console.log("Full content:", text); // Keep commented out unless debugging specific HTML
    const extractStartTime = Date.now();
    // Use the updated extraction function
    const { url: videoUrl, quality } = extractVideoInfo(text);
    const extractTime = Date.now() - extractStartTime;

    logger.debug('Video URL extraction completed', {
      extractTimeMs: extractTime,
      found: !!videoUrl
    });

    if (!videoUrl) {
      logger.error('Could not extract video URL from VK player', {url: processedUrl, contentPreview: text.substring(0, 1000)});
      throw new Error('Could not extract video URL from VK player');
    }

    logger.info('Successfully extracted VK video URL', {
      quality: `${quality}p`, // Add 'p' suffix for display
      videoUrlPreview: videoUrl.substring(0, 50) + '...'
    });

    return {
      videoUrl,
      quality: `${quality}p`, // Add 'p' suffix
      headers: videoHeaders
    };
  } catch (error) {
    logger.error('Error fetching or processing video from VK', error, { url: processedUrl });
    // Re-throw the error so the caller (GET handler) can catch it
    throw error;
  }
}

/**
 * Handle GET requests to the player API
 */
export async function GET({ url, request }) {
  const requestId = Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
  const playerType = url.searchParams.get('player');
  const embedUrl = url.searchParams.get('url');

  logger.info('Received player API request', {
    requestId,
    playerType,
    embedUrl: embedUrl ? (embedUrl.length > 50 ? embedUrl.substring(0, 50) + '...' : embedUrl) : null,
    userAgent: request.headers.get('User-Agent'),
    referer: request.headers.get('Referer')
  });

  if (!playerType || !embedUrl) {
    logger.warn('Missing required parameters', { requestId, playerType, embedUrl });
    return json({
      error: 'Missing required parameters',
      message: 'Both player and url parameters are required',
      requestId
    }, { status: 400 });
  }

  const startTime = Date.now();

  try {
    // Special handling for VK player
    if (playerType.toLowerCase() === 'vk') {
      logger.info('Processing VK player request', { requestId });
      const { videoUrl, quality, headers } = await getVideoFromVkPlayer(embedUrl, request);

      const processingTime = Date.now() - startTime;
      logger.info('VK player request completed successfully', {
        requestId,
        processingTimeMs: processingTime,
        quality // quality already includes 'p'
      });

      return json({
        url: videoUrl,
        quality, // e.g., '1080p' or '720p'
        headers,
        player: playerType,
        originalUrl: embedUrl,
        requestId,
        processingTimeMs: processingTime
      });
    }

    // For other players, just return the URL as-is (Passthrough)
    logger.info(`Processing ${playerType} player request (passthrough)`, { requestId });
    const processingTime = Date.now() - startTime;

    logger.info(`${playerType} player request completed (passthrough)`, {
      requestId,
      processingTimeMs: processingTime
    });

    return json({
      url: embedUrl,
      player: playerType,
      originalUrl: embedUrl,
      requestId,
      processingTimeMs: processingTime
      // No quality or headers for passthrough
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    // Log the specific error message from the underlying function if available
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    logger.error(`Error processing ${playerType} player`, error, { // Log the full error object
      requestId,
      processingTimeMs: processingTime,
      embedUrl,
      errorMessage // Also log just the message for easier reading
    });

    return json({
      error: 'Failed to process player',
      message: errorMessage, // Provide the specific error message
      player: playerType,
      originalUrl: embedUrl,
      requestId,
      processingTimeMs: processingTime
    }, { status: 500 });
  }
}