<script>
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { Play, Calendar, Tv, Film, Clapperboard } from 'lucide-svelte';
	import { generateAnimeUrl, getCachedColor, setCachedColor } from '$lib/myUtils';
	import tinycolor from 'tinycolor2';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { getPreferredTitle } from '$lib/utils/titleHelper';
	import { formatDistanceToNow, getHours, setHours } from 'date-fns';
	import { pl } from 'date-fns/locale';

	export let episode;
	export let lazyLoad = false;
	export let isVertical;
	export let isDragging = false;
	export let preferRomaji;
	export let translatingGroups = [];

	// Polish declination for group count
	function getPolishGroupText(count) {
		if (count === 1) return '1 Grupa';
		if (count >= 2 && count <= 4) return `${count} Grupy`;
		return `${count} Grup`;
	}

	let videoRef;
	let isHovered = false;
	let titleColor = '#ffffff';
	let imgSrc = lazyLoad ? '' : episode.image;

	// Get translating group info
	function getGroupInfo(groupName) {
		if (!groupName || groupName === 'lycoris_cafe') {
			return {
				name: 'lycoris.cafe',
				logo_url: 'https://lycoris.cafe/logo.png',
				isNative: true
			};
		}

		const group = translatingGroups.find(g => g.name === groupName);
		return {
			name: groupName,
			logo_url: group?.logo_url || null,
			isNative: false
		};
	}

	$: groupInfo = getGroupInfo(episode.translating_group);

	// Add image loading state
	let imageLoaded = false;

	// Modify handleImageLoad function
	function handleImageLoad() {
		imageLoaded = true;
	}

	function isMobileDevice() {
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			// let adjustedColor = color.saturate(10);
			let adjustedColor = color;
			while (adjustedColor.getBrightness() < 190) {
				adjustedColor = adjustedColor.lighten(10);
			}
			adjustedColor = adjustedColor.saturate(100);
			// while (adjustedColor.getBrightness() > 180) {
			// 	adjustedColor = adjustedColor.darken(10);
			// }

			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			titleColor = cachedColor;
			return;
		}

		if (isMobileDevice()) {
			titleColor = '#ffffff';
			return;
		}

		const color = await extractDominantColor(imgSrc);
		titleColor = color;
		setCachedColor(imgSrc, color);
	}

	function updateCardWidth(isVertical) {
		const root = document.documentElement;
		const width = isVertical ? '165px' : '190px';
		root.style.setProperty('--card-width-vertical', width);
	}

	$: if (isHovered && videoRef && !isMobileDevice()) {
		videoRef.currentTime = 30;
		videoRef.play().catch(console.error);
	} else if (!isHovered && videoRef) {
		videoRef.pause();
	}

	$: if (isVertical !== undefined) {
		updateCardWidth(isVertical);
	}

	function handleKeyDown(event) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			window.location.href = `${generateAnimeUrl(episode)}/watch/${episode.episode}`;
		}
	}

	function handleDragStart(e) {
		e.preventDefault();
	}

	onMount(async () => {
		if (videoRef && !isMobileDevice()) {
			videoRef.addEventListener('ended', () => {
				if (isHovered) {
					videoRef.currentTime = 30;
					videoRef.play().catch(console.error);
				}
			});
		}

		if (browser) {
			await getDominantColor(episode.image);
		}
		updateCardWidth(isVertical);
	});
</script>

<a
	href={`${generateAnimeUrl(episode)}/watch/${episode.episode}`}
	class="relative block overflow-visible transition-all duration-300 ease-in-out bg-gray-900 shadow-lg cursor-pointer card-container group hover:z-20 md:hover:scale-105"
	on:mouseenter={() => (isHovered = true)}
	on:mouseleave={() => (isHovered = false)}
	on:click={(e) => {
		if (isDragging) {
			e.preventDefault();
		}
	}}
	on:keydown={handleKeyDown}
	on:dragstart={handleDragStart}
	aria-label="Oglądaj {episode.title} Odcinek {episode.episode}"
>
	<div class="relative w-full" style="aspect-ratio: 12 / 5;">
		{#if !imageLoaded}
			<Skeleton class="absolute inset-0 w-full h-full rounded-t-lg" />
		{/if}
		<img
			src={imgSrc}
			data-src={lazyLoad ? episode.image : null}
			alt="Okładka {episode.title}"
			class="absolute top-0 left-0 object-cover w-full h-full transition-opacity duration-300 rounded-t-lg block-interaction"
			style="opacity: {imageLoaded ? 1 : 0};"
			loading={lazyLoad ? 'lazy' : 'eager'}
			on:load={handleImageLoad}
		/>
		<div class="absolute inset-0 bg-linear-to-t from-gray-900/20 to-transparent"></div>

		<div class="absolute inset-0 flex items-start justify-between p-2 transition-opacity duration-300" style="opacity: {imageLoaded ? 1 : 0}">
			<!-- Translating Group Badge - Left Side -->
			<div class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-800/80 text-white">
				{#if groupInfo.logo_url}
					<img src={groupInfo.logo_url} alt="{groupInfo.name} logo" class="w-3 h-3 rounded-full object-cover" />
				{:else}
					<div class="w-3 h-3 rounded-full bg-gray-600 flex items-center justify-center text-[8px] font-bold text-white">
						{groupInfo.name.charAt(0).toUpperCase()}
					</div>
				{/if}
				<span class="hidden sm:inline">{groupInfo.name}</span>
			</div>

			<!-- Date - Right Side -->
			<span class="px-2 py-1 text-xs font-bold transition-colors duration-300 rounded-sm time-ago bg-gray-800/80" style="color: {isHovered ? titleColor : '#ffffff'}">
				{#if episode.duration && !isNaN(new Date(episode.duration).getTime())}
					{formatDistanceToNow(new Date(new Date(episode.duration).setHours(new Date(episode.duration).getHours() + 2)), { addSuffix: true, locale: pl })}
				{:else}
					Niedawno
				{/if}
			</span>
		</div>
	</div>

	<div class="px-2 py-3 space-y-1 rounded-b-lg fade-in">
		<h3 class="text-sm font-bold transition-colors duration-300 line-clamp-1" style="color: {isHovered ? titleColor : '#ffffff'};">
			{getPreferredTitle(episode, preferRomaji)}
		</h3>
		<p class="hidden text-xs text-gray-400 line-clamp-1 md:flex">{getPreferredTitle(episode, !preferRomaji)}</p>
		<div class="flex items-center justify-between text-xs text-gray-400">
			<div class="flex items-center">
				<Film size={12} class="mr-1" aria-label="Numer odcinka" />
				<span>Odcinek {episode.episode}</span>
			</div>
			<div class="flex items-center">
				<Clapperboard size={12} class="mr-1" aria-label="Całkowita liczba odcinków" />
				<span>/{episode.total_episodes ? episode.total_episodes : '?'}</span>
			</div>
		</div>
		<div class="items-center justify-between hidden text-xs text-gray-500 md:flex">
			<div class="flex items-center">
				<Tv size={12} class="mr-1" aria-label="Typ anime" />
				<span>{episode.type}</span>
			</div>
			<div class="flex items-center">
				<Calendar size={12} class="mr-1" aria-label="Liczba grup" />
				<span>{episode.isStack ? getPolishGroupText(episode.stackedEpisodes.length) : '1 Grupa'}</span>
			</div>
		</div>
	</div>
</a>

<style>
	:root {
		--card-width-base: 190px;
		--card-width-vertical: 190px;
	}

	.block-interaction {
		pointer-events: none;
	}

	.card-container {
		width: var(--card-width-vertical);
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
	}

	.time-ago {
		transition: all 300ms ease-in-out;
	}



	@media (min-width: 450px) {
		.card-container {
			--card-width-base: 230px;
			--card-width-vertical: 230px;
		}
	}

	@media (min-width: 530px) {
		.card-container {
			--card-width-base: 240px;
			--card-width-vertical: 240px;
		}
	}

	@media (min-width: 640px) {
		.card-container {
			--card-width-base: 240px;
			--card-width-vertical: 240px;
		}
	}

	@media (min-width: 1024px) {
		.card-container {
			--card-width-base: 300px;
			--card-width-vertical: 300px;
		}
	}

	@media (min-width: 1537px) {
		.card-container {
			--card-width-base: 300px;
			--card-width-vertical: 300px;
		}
	}

	.fade-in {
		animation: fadeIn 300ms ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
</style>
