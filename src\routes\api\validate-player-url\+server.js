import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request }) {
	try {
		const { url } = await request.json();

		if (!url) {
			return json({ error: 'URL is required' }, { status: 400 });
		}

		// Fetch all player sources from the database
		const { data: playerSources, error } = await supabase
			.from('player_sources')
			.select('*');

		if (error) {
			console.error('Error fetching player sources:', error);
			return json({ error: 'Failed to fetch player sources' }, { status: 500 });
		}

		// Check if the URL matches any of the regex patterns
		for (const playerSource of playerSources) {
			try {
				const regex = new RegExp(playerSource.regex, 'i');
				if (regex.test(url)) {
					return json({
						isValid: true,
						playerSource: playerSource.name
					});
				}
			} catch (regexError) {
				console.error(`Invalid regex for player source ${playerSource.name}:`, regexError);
				continue;
			}
		}

		// If no match found, return invalid
		return json({
			isValid: false,
			playerSource: null
		});

	} catch (error) {
		console.error('Error validating player URL:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
}
