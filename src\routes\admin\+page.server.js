// src/routes/admin/+page.server.js
import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminByRole } from '$lib/utils/roleUtils';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function load({ locals }) {
  const { session, user } = await locals.safeGetSession();

  // Check if user is authenticated
  if (!session || !user) {
    throw error(401, 'Unauthorized');
  }

  // Check if user is an admin
  const hasAdminRole = await isAdminByRole(user.id);

  if (!hasAdminRole) {
    throw error(403, 'Forbidden');
  }

  // Get recent comments
  const { data: recentComments, error: recentError } = await supabase
    .from('comments')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(50);

  if (recentError) throw error(500, 'Failed to fetch recent comments');

  // Get spam queue
  const { data: spamQueue, error: spamError } = await supabase
    .from('comments')
    .select('*')
    .eq('is_spam', true)
    .eq('is_pending_review', true)
    .order('created_at', { ascending: false });

  if (spamError) throw error(500, 'Failed to fetch spam queue');

  // Get report queue with report count
  const { data: reportData, error: reportError } = await supabase
    .from('comment_reports')
    .select(`
      *,
      comment:comments(*)
    `)
    .eq('status', 'pending')
    .order('created_at', { ascending: false });

  if (reportError) throw error(500, 'Failed to fetch report queue');

  // Group reports by comment and count them
  const reportQueue = reportData.reduce((acc, report) => {
    const existingReport = acc.find(r => r.comment.id === report.comment.id);
    if (existingReport) {
      existingReport.reportCount++;
      existingReport.reasons.push(report.reason);
    } else {
      acc.push({
        ...report,
        reportCount: 1,
        reasons: [report.reason]
      });
    }
    return acc;
  }, []);

  return {
    recentComments,
    spamQueue,
    reportQueue
  };
}