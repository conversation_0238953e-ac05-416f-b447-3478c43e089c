<script>
	//src/routes/anime/[animeId]/[animeTitle]/watch/[episode]/+page.svelte
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import Player from '$lib/components/anime/Player.svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { Button } from '$lib/components/ui/button';
	import { X } from 'lucide-svelte';
	import { browser } from '$app/environment';
	import { getCachedColor, setCachedColor } from '$lib/myUtils';
	import tinycolor from 'tinycolor2';

	export let data;
	let { anime, currentEpisode, nextEpisodeData } = data;
	// Update data when it changes
	$: ({ anime, currentEpisode, nextEpisodeData } = data);

	const MIN_BRIGHTNESS = 140;
	const MAX_BRIGHTNESS = 180;

	let dominantColor;
	let animeId = $page.params.animeId;
	let selectedEpisode = parseInt($page.params.episode);
	let isLoading = true;
	let showErrorDialog = false;
	let currentError = null;

	// Handle route changes
	$: {
		animeId = $page.params.animeId;
		selectedEpisode = parseInt($page.params.episode);
	}

	// Metadata
	$: ogImageUrl = getOGImage();
	$: title = anime ? `${anime.englishTitle} Odcinek ${selectedEpisode} - Lycoris` : 'Oglądaj Anime - Lycoris';
	$: description = anime ? `Obejrzyj odcinek ${selectedEpisode} anime ${anime.title} na Lycoris.` : 'Oglądaj swoje ulubione anime online za darmo na Lycoris.';
	$: canonicalUrl = `https://lycoris.cafe/anime/${animeId}/watch/${selectedEpisode}`;

	function getOGImage() {
		if (!anime || !currentEpisode) return null;

		const params = new URLSearchParams({
			id: anime.id,
			episode: selectedEpisode
		});

		const cacheBuster = Date.now();
		params.append('_v', cacheBuster);

		return `https://www.lycoris.cafe/api/og2?${params.toString()}`;
	}

	onMount(async () => {
		let backgroundImage = anime?.background || (anime?.episodes?.length > 0 ? anime.episodes[anime.episodes.length - 1].thumbnail : null);
		await getDominantColor(anime.poster);
		isLoading = false;
	});

	function closePlayer() {
		const currentPath = $page.url.pathname;
		const newPath = currentPath.replace(/\/watch\/\d+/, '');
		goto(newPath, { replaceState: true });
	}

	function handlePlayerError(error) {
		currentError = error;
		showErrorDialog = true;
	}

	async function handleEpisodeEnd() {
		if (selectedEpisode < anime.totalEpisodes) {
			const nextEpNumber = selectedEpisode + 1;
			const baseUrl = $page.url.pathname.replace(/\/\d+$/, '');
			await goto(`${baseUrl}/${nextEpNumber}`, {
				noScroll: true,
				replaceState: true,
				invalidateAll: true
			});
		}
	}

	async function changeEpisode(episodeNumber) {
		const baseUrl = $page.url.pathname.replace(/\/\d+$/, '');
		await goto(`${baseUrl}/${episodeNumber}`, {
			noScroll: true,
			invalidateAll: true
		});
	}

	function isMobileDevice() {
		if (!browser) return false;
		let isMobile = window.innerWidth < 640;
		if (isMobile) return true;
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			let adjustedColor = color;

			// Ensure brightness is between 140-180
			while (adjustedColor.getBrightness() < 140) {
				adjustedColor = adjustedColor.lighten(5);
			}
			while (adjustedColor.getBrightness() > 180) {
				adjustedColor = adjustedColor.darken(5);
			}

			// Increase saturation
			adjustedColor = adjustedColor.saturate(40);
			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			dominantColor = cachedColor;
			return;
		}

		if (isMobileDevice()) {
			dominantColor = '#ffffff';
			return;
		}

		dominantColor = await extractDominantColor(imgSrc);
		setCachedColor(imgSrc, dominantColor);
	}
</script>

<svelte:head>
	<title>{title}</title>
	<meta name="description" content={description} />

	<meta name="twitter:title" content={title.length > 50 ? title.substring(0, 50) + '...' : title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={ogImageUrl} />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content={`Odcinek ${selectedEpisode} z ${anime?.title || 'anime'}`} />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="video.episode" />
	<meta property="og:site_name" content="Lycoris" />

	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content={title.length > 50 ? title.substring(0, 50) + '...' : title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={ogImageUrl} />
	<meta name="twitter:image:alt" content={`Odcinek ${selectedEpisode} z ${anime?.title || 'anime'}`} />

	<meta property="video:duration" content={currentEpisode.duration || anime.duration || 0} />
	<meta property="video:release_date" content={currentEpisode.airDate} />
	<meta property="video:series" content={anime.title} />
	<meta property="video:tag" content={anime.genres.join(',')} />
	<meta property="video:actor" content={anime.studio} />

	<meta property="og:video:episode" content={selectedEpisode.toString()} />
	<meta property="og:video:episode_name" content={currentEpisode.title || `Odcinek ${selectedEpisode}`} />

	<link rel="canonical" href={canonicalUrl} />

	<meta name="keywords" content={`anime, ${anime?.title || ''}, odcinek ${selectedEpisode}, ${anime?.genres?.join(', ') || ''}, Lycoris`} />
	<meta name="theme-color" content="#ee8585" />
	<meta name="author" content={anime?.studio || 'Lycoris'} />
	<meta name="robots" content="index, follow" />
</svelte:head>

{#if anime}
	<div class:opacity-50={isLoading} class="relative" in:fade={{ duration: 150, delay: 150 }} out:fade={{ duration: 150 }}>
		<div class="fixed top-2 right-4">
			<Button class="p-2 text-white bg-transparent rounded-full hover:bg-gray-700 hover:opacity-50" on:click={closePlayer}>
				<X class="w-6 h-6" />
			</Button>
		</div>

		{#if isLoading}
			<div class="fixed inset-0 z-50 flex flex-col">
				<div class="flex-1 overflow-y-auto bg-black opacity-75 backdrop-blur-xs">
					<div class="w-full min-h-screen bg-gray-900 shadow-lg">
						<div class="flex flex-col lg:flex-row">
							<div class="w-full lg:w-3/4">
								<div class="relative h-[80vh] w-full">
									<div class="absolute z-10 top-4 left-4">
										<Skeleton class="w-64 h-8" />
									</div>

									<div class="flex items-center justify-center h-full bg-black">
										<Skeleton class="w-full h-full" />
									</div>

									<div class="absolute -translate-y-1/2 top-1/2 left-4">
										<Skeleton class="w-10 h-10 rounded-full" />
									</div>
									<div class="absolute -translate-y-1/2 top-1/2 right-4">
										<Skeleton class="w-10 h-10 rounded-full" />
									</div>
								</div>

								<div class="p-4 bg-gray-800 lg:hidden">
									<Skeleton class="w-24 h-6 mb-4" />
									<div class="space-y-2">
										{#each Array(5) as _}
											<div class="flex items-center p-2 space-x-2 rounded-md">
												<Skeleton class="w-24 h-16 rounded" />
												<div class="flex-1">
													<Skeleton class="w-3/4 h-4 mb-1" />
													<Skeleton class="w-24 h-3 mb-2" />
													<Skeleton class="w-full h-1 rounded-full" />
												</div>
											</div>
										{/each}
									</div>
								</div>
							</div>

							<div class="hidden w-full p-4 bg-gray-800 hide-scrollbar lg:block lg:h-full lg:w-1/4">
								<Skeleton class="w-24 h-6 mb-4" />
								<div class="space-y-2">
									{#each Array(12) as _}
										<div class="flex items-center p-2 space-x-2 rounded-md">
											<Skeleton class="w-24 h-16 rounded" />
											<div class="flex-1">
												<Skeleton class="w-3/4 h-4 mb-1" />
												<Skeleton class="w-24 h-3 mb-2" />
												<Skeleton class="w-full h-1 rounded-full" />
											</div>
										</div>
									{/each}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		{/if}

		{#if currentEpisode}
			{#key selectedEpisode}
				{#if dominantColor !== undefined}
					<div class:pointer-events-none={isLoading}>
						<!-- {console.log(anime.episodes)} -->
						<Player
							{anime}
							episode={currentEpisode}
							allEpisodes={anime.episodes}
							{nextEpisodeData}
							{dominantColor}
							on:changeEpisode={(e) => changeEpisode(e.detail)}
							on:close={closePlayer}
							on:error={handlePlayerError}
							on:episodeEnd={handleEpisodeEnd}
						/>
					</div>
				{/if}
			{/key}
		{/if}
	</div>
{:else}
	<div class="flex items-center justify-center h-screen" aria-label="Loading anime details" />
{/if}

<style>
	:global(.hide-scrollbar::-webkit-scrollbar) {
		display: none;
	}

	:global(.hide-scrollbar) {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
</style>
