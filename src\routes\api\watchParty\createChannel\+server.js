import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET(params) {
    let key = params.url.searchParams.get('key');
    if (key !== '719a15b1-9716-48a7-ab4a-074322a9c935') {
        return;
    }
    let roomCode = params.url.searchParams.get('roomCode');
    let animeTitle = params.url.searchParams.get('title')
    let animeEpisode = params.url.searchParams.get('episode')
    const metadata = await createChannel(roomCode, animeTitle, animeEpisode);
    return new Response(JSON.stringify(metadata));
}

async function createChannel(roomCode, animeTitle, animeEpisode) {
    const { data, error } = await supabase
        .from('rooms')
        .insert([
            { room_code: roomCode, anime_title: animeTitle, anime_episode: animeEpisode, users: { members: ['updating...'] } },
        ])
    return;
}