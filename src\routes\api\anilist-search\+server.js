import { error } from '@sveltejs/kit';

/**
 * DEPRECATED: This server-side API is deprecated and will be removed in a future version.
 * Please use the client-side API function from '$lib/utils/api-clients.js' instead.
 *
 * Search for anime on AniList
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Search results array
 */
async function searchAniList(query) {
  try {
    const response = await fetch('https://graphql.anilist.co', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query ($search: String) {
            Page(page: 1, perPage: 10) {
              media(search: $search, type: ANIME) {
                id
                idMal
                title {
                  romaji
                  english
                  native
                }
                type
                episodes
                coverImage {
                  medium
                }
                siteUrl
                synonyms
              }
            }
          }
        `,
        variables: {
          search: query
        }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.errors) {
      throw new Error(data.errors[0].message);
    }

    return data.data.Page.media;
  } catch (error) {
    console.error('Error searching AniList:', error);
    throw error;
  }
}

export async function POST({ request }) {
  try {
    console.warn('DEPRECATED: The server-side AniList search API is deprecated. Please use the client-side API function from "$lib/utils/api-clients.js" instead.');

    const data = await request.json();
    const { title } = data;

    if (!title) {
      throw error(400, 'Missing title parameter');
    }

    const result = await searchAniList(title);

    return new Response(JSON.stringify(result), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=3600' // Cache for 1 hour
      }
    });
  } catch (err) {
    console.error('Error in AniList search API:', err);
    throw error(500, err.message || 'Failed to search AniList');
  }
}
