<script>
	import { createEventDispatcher } from 'svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Accordion from '$lib/components/ui/accordion';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { toast } from 'svelte-sonner';
	import { Search, RefreshCw, Check, X } from 'lucide-svelte';
	import { searchJikan, searchAniList } from '$lib/utils/api-clients';

	export let open = false;
	export let multipleMatches = [];
	export let failedMatches = [];
	export let onSave = () => {};

	const dispatch = createEventDispatcher();

	let selectedMatches = {};
	let searchTerm = '';
	let filteredMultipleMatches = [];
	let filteredFailedMatches = [];
	let isLoading = false;
	let anilistResults = {};
	let anilistLoading = {};
	let jikanResults = {};
	let jikanLoading = {};
	let searchErrors = {};

	// Track which result index is currently shown for each anime
	let currentResultIndex = {};
	// Track all results for each anime
	let allSearchResults = {};
	// Track manual input mode and values
	let manualInputMode = {};
	let manualInputValues = {};
	// Track custom search inputs
	let customSearchInputs = {};
	let customSearchLoading = {};
	// Track when an item is selected from search results
	let searchMatched = {};

	$: {
		if (searchTerm) {
			const term = searchTerm.toLowerCase();
			filteredMultipleMatches = multipleMatches.filter((match) => match.title.toLowerCase().includes(term));
			filteredFailedMatches = failedMatches.filter((match) => match.title.toLowerCase().includes(term));
		} else {
			filteredMultipleMatches = multipleMatches;
			filteredFailedMatches = failedMatches;
		}
	}

	// Initialize custom search inputs with anime titles
	$: {
		if (multipleMatches && multipleMatches.length > 0) {
			multipleMatches.forEach((match) => {
				if (!customSearchInputs[match.title]) {
					customSearchInputs[match.title] = match.title;
				}
			});
		}

		if (failedMatches && failedMatches.length > 0) {
			failedMatches.forEach((match) => {
				if (!customSearchInputs[match.title]) {
					customSearchInputs[match.title] = match.title;
				}
			});
		}
	}

	function closeModal() {
		open = false;
		selectedMatches = {};
		searchTerm = '';
		anilistResults = {};
		anilistLoading = {};
		jikanResults = {};
		jikanLoading = {};
		searchErrors = {};
		currentResultIndex = {};
		allSearchResults = {};
		manualInputMode = {};
		manualInputValues = {};
		customSearchInputs = {};
		customSearchLoading = {};
		searchMatched = {};
		dispatch('close');
	}

	function handleSelectMatch(animeTitle, matchIndex) {
		selectedMatches[animeTitle] = matchIndex;
		// Clear any search match status when selecting from radio buttons
		delete searchMatched[animeTitle];
		searchMatched = { ...searchMatched }; // Trigger reactivity
	}

	// Function to get the next result based on the current index
	function getNextResult(animeTitle) {
		if (!allSearchResults[animeTitle]) return null;

		// Initialize if not set
		if (currentResultIndex[animeTitle] === undefined) {
			currentResultIndex[animeTitle] = 0;
		}

		// Increment the index
		currentResultIndex[animeTitle]++;

		// Get the current index
		const index = currentResultIndex[animeTitle];

		// Determine which source to use based on alternating pattern
		// We'll alternate between MAL and AniList, with 3 MAL results first
		let source, resultIndex;

		// For odd indices (1, 3, 5, 7...) use MAL
		// For even indices (2, 4, 6, 8...) use AniList
		// But start with 3 MAL results (0, 1, 2)
		if (index < 3) {
			// First 3 results from MAL (indices 0, 1, 2)
			source = 'mal';
			resultIndex = index;
		} else {
			// After the first 3 MAL results, alternate between AniList and MAL
			const isEven = (index - 3) % 2 === 0;
			source = isEven ? 'anilist' : 'mal';

			// Calculate the result index within each source
			if (source === 'mal') {
				// For MAL: after the first 3, we want indices 3, 5, 7, 9...
				resultIndex = Math.floor((index - 3) / 2) + 3;
			} else {
				// For AniList: we want indices 0, 1, 2, 3...
				resultIndex = Math.floor((index - 3) / 2);
			}
		}

		// Check if we have results for this source and if the index is valid
		if (!allSearchResults[animeTitle][source] || !allSearchResults[animeTitle][source][resultIndex]) {
			// If we've run out of results for one source, try the other
			const otherSource = source === 'mal' ? 'anilist' : 'mal';
			const otherIndex = source === 'mal' ? Math.floor((index - 3) / 2) : Math.floor((index - 3) / 2) + 3;

			if (allSearchResults[animeTitle][otherSource] && allSearchResults[animeTitle][otherSource][otherIndex]) {
				source = otherSource;
				resultIndex = otherIndex;
			} else {
				// We've exhausted all results
				return null;
			}
		}

		// Return the result with source info and index information for display
		return {
			result: allSearchResults[animeTitle][source][resultIndex],
			source: source,
			sourceIndex: resultIndex + 1, // 1-based for display
			totalResults: {
				mal: allSearchResults[animeTitle].mal ? allSearchResults[animeTitle].mal.length : 0,
				anilist: allSearchResults[animeTitle].anilist ? allSearchResults[animeTitle].anilist.length : 0
			},
			overallIndex: index + 1 // 1-based for display
		};
	}

	// Main search function - now starts with MyAnimeList and shows multiple results
	async function searchAnime(animeTitle) {
		// Initialize tracking for this anime
		currentResultIndex[animeTitle] = -1; // Start at -1 so first call to getNextResult gives index 0
		allSearchResults[animeTitle] = {
			mal: [],
			anilist: [],
			currentMalIndex: 0,
			currentAnilistIndex: 0,
			totalResults: 0,
			showAllResults: true // New flag to show all results at once
		};

		// Reset any previous search errors and manual input mode
		delete searchErrors[animeTitle];
		searchErrors = { ...searchErrors };
		delete manualInputMode[animeTitle];

		// Clear previous results
		delete jikanResults[animeTitle];
		delete anilistResults[animeTitle];
		jikanResults = { ...jikanResults };
		anilistResults = { ...anilistResults };

		// Start with MyAnimeList search
		await fetchMalResults(animeTitle);

		// Also fetch AniList results
		await fetchAniListResults(animeTitle);

		// Update total results count
		allSearchResults[animeTitle].totalResults = allSearchResults[animeTitle].mal.length + allSearchResults[animeTitle].anilist.length;

		if (allSearchResults[animeTitle].totalResults > 0) {
			// We have results - show them all
			// We'll handle the display in the UI with the showAllResults flag

			// Store the first result from each source for backward compatibility
			if (allSearchResults[animeTitle].mal.length > 0) {
				jikanResults[animeTitle] = allSearchResults[animeTitle].mal[0];
				jikanResults = { ...jikanResults };
			}

			if (allSearchResults[animeTitle].anilist.length > 0) {
				anilistResults[animeTitle] = allSearchResults[animeTitle].anilist[0];
				anilistResults = { ...anilistResults };
			}
		} else {
			enableManualInput(animeTitle);
		}
	}

	// Function to fetch results from MyAnimeList
	async function fetchMalResults(animeTitle, customSearchTerm = null) {
		if (jikanLoading[animeTitle]) return;

		try {
			jikanLoading[animeTitle] = true;
			jikanLoading = { ...jikanLoading }; // Trigger reactivity

			const searchTerm = customSearchTerm || animeTitle;

			// Call the client-side API function directly
			const results = await searchJikan(searchTerm);

			if (results && Array.isArray(results)) {
				allSearchResults[animeTitle].mal = results;
				allSearchResults[animeTitle].totalResults = (allSearchResults[animeTitle].mal?.length || 0) + (allSearchResults[animeTitle].anilist?.length || 0);
			}
		} catch (err) {
			console.error('Error searching MyAnimeList:', err);
		} finally {
			jikanLoading[animeTitle] = false;
			jikanLoading = { ...jikanLoading }; // Trigger reactivity
		}
	}

	// Function to fetch results from AniList
	async function fetchAniListResults(animeTitle, customSearchTerm = null) {
		if (anilistLoading[animeTitle]) return;

		try {
			anilistLoading[animeTitle] = true;
			anilistLoading = { ...anilistLoading }; // Trigger reactivity

			const searchTerm = customSearchTerm || animeTitle;

			// Call the client-side API function directly
			const results = await searchAniList(searchTerm);

			if (results && Array.isArray(results)) {
				allSearchResults[animeTitle].anilist = results;
				allSearchResults[animeTitle].totalResults = (allSearchResults[animeTitle].mal?.length || 0) + (allSearchResults[animeTitle].anilist?.length || 0);
			}
		} catch (err) {
			console.error('Error searching AniList:', err);
		} finally {
			anilistLoading[animeTitle] = false;
			anilistLoading = { ...anilistLoading }; // Trigger reactivity
		}
	}

	// Function to enable manual input mode
	function enableManualInput(animeTitle) {
		manualInputMode[animeTitle] = true;
		manualInputValues[animeTitle] = {
			title: '',
			type: 'TV',
			episodes: '',
			malId: ''
		};
	}

	// Function to handle manual input submission
	function handleManualSubmit(failedMatch) {
		const animeTitle = failedMatch.title;
		const manualData = manualInputValues[animeTitle];

		if (!manualData.title) {
			toast.error('Proszę podać tytuł anime');
			return;
		}

		// Create a match object similar to what we'd get from the API
		const match = {
			shindenAnime: {
				title: failedMatch.title,
				watchStatus: failedMatch.rawWatchStatus,
				watchedEpisodesCnt: failedMatch.watchedEpisodes,
				rateTotal: failedMatch.score
			},
			dbAnime: {
				title: manualData.title,
				synonyms: [],
				sources: manualData.malId ? [`https://myanimelist.net/anime/${manualData.malId}`] : [],
				type: manualData.type || 'TV',
				episodes: parseInt(manualData.episodes) || 0,
				malId: parseInt(manualData.malId) || 0
			}
		};

		// Add to selected matches
		onSave([match]);

		// Remove from failed matches
		removeFromFailedMatches(failedMatch);

		// Reset manual input mode
		delete manualInputMode[animeTitle];
		delete manualInputValues[animeTitle];

		// toast.success('Anime zostało dodane do dopasowanych!');
	}

	// Function to handle custom search input
	async function handleCustomSearch(failedMatch) {
		const animeTitle = failedMatch.title;
		const customSearchTerm = customSearchInputs[animeTitle];

		if (!customSearchTerm || customSearchTerm.trim() === '') {
			toast.error('Proszę wprowadzić tekst do wyszukiwania');
			return;
		}

		// Set loading state
		customSearchLoading[animeTitle] = true;
		customSearchLoading = { ...customSearchLoading };

		try {
			// Initialize tracking for this anime if not already done
			if (!allSearchResults[animeTitle]) {
				allSearchResults[animeTitle] = {
					mal: [],
					anilist: [],
					currentMalIndex: 0,
					currentAnilistIndex: 0,
					totalResults: 0,
					showAllResults: true
				};
			} else {
				// Reset results but keep the structure
				allSearchResults[animeTitle].mal = [];
				allSearchResults[animeTitle].anilist = [];
				allSearchResults[animeTitle].totalResults = 0;
				allSearchResults[animeTitle].showAllResults = true;
			}

			// Clear previous errors
			delete searchErrors[animeTitle];
			searchErrors = { ...searchErrors };

			// Search both APIs with the custom term
			await Promise.all([fetchMalResults(animeTitle, customSearchTerm), fetchAniListResults(animeTitle, customSearchTerm)]);

			// Update total results count
			allSearchResults[animeTitle].totalResults = allSearchResults[animeTitle].mal.length + allSearchResults[animeTitle].anilist.length;

			if (allSearchResults[animeTitle].totalResults > 0) {
				toast.success(`Znaleziono ${allSearchResults[animeTitle].totalResults} wyników dla "${customSearchTerm}"`);
			} else {
				toast.error(`Nie znaleziono wyników dla "${customSearchTerm}"`);
				searchErrors[animeTitle] = `Nie znaleziono wyników dla "${customSearchTerm}".`;
				searchErrors = { ...searchErrors };
			}
		} catch (error) {
			console.error('Error in custom search:', error);
			toast.error('Wystąpił błąd podczas wyszukiwania');
			searchErrors[animeTitle] = 'Wystąpił błąd podczas wyszukiwania. Spróbuj ponownie.';
			searchErrors = { ...searchErrors };
		} finally {
			// Clear loading state
			customSearchLoading[animeTitle] = false;
			customSearchLoading = { ...customSearchLoading };
		}
	}

	// Legacy functions for backward compatibility
	async function searchAniListLegacy(animeTitle) {
		// Now just calls the main search function
		await searchAnime(animeTitle);
	}

	async function searchJikanLegacy(animeTitle) {
		// Now just calls the main search function
		await searchAnime(animeTitle);
	}

	function acceptAniListMatch(failedMatch, anilistResult) {
		// Create a match object similar to what we'd get from the server
		const match = {
			shindenAnime: {
				title: failedMatch.title,
				watchStatus: failedMatch.rawWatchStatus,
				watchedEpisodesCnt: failedMatch.watchedEpisodes,
				rateTotal: failedMatch.score
			},
			dbAnime: {
				title: anilistResult.title.romaji,
				synonyms: [...(anilistResult.title.english ? [`english:${anilistResult.title.english}`] : []), ...(anilistResult.title.native ? [`native:${anilistResult.title.native}`] : []), ...(anilistResult.synonyms || [])],
				sources: [anilistResult.siteUrl],
				type: anilistResult.type,
				episodes: anilistResult.episodes,
				malId: anilistResult.idMal || 0
			}
		};

		// Mark as selected from search results
		searchMatched[failedMatch.title] = true;
		searchMatched = { ...searchMatched }; // Trigger reactivity

		// Add to selected matches
		onSave([match]);

		// Remove from failed matches
		removeFromFailedMatches(failedMatch);

		// Remove from anilist results
		delete anilistResults[failedMatch.title];
		anilistResults = { ...anilistResults }; // Trigger reactivity

		// toast.success('Anime zostało dodane do dopasowanych!');
	}

	function acceptJikanMatch(failedMatch, jikanResult) {
		// Create a match object similar to what we'd get from the server
		const match = {
			shindenAnime: {
				title: failedMatch.title,
				watchStatus: failedMatch.rawWatchStatus,
				watchedEpisodesCnt: failedMatch.watchedEpisodes,
				rateTotal: failedMatch.score
			},
			dbAnime: {
				title: jikanResult.title,
				synonyms: [...(jikanResult.title_english ? [`english:${jikanResult.title_english}`] : []), ...(jikanResult.title_japanese ? [`native:${jikanResult.title_japanese}`] : []), ...(jikanResult.titles?.map((t) => t.title) || [])],
				sources: [`https://myanimelist.net/anime/${jikanResult.mal_id}`],
				type: jikanResult.type,
				episodes: jikanResult.episodes,
				malId: jikanResult.mal_id || 0
			}
		};

		// Mark as selected from search results
		searchMatched[failedMatch.title] = true;
		searchMatched = { ...searchMatched }; // Trigger reactivity

		// Add to selected matches
		onSave([match]);

		// Remove from failed matches
		removeFromFailedMatches(failedMatch);

		// Remove from jikan results
		delete jikanResults[failedMatch.title];
		jikanResults = { ...jikanResults }; // Trigger reactivity

		// toast.success('Anime zostało dodane do dopasowanych!');
	}

	function removeFromFailedMatches(failedMatch) {
		const index = failedMatches.findIndex((m) => m.title === failedMatch.title);
		if (index !== -1) {
			failedMatches.splice(index, 1);
			failedMatches = [...failedMatches]; // Trigger reactivity
			filteredFailedMatches = filteredFailedMatches.filter((m) => m.title !== failedMatch.title);
		}

		// Also clear any search errors
		delete searchErrors[failedMatch.title];
		searchErrors = { ...searchErrors };
	}

	function handleSaveMatches() {
		isLoading = true;

		try {
			// Prepare the selected matches data
			const matches = Object.entries(selectedMatches)
				.map(([title, index]) => {
					const match = multipleMatches.find((m) => m.title === title);
					if (!match) return null;

					return {
						shindenAnime: {
							title: match.title,
							watchStatus: match.rawWatchStatus, // Use the raw watch status
							watchedEpisodesCnt: match.watchedEpisodes,
							rateTotal: match.score
						},
						dbAnime: match.possibleMatches[index]
					};
				})
				.filter(Boolean);

			onSave(matches);
			toast.success('Wybrane dopasowania zostały zapisane!');
			closeModal();
		} catch (err) {
			console.error('Error saving matches:', err);
			toast.error('Nie udało się zapisać dopasowań');
		} finally {
			isLoading = false;
		}
	}
</script>

<Dialog.Root bind:open on:close={closeModal}>
	<Dialog.Content class="max-h-[85vh] w-[95vw] overflow-y-auto sm:max-w-[700px]">
		<Dialog.Header>
			<Dialog.Title>Dopasuj anime</Dialog.Title>
			<Dialog.Description class="text-xs sm:text-sm">
				Niektóre anime z twojej listy AnimeZone nie zostały automatycznie dopasowane. Wybierz poprawne dopasowania, aby uwzględnić je w eksporcie.
				<br /><span class="text-red-500">W sekcji "anime bez dopasowań" nie wybieraj anime za szybko bo się zduplikują, poczekaj na komunikat "Dopasowania zostały zaktualizowane!" </span>
			</Dialog.Description>
		</Dialog.Header>

		<div class="py-4 space-y-4">
			<div class="relative">
				<Search class="absolute top-2.5 left-2 h-4 w-4 text-gray-400" />
				<Input type="text" placeholder="Szukaj anime..." bind:value={searchTerm} class="pl-8" />
			</div>

			{#if filteredMultipleMatches.length > 0}
				<div class="p-4 bg-gray-800 rounded-md">
					<h3 class="mb-2 font-medium">Anime z wieloma możliwymi dopasowaniami ({filteredMultipleMatches.length})</h3>

					<Accordion.Root class="w-full" type="multiple" defaultValue={filteredMultipleMatches.map((_, i) => `multiple-${i}`)}>
						{#each filteredMultipleMatches as match, i}
							<Accordion.Item value={`multiple-${i}`} class="border-b border-gray-700">
								<Accordion.Trigger class="flex w-full flex-col justify-between px-4 py-2 text-left hover:bg-gray-700 sm:flex-row sm:items-center {searchMatched[match.title] ? 'bg-green-900/20' : selectedMatches[match.title] !== undefined ? 'bg-blue-900/20' : ''}">
									<div class="flex items-center mb-1 sm:mb-0">
										<span class="break-words">{match.title}</span>
										{#if searchMatched[match.title]}
											<span class="ml-2 flex items-center rounded-full bg-green-500 px-2 py-0.5 text-xs text-black">
												<Check class="w-3 h-3 mr-1" />
												Wybrano z wyszukiwania
											</span>
										{:else if selectedMatches[match.title] !== undefined}
											<span class="ml-2 flex items-center rounded-full bg-blue-300 px-2 py-0.5 text-xs text-black">
												<Check class="w-3 h-3 mr-1" />
												Wybrano dopasowanie
											</span>
										{/if}
									</div>
									<span class="text-xs text-gray-400 whitespace-normal sm:whitespace-nowrap">
										{match.watchStatus} • {match.watchedEpisodes} odcinków • Ocena: {match.score || 'brak'}
									</span>
								</Accordion.Trigger>
								<Accordion.Content class="p-4 bg-gray-700">
									<div class="space-y-2">
										<p class="text-sm text-gray-300">Wybierz poprawne dopasowanie:</p>
										<div class="grid gap-2">
											{#each match.possibleMatches as possibleMatch, j}
												<div class="flex items-center">
													<input type="radio" id={`match-${i}-${j}`} name={`match-${i}`} class="mr-2" checked={selectedMatches[match.title] === j && !searchMatched[match.title]} disabled={searchMatched[match.title]} on:change={() => handleSelectMatch(match.title, j)} />
													<label for={`match-${i}-${j}`} class="w-full text-sm {searchMatched[match.title] ? 'cursor-not-allowed opacity-50' : ''}">
														<div class="font-medium">{possibleMatch.title}</div>
														<div class="mt-1 text-xs text-gray-400">
															{#if possibleMatch.type}
																<span class="mr-2 rounded bg-gray-600 px-1.5 py-0.5">
																	{possibleMatch.type}
																</span>
															{/if}
															{#if possibleMatch.episodes}
																<span class="rounded bg-gray-600 px-1.5 py-0.5">
																	{possibleMatch.episodes} odcinków
																</span>
															{/if}
														</div>

														{#if possibleMatch.sources && possibleMatch.sources.length > 0}
															<div class="mt-1 text-xs text-gray-400">
																{#if possibleMatch.sources.find((s) => s.includes('myanimelist'))}
																	<a href={possibleMatch.sources.find((s) => s.includes('myanimelist'))} target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:underline"> Nie jesteś pewny? Sprawdź MyAnimeList </a>
																{:else if possibleMatch.sources.find((s) => s.includes('anilist'))}
																	<a href={possibleMatch.sources.find((s) => s.includes('anilist'))} target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:underline"> Nie jesteś pewny? Sprawdź AniList </a>
																{/if}
															</div>
														{/if}
													</label>
												</div>
											{/each}
										</div>

										<!-- Add custom search for multiple matches -->
										<div class="mt-4 rounded-md bg-gray-600 p-3 {searchMatched[match.title] ? 'border-2 border-green-500' : ''}">
											<h4 class="flex items-center mb-2 font-medium">
												Własne wyszukiwanie
												{#if searchMatched[match.title]}
													<span class="ml-2 flex items-center rounded-full bg-green-500 px-2 py-0.5 text-xs text-black">
														<Check class="w-3 h-3 mr-1" />
														Wybrano dopasowanie
													</span>
												{/if}
											</h4>
											<p class="mb-2 text-xs text-gray-400">Jeśli żadne z automatycznych dopasowań nie jest poprawne, możesz wyszukać anime ręcznie.</p>
											<div class="flex flex-col gap-2 sm:flex-row">
												<div class="relative flex-1">
													<Input id={`custom-search-multiple-${match.title}`} type="text" placeholder="Wpisz tytuł anime do wyszukania" bind:value={customSearchInputs[match.title]} class="flex-1 {searchMatched[match.title] ? 'border-green-500 focus:ring-green-500' : ''}" disabled={customSearchLoading[match.title] || searchMatched[match.title]} on:keydown={(e) => e.key === 'Enter' && !customSearchLoading[match.title] && !searchMatched[match.title] && handleCustomSearch(match)} />
													{#if searchMatched[match.title]}
														<div class="absolute text-green-500 transform -translate-y-1/2 top-1/2 right-2">
															<Check class="w-4 h-4" />
														</div>
													{/if}
												</div>
												<Button variant="outline" size="sm" class="w-full cursor-pointer text-xs sm:w-auto {searchMatched[match.title] ? 'border-green-500 text-green-500' : ''}" on:click={() => handleCustomSearch(match)} disabled={customSearchLoading[match.title] || searchMatched[match.title]}>
													{#if customSearchLoading[match.title]}
														<div class="w-3 h-3 mr-1 border-2 border-blue-400 rounded-full animate-spin border-t-transparent"></div>
														Szukam...
													{:else}
														Szukaj
													{/if}
												</Button>
											</div>

											{#if searchErrors[match.title]}
												<div class="p-2 mt-2 text-xs text-red-300 border border-red-700 rounded-md bg-red-900/30">
													{searchErrors[match.title]}
												</div>
											{/if}

											{#if allSearchResults[match.title]?.showAllResults && (allSearchResults[match.title]?.mal?.length > 0 || allSearchResults[match.title]?.anilist?.length > 0)}
												<!-- Show multiple results from both sources -->
												<div class="mt-3 space-y-3">
													<!-- MyAnimeList Results -->
													{#if allSearchResults[match.title]?.mal?.length > 0}
														<div class="p-2 bg-gray-700 rounded-md">
															<h5 class="mb-2 text-sm font-medium">Wyniki z MyAnimeList ({Math.min(allSearchResults[match.title].mal.length, 5)})</h5>
															<div class="space-y-2">
																{#each allSearchResults[match.title].mal.slice(0, 5) as malResult, index}
																	<div class="p-2 bg-gray-800 rounded-md">
																		<div class="flex items-start">
																			{#if malResult.images?.jpg?.image_url}
																				<img src={malResult.images.jpg.image_url} alt={malResult.title} class="hidden w-8 h-auto mr-2 rounded sm:mr-3 sm:block sm:w-10" />
																			{/if}
																			<div class="flex-1">
																				<div class="flex flex-col sm:flex-row sm:justify-between">
																					<div class="pr-8 text-sm font-medium break-words sm:pr-0">{malResult.title}</div>
																					<div class="absolute right-2 rounded bg-gray-900 px-2 py-0.5 text-xs text-gray-400 sm:static">
																						#{index + 1}
																					</div>
																				</div>
																				<div class="flex flex-wrap gap-1 mt-1 text-xs text-gray-400">
																					<span class="rounded bg-gray-900 px-1.5 py-0.5">
																						{malResult.type}
																					</span>
																					{#if malResult.episodes}
																						<span class="rounded bg-gray-900 px-1.5 py-0.5">
																							{malResult.episodes} odcinków
																						</span>
																					{/if}
																				</div>
																				<div class="mt-2">
																					<Button variant="outline" size="sm" class="w-full text-xs text-green-400 border-green-700 cursor-pointer bg-green-800/30 hover:bg-green-800/50 sm:w-auto" on:click={() => acceptJikanMatch(match, malResult)}>
																						<Check class="w-3 h-3 mr-1" />
																						Wybierz
																					</Button>
																				</div>
																			</div>
																		</div>
																	</div>
																{/each}
															</div>
														</div>
													{/if}

													<!-- AniList Results -->
													{#if allSearchResults[match.title]?.anilist?.length > 0}
														<div class="p-2 bg-gray-700 rounded-md">
															<h5 class="mb-2 text-sm font-medium">Wyniki z AniList ({Math.min(allSearchResults[match.title].anilist.length, 5)})</h5>
															<div class="space-y-2">
																{#each allSearchResults[match.title].anilist.slice(0, 5) as anilistResult, index}
																	<div class="p-2 bg-gray-800 rounded-md">
																		<div class="flex items-start">
																			{#if anilistResult.coverImage?.medium}
																				<img src={anilistResult.coverImage.medium} alt={anilistResult.title.romaji} class="hidden w-8 h-auto mr-2 rounded sm:mr-3 sm:block sm:w-10" />
																			{/if}
																			<div class="flex-1">
																				<div class="flex flex-col sm:flex-row sm:justify-between">
																					<div class="pr-8 text-sm font-medium break-words sm:pr-0">{anilistResult.title.romaji}</div>
																					<div class="absolute right-2 rounded bg-gray-900 px-2 py-0.5 text-xs text-gray-400 sm:static">
																						#{index + 1}
																					</div>
																				</div>
																				<div class="flex flex-wrap gap-1 mt-1 text-xs text-gray-400">
																					<span class="rounded bg-gray-900 px-1.5 py-0.5">
																						{anilistResult.type}
																					</span>
																					{#if anilistResult.episodes}
																						<span class="rounded bg-gray-900 px-1.5 py-0.5">
																							{anilistResult.episodes} odcinków
																						</span>
																					{/if}
																				</div>
																				<div class="mt-2">
																					<Button variant="outline" size="sm" class="w-full text-xs text-green-400 border-green-700 cursor-pointer bg-green-800/30 hover:bg-green-800/50 sm:w-auto" on:click={() => acceptAniListMatch(match, anilistResult)}>
																						<Check class="w-3 h-3 mr-1" />
																						Wybierz
																					</Button>
																				</div>
																			</div>
																		</div>
																	</div>
																{/each}
															</div>
														</div>
													{/if}
												</div>
											{/if}
										</div>
									</div>
								</Accordion.Content>
							</Accordion.Item>
						{/each}
					</Accordion.Root>
				</div>
			{/if}

			{#if filteredFailedMatches.length > 0}
				<div class="p-4 bg-gray-800 rounded-md">
					<h3 class="mb-2 font-medium">Anime bez dopasowań ({filteredFailedMatches.length})</h3>

					<div class="space-y-2">
						<p class="text-sm text-gray-300">Poniższe anime nie zostały dopasowane do bazy danych. Kliknij "Szukaj na MyAnimeList" aby spróbować znaleźć odpowiednie anime.</p>

						<div class="space-y-4">
							{#each filteredFailedMatches as match, i}
								<div class="p-3 bg-gray-700 rounded-md">
									<div class="flex flex-col justify-between gap-2 mb-2 sm:flex-row sm:items-start">
										<div class="break-words">
											<div class="font-medium">{match.title}</div>
											<div class="text-xs text-gray-400">
												{match.watchStatus} • {match.watchedEpisodes} odcinków • Ocena: {match.score || 'brak'}
											</div>
										</div>

										{#if !jikanResults[match.title] && !anilistResults[match.title] && !jikanLoading[match.title] && !anilistLoading[match.title]}
											<Button variant="outline" size="sm" class="w-full mt-1 text-xs cursor-pointer sm:mt-0 sm:w-auto" on:click={() => searchAnime(match.title)}>
												<RefreshCw class="w-3 h-3 mr-1" />
												Szukaj na MyAnimeList
											</Button>
										{:else if jikanLoading[match.title] || anilistLoading[match.title]}
											<div class="flex items-center mt-1 text-xs text-gray-400 sm:mt-0">
												<div class="w-3 h-3 mr-1 border-2 border-blue-400 rounded-full animate-spin border-t-transparent"></div>
												Szukam...
											</div>
										{/if}
									</div>

									{#if allSearchResults[match.title]?.showAllResults && (allSearchResults[match.title]?.mal?.length > 0 || allSearchResults[match.title]?.anilist?.length > 0)}
										<!-- Show multiple results from both sources -->
										<div class="mt-2 space-y-4">
											<!-- MyAnimeList Results -->
											{#if allSearchResults[match.title]?.mal?.length > 0}
												<div class="p-3 bg-gray-600 rounded-md">
													<h4 class="mb-2 font-medium">Wyniki z MyAnimeList ({Math.min(allSearchResults[match.title].mal.length, 5)})</h4>
													<div class="space-y-3">
														{#each allSearchResults[match.title].mal.slice(0, 5) as malResult, index}
															<div class="p-2 bg-gray-700 rounded-md">
																<div class="flex items-start">
																	{#if malResult.images?.jpg?.image_url}
																		<img src={malResult.images.jpg.image_url} alt={malResult.title} class="hidden w-10 h-auto mr-2 rounded sm:mr-3 sm:block sm:w-12" />
																	{/if}
																	<div class="flex-1">
																		<div class="flex flex-col sm:flex-row sm:justify-between">
																			<div class="pr-8 text-sm font-medium break-words sm:pr-0">{malResult.title}</div>
																			<div class="absolute right-2 rounded bg-gray-800 px-2 py-0.5 text-xs text-gray-400 sm:static">
																				#{index + 1}
																			</div>
																		</div>
																		{#if malResult.title_english}
																			<div class="text-xs text-gray-300 break-words">{malResult.title_english}</div>
																		{/if}
																		<div class="flex flex-wrap gap-1 mt-1 text-xs text-gray-400">
																			<span class="rounded bg-gray-800 px-1.5 py-0.5">
																				{malResult.type}
																			</span>
																			{#if malResult.episodes}
																				<span class="rounded bg-gray-800 px-1.5 py-0.5">
																					{malResult.episodes} odcinków
																				</span>
																			{/if}
																			{#if malResult.mal_id}
																				<span class="rounded bg-gray-800 px-1.5 py-0.5">
																					MAL ID: {malResult.mal_id}
																				</span>
																			{/if}
																		</div>
																		<div class="mt-2">
																			<Button variant="outline" size="sm" class="w-full text-xs text-green-400 border-green-700 cursor-pointer bg-green-800/30 hover:bg-green-800/50 sm:w-auto" on:click={() => acceptJikanMatch(match, malResult)}>
																				<Check class="w-3 h-3 mr-1" />
																				Wybierz
																			</Button>
																		</div>
																	</div>
																</div>
															</div>
														{/each}
													</div>
												</div>
											{/if}

											<!-- AniList Results -->
											{#if allSearchResults[match.title]?.anilist?.length > 0}
												<div class="p-3 bg-gray-600 rounded-md">
													<h4 class="mb-2 font-medium">Wyniki z AniList ({Math.min(allSearchResults[match.title].anilist.length, 5)})</h4>
													<div class="space-y-3">
														{#each allSearchResults[match.title].anilist.slice(0, 5) as anilistResult, index}
															<div class="p-2 bg-gray-700 rounded-md">
																<div class="flex items-start">
																	{#if anilistResult.coverImage?.medium}
																		<img src={anilistResult.coverImage.medium} alt={anilistResult.title.romaji} class="hidden w-10 h-auto mr-2 rounded sm:mr-3 sm:block sm:w-12" />
																	{/if}
																	<div class="flex-1">
																		<div class="flex flex-col sm:flex-row sm:justify-between">
																			<div class="pr-8 text-sm font-medium break-words sm:pr-0">{anilistResult.title.romaji}</div>
																			<div class="absolute right-2 rounded bg-gray-800 px-2 py-0.5 text-xs text-gray-400 sm:static">
																				#{index + 1}
																			</div>
																		</div>
																		{#if anilistResult.title.english}
																			<div class="text-xs text-gray-300 break-words">{anilistResult.title.english}</div>
																		{/if}
																		<div class="flex flex-wrap gap-1 mt-1 text-xs text-gray-400">
																			<span class="rounded bg-gray-800 px-1.5 py-0.5">
																				{anilistResult.type}
																			</span>
																			{#if anilistResult.episodes}
																				<span class="rounded bg-gray-800 px-1.5 py-0.5">
																					{anilistResult.episodes} odcinków
																				</span>
																			{/if}
																			{#if anilistResult.idMal}
																				<span class="rounded bg-gray-800 px-1.5 py-0.5">
																					MAL ID: {anilistResult.idMal}
																				</span>
																			{/if}
																		</div>
																		<div class="mt-2">
																			<Button variant="outline" size="sm" class="w-full text-xs text-green-400 border-green-700 cursor-pointer bg-green-800/30 hover:bg-green-800/50 sm:w-auto" on:click={() => acceptAniListMatch(match, anilistResult)}>
																				<Check class="w-3 h-3 mr-1" />
																				Wybierz
																			</Button>
																		</div>
																	</div>
																</div>
															</div>
														{/each}
													</div>
												</div>
											{/if}

											<!-- Custom Search Input -->
											<div class="p-3 mt-4 bg-gray-600 rounded-md">
												<h4 class="mb-2 font-medium">Własne wyszukiwanie</h4>
												<div class="flex flex-col gap-2 sm:flex-row">
													<Input id={`custom-search-${match.title}`} type="text" placeholder="Wpisz tytuł anime do wyszukania" bind:value={customSearchInputs[match.title]} class="flex-1" disabled={customSearchLoading[match.title]} on:keydown={(e) => e.key === 'Enter' && !customSearchLoading[match.title] && handleCustomSearch(match)} />
													<Button variant="outline" size="sm" class="w-full text-xs cursor-pointer sm:w-auto" on:click={() => handleCustomSearch(match)} disabled={customSearchLoading[match.title]}>
														{#if customSearchLoading[match.title]}
															<div class="w-3 h-3 mr-1 border-2 border-blue-400 rounded-full animate-spin border-t-transparent"></div>
															Szukam...
														{:else}
															Szukaj
														{/if}
													</Button>
												</div>
											</div>
										</div>
									{:else if manualInputMode[match.title]}
										<!-- Manual Input Form -->
										<div class="p-3 mt-2 bg-gray-600 rounded-md">
                                            <h4 class="mb-3 font-medium">Kliknij na przycisk szukania jeszcze raz by je ponowić.</h4>
										</div>
									{:else if anilistResults[match.title]}
										<!-- Legacy single result view for AniList -->
										<div class="p-2 mt-2 bg-gray-600 rounded-md">
											<div class="flex items-start">
												{#if anilistResults[match.title].coverImage?.medium}
													<img src={anilistResults[match.title].coverImage.medium} alt={anilistResults[match.title].title.romaji} class="hidden w-12 h-auto mr-3 rounded sm:block" />
												{/if}

												<div class="flex-1">
													<div class="flex flex-col sm:flex-row sm:justify-between">
														<div class="pr-8 font-medium break-words sm:pr-0">{anilistResults[match.title].title.romaji}</div>
														<div class="absolute right-2 rounded bg-gray-700 px-2 py-0.5 text-xs text-gray-400 sm:static">
															AniList {allSearchResults[match.title]?.currentAnilistIndex || 1}/{allSearchResults[match.title]?.anilist?.length || 1}
														</div>
													</div>
													{#if anilistResults[match.title].title.english}
														<div class="text-xs text-gray-300 break-words">{anilistResults[match.title].title.english}</div>
													{/if}
													<div class="flex flex-wrap gap-1 mt-1 text-xs text-gray-400">
														<span class="rounded bg-gray-700 px-1.5 py-0.5">
															{anilistResults[match.title].type}
														</span>
														{#if anilistResults[match.title].episodes}
															<span class="rounded bg-gray-700 px-1.5 py-0.5">
																{anilistResults[match.title].episodes} odcinków
															</span>
														{/if}
														{#if anilistResults[match.title].idMal}
															<span class="rounded bg-gray-700 px-1.5 py-0.5">
																MAL ID: {anilistResults[match.title].idMal}
															</span>
														{/if}
													</div>

													<div class="flex flex-col gap-2 mt-2 sm:flex-row">
														<Button variant="outline" size="sm" class="w-full text-xs text-green-400 border-green-700 cursor-pointer bg-green-800/30 hover:bg-green-800/50 sm:w-auto" on:click={() => acceptAniListMatch(match, anilistResults[match.title])}>
															<Check class="w-3 h-3 mr-1" />
															To jest to
														</Button>

														<Button variant="outline" size="sm" class="w-full text-xs text-red-400 border-red-700 cursor-pointer bg-red-800/30 hover:bg-red-800/50 sm:w-auto" on:click={() => enableManualInput(match.title)}>
															<X class="w-3 h-3 mr-1" />
															Wprowadź ręcznie
														</Button>
													</div>
												</div>
											</div>
										</div>
									{:else if jikanResults[match.title]}
										<!-- Legacy single result view for MyAnimeList -->
										<div class="p-2 mt-2 bg-gray-600 rounded-md">
											<div class="flex items-start">
												{#if jikanResults[match.title].images?.jpg?.image_url}
													<img src={jikanResults[match.title].images.jpg.image_url} alt={jikanResults[match.title].title} class="hidden w-12 h-auto mr-3 rounded sm:block" />
												{/if}

												<div class="flex-1">
													<div class="flex flex-col sm:flex-row sm:justify-between">
														<div class="pr-8 font-medium break-words sm:pr-0">{jikanResults[match.title].title}</div>
														<div class="absolute right-2 rounded bg-gray-700 px-2 py-0.5 text-xs text-gray-400 sm:static">
															MyAnimeList {allSearchResults[match.title]?.currentMalIndex || 1}/{allSearchResults[match.title]?.mal?.length || 1}
														</div>
													</div>
													{#if jikanResults[match.title].title_english}
														<div class="text-xs text-gray-300 break-words">{jikanResults[match.title].title_english}</div>
													{/if}
													<div class="flex flex-wrap gap-1 mt-1 text-xs text-gray-400">
														<span class="rounded bg-gray-700 px-1.5 py-0.5">
															{jikanResults[match.title].type}
														</span>
														{#if jikanResults[match.title].episodes}
															<span class="rounded bg-gray-700 px-1.5 py-0.5">
																{jikanResults[match.title].episodes} odcinków
															</span>
														{/if}
														{#if jikanResults[match.title].mal_id}
															<span class="rounded bg-gray-700 px-1.5 py-0.5">
																MAL ID: {jikanResults[match.title].mal_id}
															</span>
														{/if}
													</div>

													<div class="flex flex-col gap-2 mt-2 sm:flex-row">
														<Button variant="outline" size="sm" class="w-full text-xs text-green-400 border-green-700 cursor-pointer bg-green-800/30 hover:bg-green-800/50 sm:w-auto" on:click={() => acceptJikanMatch(match, jikanResults[match.title])}>
															<Check class="w-3 h-3 mr-1" />
															To jest to
														</Button>

														<Button variant="outline" size="sm" class="w-full text-xs text-red-400 border-red-700 cursor-pointer bg-red-800/30 hover:bg-red-800/50 sm:w-auto" on:click={() => enableManualInput(match.title)}>
															<X class="w-3 h-3 mr-1" />
															Wprowadź ręcznie
														</Button>
													</div>
												</div>
											</div>
										</div>
									{/if}
								</div>
							{/each}
						</div>
					</div>
				</div>
			{/if}

			<div class="p-4 bg-gray-800 rounded-md">
				<h3 class="mb-2 font-medium">Uwaga</h3>
				<p class="text-sm text-gray-300">Anime, które nie zostaną dopasowane, nie będą uwzględnione w eksportowanym pliku XML. Możesz później ręcznie dodać je do swojej listy na AniList lub MyAnimeList.</p>
			</div>
		</div>

		<Dialog.Footer class="flex flex-col-reverse gap-2 sm:flex-row sm:gap-0">
			<Button variant="outline" on:click={closeModal} disabled={isLoading} class="w-full cursor-pointer sm:w-auto">Anuluj</Button>
			<Button on:click={handleSaveMatches} disabled={isLoading} class="w-full cursor-pointer bg-[#ee8585] text-black hover:bg-[#8ec3f4] sm:w-auto">
				{#if isLoading}
					<div class="w-4 h-4 mr-2 border-2 border-white rounded-full animate-spin border-t-transparent"></div>
					Zapisuję...
				{:else}
					Zapisz dopasowania
				{/if}
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
