//src/routes/api/comments/like/+server.js
import { json, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request, locals }) {
  try {
    // Verify user is authenticated
    const { session, user } = await locals.safeGetSession();
    if (!session || !user) {
      throw error(401, 'Must be logged in to like comments');
    }

    const { commentId } = await request.json();

    // Check if user has already liked/disliked
    const { data: existing, error: checkError } = await supabase
      .from('comment_reactions')
      .select('reaction_type')  // Changed to select just reaction_type
      .eq('comment_id', commentId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" which is ok
      throw checkError;
    }

    // Call the RPC function with all required parameters in correct order
    const { data, error: transactionError } = await supabase.rpc('toggle_like', {
      p_comment_id: commentId,
      p_user_id: user.id,
      had_previous_reaction: !!existing,
      previous_reaction_type: existing?.reaction_type || null  // Add null fallback
    });

    if (transactionError) throw transactionError;

    return json({ likes: data.likes, dislikes: data.dislikes });
  } catch (err) {
    console.error('Error liking comment:', err);
    throw error(err.status || 500, err.message || 'Error processing reaction');
  }
}