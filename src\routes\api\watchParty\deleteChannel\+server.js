import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET(params) {
    let rommCode = params.url.searchParams.get('roomCode');
    const metadata = await deleteChannel(rommCode);
    return new Response(JSON.stringify(metadata));
}

async function deleteChannel(roomCode) {
    const { error } = await supabase
        .from('rooms')
        .delete()
        .eq('room_code', roomCode)
    return;
}