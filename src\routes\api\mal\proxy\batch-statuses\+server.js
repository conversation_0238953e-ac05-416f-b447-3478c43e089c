// src/routes/api/mal/proxy/batch-statuses/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabaseAdmin = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Helper function to parse MAL date format (YYYY-MM-DD) to object
function parseMALDate(dateString) {
  try {
    const [year, month, day] = dateString.split('-').map(Number);
    return { year, month, day };
  } catch (e) {
    console.error('Date parsing error:', e, 'for date string:', dateString);
    return null;
  }
}

// Helper function to map MAL status to AniList format
function mapMALStatusToAniList(status) {
  switch (status) {
    case 'watching': return 'CURRENT';
    case 'completed': return 'COMPLETED';
    case 'on_hold': return 'PAUSED';
    case 'dropped': return 'DROPPED';
    case 'plan_to_watch': return 'PLANNING';
    default: return 'PLANNING';
  }
}

export async function POST({ request }) {
  let requestBody;
  try {
    requestBody = await request.json();
  } catch (e) {
    console.error('Failed to parse request JSON:', e);
    return json({ error: 'Invalid JSON in request body' }, { status: 400 });
  }

  const { animeIds, token } = requestBody;

  if (!animeIds || !animeIds.length || !token) {
    console.error('Missing required parameters:', {
      animeIdsPresent: !!animeIds,
      animeIdsLength: animeIds?.length,
      tokenPresent: !!token
    });
    return json({ error: 'Missing required parameters' }, { status: 400 });
  }

  try {
    // First, map AniList IDs to MAL IDs
    const { data: mappingData, error: mappingError } = await supabaseAdmin
      .from('anime_metadata')
      .select('mal_id, anilist_id')
      .in('anilist_id', animeIds)
      .not('mal_id', 'is', null);

    if (mappingError) {
      console.error('Database mapping query error:', mappingError);
      return json({
        error: 'Failed to fetch ID mappings from database',
        statuses: []
      }, { status: 500 });
    }

    // Create a mapping of AniList IDs to MAL IDs
    const idMap = new Map();
    mappingData.forEach(item => {
      idMap.set(parseInt(item.anilist_id), parseInt(item.mal_id));
    });

    // Get the corresponding MAL IDs
    const malIds = mappingData.map(item => parseInt(item.mal_id)).filter(id => !isNaN(id));

    if (malIds.length === 0) {
      return json({ statuses: [] });
    }

    // Group anime IDs in batches to avoid too many requests at once
    const batchSize = 10;
    const batches = [];

    for (let i = 0; i < malIds.length; i += batchSize) {
      batches.push(malIds.slice(i, i + batchSize));
    }

    const allResults = [];

    // Process each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      const promises = batch.map(async (malId) => {
        try {
          // Check if this anime is in the user's list
          const url = `https://api.myanimelist.net/v2/anime/${malId}?fields=my_list_status`;

          const response = await fetch(url, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            console.error(`Non-OK response for MAL anime ${malId}:`, response.status, response.statusText);
            return null;
          }

          const data = await response.json();
          if (!data.my_list_status) {
            return null;
          }

          // Find corresponding AniList ID
          const anilistId = [...idMap.entries()]
            .find(([_, id]) => id === malId)?.[0];

          if (!anilistId) {
            return null;
          }

          // Map MAL list status to a format similar to AniList
          const result = {
            mediaId: anilistId, // Return AniList ID as mediaId to match expected format
            status: mapMALStatusToAniList(data.my_list_status.status),
            score: data.my_list_status.score || 0,
            progress: data.my_list_status.num_episodes_watched || 0,
            startedAt: data.my_list_status.start_date ? parseMALDate(data.my_list_status.start_date) : null,
            completedAt: data.my_list_status.finish_date ? parseMALDate(data.my_list_status.finish_date) : null,
            updatedAt: data.my_list_status.updated_at ? new Date(data.my_list_status.updated_at * 1000).getTime() / 1000 : null
          };
          return result;
        } catch (error) {
          console.error(`Error fetching MAL status for anime ${malId}:`, error);
          console.error('Error stack:', error.stack);
          return null;
        }
      });

      const batchResults = await Promise.all(promises);

      const validResults = batchResults.filter(Boolean);

      allResults.push(...validResults);

      // Add a small delay between batches to avoid rate limiting
      if (batches.length > 1 && batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    return json({ statuses: allResults });
  } catch (error) {
    console.error('Error fetching batch anime statuses from MAL:', error);
    console.error('Error stack:', error.stack);
    return json({
      error: error.message,
      stack: error.stack,
      statuses: []
    }, { status: 500 });
  }
}