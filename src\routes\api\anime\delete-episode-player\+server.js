import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function DELETE({ request }) {
	try {
		const {
			episodeId,
			translatingGroup,
			externalPlayerLink
		} = await request.json();

		if (!episodeId || !translatingGroup || !externalPlayerLink) {
			return json({ 
				error: 'Missing required fields: episodeId, translatingGroup, externalPlayerLink' 
			}, { status: 400 });
		}

		// First, get the episode details to find the correct anime
		const { data: existingEpisode, error: fetchError } = await supabase
			.from('anime_new')
			.select('anilist_id, episode_number')
			.eq('id', episodeId)
			.single();

		if (fetchError || !existingEpisode) {
			return json({ error: 'Episode not found' }, { status: 404 });
		}

		// Find and delete the specific episode player entry
		const { data: deletedEpisode, error: deleteError } = await supabase
			.from('anime_new')
			.delete()
			.eq('anilist_id', existingEpisode.anilist_id)
			.eq('episode_number', existingEpisode.episode_number)
			.eq('translating_group', translatingGroup)
			.eq('external_player_link', externalPlayerLink)
			.select()
			.single();

		if (deleteError) {
			console.error('Error deleting episode player:', deleteError);
			
			// Check if it's because no matching record was found
			if (deleteError.code === 'PGRST116') {
				return json({ 
					error: 'Player not found with the specified criteria' 
				}, { status: 404 });
			}
			
			return json({ error: 'Failed to delete player' }, { status: 500 });
		}

		if (!deletedEpisode) {
			return json({ 
				error: 'Player not found with the specified criteria' 
			}, { status: 404 });
		}

		return json({
			success: true,
			deletedEpisode: deletedEpisode
		});

	} catch (error) {
		console.error('Error deleting episode player:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
}
