<script>
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Select from '$lib/components/ui/select';
	import { X, ChevronUp, ChevronDown, CalendarIcon } from 'lucide-svelte';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Calendar } from '$lib/components/ui/calendar/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import { DateFormatter } from '@internationalized/date';
	import { cn } from '$lib/utils.js';
	import { toast } from 'svelte-sonner';
	import { Star, Frown, Meh, Smile } from 'lucide-svelte';
	import { userStore } from '$lib/stores/userLogin';

	export let open = false;
	export let anime;
	export let onClose;
	export let onSave;
	export let onDelete;

	let isLoading = true;
	let initialFetchError = false;

	const reverseStatusMap = {
		CURRENT: 'watching',
		current: 'watching',
		COMPLETED: 'completed',
		completed: 'completed',
		PAUSED: 'on-hold',
		paused: 'on-hold',
		DROPPED: 'dropped',
		dropped: 'dropped',
		PLANNING: 'planning',
		planning: 'planning',
		REPEATING: 'repeating',
		repeating: 'repeating'
	};

	const df = new DateFormatter('pl-PL', { dateStyle: 'long' });

	let selectedStatus;
	let modalElement;
	let isSubmitting = false;
	let scoreFormat = 'POINT_10'; // Default format
	let maxScore = 10;
	let step = 1;
	let showDecimals = false;
	let showStars = false;
	let showSmileys = false;

	$: if (open && isLoading) {
		fetchAniListData().catch((error) => {
			console.error('Error in initial data fetch:', error);
			isLoading = false;
			initialFetchError = true;
		});
	}

	function handleModalClose() {
		isLoading = true; // Reset loading state for next open
		initialFetchError = false;
		onClose();
	}

	const statusMap = {
		watching: 'CURRENT',
		completed: 'COMPLETED',
		'on-hold': 'PAUSED',
		dropped: 'DROPPED',
		planning: 'PLANNING',
		repeating: 'REPEATING'
	};

	const ANILIST_API = 'https://graphql.anilist.co';

	const malStatusMap = {
		watching: 'watching',
		completed: 'completed',
		'on-hold': 'on_hold',
		dropped: 'dropped',
		planning: 'plan_to_watch',
		repeating: 'watching' // MAL doesn't have a "repeating" status, map to watching
	};

	// Add reverse mapping
	const reverseMalStatusMap = {
		watching: 'watching',
		completed: 'completed',
		on_hold: 'on-hold',
		dropped: 'dropped',
		plan_to_watch: 'planning'
	};

	async function refreshMALToken() {
		try {
			const response = await fetch('/api/mal/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					mal_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing MAL token:', error);
			toast.error('Nie udało się odświeżyć tokena MAL. Możesz spróbować wylogować i zalogować się ponownie aby naprawić ten błąd.', {
				duration: Number.POSITIVE_INFINITY
			});
			return null;
		}
	}

	async function fetchMALData() {
		if (!open || !isLoading) return;

		try {
			let token = $userStore.user_metadata?.mal_token;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshMALToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token) {
				throw new Error('No MAL authentication available');
			}

			// Use server proxy instead of direct MAL API access
			const response = await fetch('/api/mal/proxy/anime-status', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					animeId: anime.mal_id,
					token
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || `Failed to fetch MAL data: ${response.statusText}`);
			}

			const data = await response.json();
			const myStatus = data.my_list_status;

			// Configure score format based on MAL's 10-point system
			scoreFormat = 'POINT_10';
			configureScoreFormat();

			if (!myStatus) {
				// New anime not in list yet
				selectedStatus = 'planning';
				anime = {
					...anime,
					status: 'planning',
					score: 0,
					episodeProgress: '',
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
				isLoading = false;
				return;
			}

			const mappedStatus = reverseMalStatusMap[myStatus.status] || 'planning';
			selectedStatus = mappedStatus;

			anime = {
				...anime,
				status: mappedStatus,
				score: myStatus.score || 0,
				episodeProgress: myStatus.num_episodes_watched || '',
				startDate: myStatus.start_date ? new Date(myStatus.start_date) : null,
				finishDate: myStatus.finish_date ? new Date(myStatus.finish_date) : null,
				totalRewatches: myStatus.num_times_rewatched || '',
				notes: myStatus.comments || ''
			};

			isLoading = false;
		} catch (error) {
			console.error('Error fetching MAL data:', error);
			toast.error('Nie udało się pobrać danych z MAL');

			// Set default values
			anime = {
				...anime,
				status: 'planning',
				score: 0,
				episodeProgress: '',
				startDate: null,
				finishDate: null,
				totalRewatches: '',
				notes: ''
			};
			selectedStatus = 'planning';

			isLoading = false;
			initialFetchError = true;
			throw error;
		}
	}

	async function saveToMAL() {
		try {
			let token = $userStore.user_metadata?.mal_token;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshMALToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token) {
				throw new Error('No MAL authentication available');
			}

			toast.info('Aktualizuję wpis...');

			const malStatus = malStatusMap[selectedStatus] || 'plan_to_watch';

			// Prepare data for the server
			const updateData = {
				animeId: anime.mal_id,
				token,
				status: malStatus,
				score: Math.round(anime.score || 0),
				numWatchedEpisodes: parseInt(anime.episodeProgress) || 0
			};

			if (anime.totalRewatches) {
				updateData.numTimesRewatched = parseInt(anime.totalRewatches) || 0;
			}

			if (anime.notes) {
				updateData.comments = anime.notes;
			}

			if (anime.startDate) {
				updateData.startDate = anime.startDate.toISOString().split('T')[0];
			}

			if (anime.finishDate) {
				updateData.finishDate = anime.finishDate.toISOString().split('T')[0];
			}

			// Use server proxy instead of direct MAL API access
			const response = await fetch('/api/mal/proxy/update-entry', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(updateData)
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update MAL entry');
			}

			toast.success('Zaktualizowano wpis');
			onSave(anime);
		} catch (error) {
			console.error('Error saving to MAL:', error);
			toast.error('Wystąpił błąd podczas zapisywania');
			onSave(anime);
		}
	}

	async function saveToAniList() {
		try {
			let token = $userStore.user_metadata?.anilist_token;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			// Check token expiry and refresh if needed
			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshAnilistToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token) {
				throw new Error('No AniList authentication available');
			}

			toast.info('Aktualizuję wpis...');

			const statusToUse = !selectedStatus || selectedStatus === 'Wybierz status' ? 'CURRENT' : statusMap[selectedStatus] || 'CURRENT';

			const response = await fetch(ANILIST_API, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json',
					Authorization: `Bearer ${token}`
				},
				body: JSON.stringify({
					query: MUTATION,
					variables: {
						mediaId: anime.id,
						status: statusToUse,
						score: anime.score || 0,
						progress: parseInt(anime.episodeProgress) || 0,
						startedAt: anime.startDate
							? {
									year: anime.startDate.getFullYear(),
									month: anime.startDate.getMonth() + 1,
									day: anime.startDate.getDate()
								}
							: null,
						completedAt: anime.finishDate
							? {
									year: anime.finishDate.getFullYear(),
									month: anime.finishDate.getMonth() + 1,
									day: anime.finishDate.getDate()
								}
							: null,
						repeat: parseInt(anime.totalRewatches) || 0,
						notes: anime.notes || ''
					}
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.errors?.[0]?.message || 'Failed to update AniList entry');
			}

			const data = await response.json();

			if (data.errors) {
				throw new Error(data.errors[0]?.message || 'AniList API returned errors');
			}

			toast.success('Zaktualizowano wpis');
			onSave(anime);
		} catch (error) {
			throw error;
		}
	}

	function formatDisplayScore(score) {
		switch (scoreFormat) {
			case 'POINT_100':
				return Math.round(score);
			case 'POINT_10_DECIMAL':
				return score.toFixed(1); // Always show one decimal place
			case 'POINT_10':
				return Math.round(score);
			case 'POINT_5':
				return Math.round(score);
			case 'POINT_3':
				return Math.round(score);
			default:
				return Math.round(score);
		}
	}

	const MEDIA_ENTRY_QUERY = `
query ($mediaId: Int, $userId: Int) {
  Media(id: $mediaId) {
    id
    title {
      romaji 
      english
    }
  }
  MediaList(userId: $userId, mediaId: $mediaId) {
    id
    status
    score
    progress
    startedAt {
      year
      month
      day
    }
    completedAt {
      year
      month
      day
    }
    repeat
    notes
    updatedAt
  }
  Viewer {
    id
    mediaListOptions {
      scoreFormat
    }
  }
}`;

	const MUTATION = `
	mutation (
		$mediaId: Int, 
		$status: MediaListStatus, 
		$score: Float,
		$progress: Int,
		$startedAt: FuzzyDateInput,
		$completedAt: FuzzyDateInput,
		$repeat: Int,
		$notes: String
	) {
		SaveMediaListEntry (
			mediaId: $mediaId,
			status: $status,
			score: $score,
			progress: $progress,
			startedAt: $startedAt,
			completedAt: $completedAt,
			repeat: $repeat,
			notes: $notes
		) {
			id
			status
			score
			progress
			startedAt {
				year
				month
				day
			}
			completedAt {
				year
				month
				day
			}
			repeat
			notes
		}
	}`;

	function configureScoreFormat() {
		switch (scoreFormat) {
			case 'POINT_100':
				maxScore = 100;
				step = 1;
				showDecimals = false;
				showStars = false;
				showSmileys = false;
				break;
			case 'POINT_10_DECIMAL':
				maxScore = 10;
				step = 0.1;
				showDecimals = true;
				showStars = false;
				showSmileys = false;
				break;
			case 'POINT_10':
				maxScore = 10;
				step = 1;
				showDecimals = false;
				showStars = false;
				showSmileys = false;
				break;
			case 'POINT_5':
				maxScore = 5;
				step = 1;
				showDecimals = false;
				showStars = true;
				showSmileys = false;
				break;
			case 'POINT_3':
				maxScore = 3;
				step = 1;
				showDecimals = false;
				showStars = false;
				showSmileys = true;
				break;
		}
	}
	function getScoreDisplay(score) {
		if (showSmileys) {
			const smileys = ['', ':(', ':|', ':)'];
			return smileys[score] || '';
		}
		if (showStars) {
			return '⭐'.repeat(score);
		}
		if (showDecimals) {
			return score.toFixed(1);
		}
		return Math.round(score);
	}

	async function refreshAnilistToken() {
		try {
			const response = await fetch('/api/anilist/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			// Update user store with new token
			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					anilist_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing token:', error);
			toast.error('Nie udało się odświeżyć tokena');
			return null;
		}
	}

	async function fetchAniListData() {
		if (!open || !isLoading) return;

		const provider = $userStore?.user_metadata?.provider;

		if (provider === 'mal') {
			return await fetchMALData();
		}

		try {
			let token = $userStore.user_metadata?.anilist_token;
			const userId = $userStore.user_metadata?.id;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshAnilistToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token || !userId) {
				throw new Error('No AniList authentication available');
			}

			const response = await fetch(ANILIST_API, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json',
					Authorization: `Bearer ${token}`
				},
				body: JSON.stringify({
					query: MEDIA_ENTRY_QUERY,
					variables: {
						mediaId: parseInt(anime.id),
						userId: parseInt(userId)
					}
				})
			});

			const data = await response.json();

			if (data.errors && data.data?.MediaList === null) {
				// New anime not in list yet - set default values
				scoreFormat = data.data.Viewer?.mediaListOptions?.scoreFormat || 'POINT_10';
				configureScoreFormat();

				selectedStatus = 'planning';
				anime = {
					...anime,
					status: 'planning',
					score: 0,
					episodeProgress: '',
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};

				isLoading = false;
				return;
			}

			const mediaList = data.data.MediaList;
			scoreFormat = data.data.Viewer?.mediaListOptions?.scoreFormat || 'POINT_10';
			configureScoreFormat();

			const apiStatus = mediaList?.status;
			const mappedStatus = apiStatus ? reverseStatusMap[apiStatus] || reverseStatusMap[apiStatus.toLowerCase()] || 'planning' : 'planning';

			selectedStatus = mappedStatus;

			anime = {
				...anime,
				status: mappedStatus,
				score: mediaList?.score || 0,
				episodeProgress: mediaList?.progress || '',
				startDate: mediaList?.startedAt?.year ? new Date(mediaList.startedAt.year, mediaList.startedAt.month - 1, mediaList.startedAt.day) : null,
				finishDate: mediaList?.completedAt?.year ? new Date(mediaList.completedAt.year, mediaList.completedAt.month - 1, mediaList.completedAt.day) : null,
				totalRewatches: mediaList?.repeat || '',
				notes: mediaList?.notes || ''
			};

			isLoading = false;
		} catch (error) {
			console.error('Error fetching AniList data:', error);
			toast.error('Nie udało się pobrać danych z AniList');

			// Set default values while preserving existing anime data
			anime = {
				...anime,
				status: 'planning',
				score: 0,
				episodeProgress: '',
				startDate: null,
				finishDate: null,
				totalRewatches: '',
				notes: ''
			};
			selectedStatus = 'planning';

			isLoading = false;
			initialFetchError = true;
			throw error;
		}
	}

	async function handleSave() {
		if (isSubmitting) return;
		isSubmitting = true;

		try {
			const provider = $userStore?.user_metadata?.provider;

			if (provider === 'mal') {
				await saveToMAL();
			} else {
				await saveToAniList();
			}
		} catch (error) {
			console.error('Error saving:', error);
			toast.error('Wystąpił błąd podczas zapisywania');
			onSave(anime); // Still call onSave to close dialog even on error
		} finally {
			isSubmitting = false;
		}
	}

	function handleStatusSelect(event) {
		selectedStatus = event.detail;
		anime.status = selectedStatus;
	}

	function increment(field) {
		if (field === 'score') {
			switch (scoreFormat) {
				case 'POINT_100':
					anime.score = Math.min(100, Math.round(anime.score + 1));
					break;
				case 'POINT_10_DECIMAL':
					anime.score = Math.min(10, Math.round((anime.score + 0.1) * 10) / 10);
					break;
				case 'POINT_10':
					anime.score = Math.min(10, Math.round(anime.score + 1));
					break;
				case 'POINT_5':
					anime.score = Math.min(5, Math.round(anime.score + 1));
					break;
				case 'POINT_3':
					anime.score = Math.min(3, Math.round(anime.score + 1));
					break;
			}
		} else {
			const max = field === 'episodeProgress' ? anime.totalEpisodes || Infinity : Infinity;
			anime[field] = Math.min(max, Math.round((parseFloat(anime[field]) || 0) + 1));
		}
		anime = { ...anime };
	}

	function decrement(field) {
		if (field === 'score') {
			switch (scoreFormat) {
				case 'POINT_100':
					anime.score = Math.max(0, Math.round(anime.score - 1));
					break;
				case 'POINT_10_DECIMAL':
					anime.score = Math.max(0, Math.round((anime.score - 0.1) * 10) / 10);
					break;
				case 'POINT_10':
					anime.score = Math.max(0, Math.round(anime.score - 1));
					break;
				case 'POINT_5':
					anime.score = Math.max(0, Math.round(anime.score - 1));
					break;
				case 'POINT_3':
					anime.score = Math.max(0, Math.round(anime.score - 1));
					break;
			}
		} else if (anime[field] > 0) {
			anime[field] = Math.max(0, Math.round((parseFloat(anime[field]) || 0) - 1));
		}
		anime = { ...anime };
	}

	function validateInput(field, value) {
		if (field === 'score') {
			// Allow decimal input for decimal formats
			if (scoreFormat === 'POINT_10_DECIMAL') {
				// Remove any non-numeric characters except dot
				const cleanedValue = value.replace(/[^0-9.]/g, '');

				// Split into integer and decimal parts
				const parts = cleanedValue.split('.');

				// Ensure only one decimal point
				if (parts.length > 2) {
					return;
				}

				// Special case handling
				if (cleanedValue === '' || cleanedValue === '.') {
					anime.score = 0;
					return;
				}

				// Handle cases like '5.', '5.1', '5.12'
				if (parts.length === 2) {
					const intPart = parseInt(parts[0]);
					const decimalPart = parts[1];

					// Ensure integer part is valid
					if (intPart > 10) {
						return;
					}

					// If decimal part is longer than 1 character, truncate
					const formattedDecimal = decimalPart.slice(0, 1);

					// Combine parts while preventing over 10
					const combinedScore = formattedDecimal ? Number(`${intPart}.${formattedDecimal}`) : intPart;

					// Use Number() and toFixed() to handle precision issues
					anime.score = Math.min(10, Math.max(0, Number(combinedScore.toFixed(1))));
				} else {
					// Handle whole number input
					const numValue = parseFloat(cleanedValue);
					anime.score = Math.min(10, Math.max(0, Number(numValue.toFixed(1))));
				}
			} else {
				const numValue = parseFloat(value);
				if (isNaN(numValue)) {
					anime.score = 0;
				} else {
					switch (scoreFormat) {
						case 'POINT_100':
							anime.score = Math.min(100, Math.max(0, numValue));
							break;
						case 'POINT_10':
							anime.score = Math.min(10, Math.max(0, numValue));
							break;
						case 'POINT_5':
							anime.score = Math.min(5, Math.max(0, numValue));
							break;
						case 'POINT_3':
							anime.score = Math.min(3, Math.max(0, numValue));
							break;
					}
				}
			}
			anime = { ...anime };
		} else {
			const max = field === 'episodeProgress' ? anime.totalEpisodes || Infinity : Infinity;
			const numValue = parseFloat(value);
			anime[field] = isNaN(numValue) ? 0 : Math.min(max, Math.max(0, Math.round(numValue)));
		}
		anime = { ...anime };
	}

	function handleInputKeyDown(event, field) {
		if (event.key === 'ArrowUp') {
			event.preventDefault();
			increment(field);
		} else if (event.key === 'ArrowDown') {
			event.preventDefault();
			decrement(field);
		}
	}
</script>

<Dialog.Root
	{open}
	onOpenChange={(newOpen) => {
		open = newOpen;
		if (!newOpen) handleModalClose();
	}}
>
	<Dialog.Content class="flex max-h-[90vh] w-full max-w-[90vw] flex-col overflow-hidden bg-gray-900 p-0 text-white md:block md:max-h-none md:max-w-3xl" bind:this={modalElement}>
		{#if isLoading}
			<div class="flex items-center justify-center p-8">
				<div class="animate-spin">
					<!-- A	dd a loading spinner or icon -->
					<svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
						<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
					</svg>
				</div>
			</div>
		{:else}
			<div class="relative h-44 shrink-0 bg-cover bg-center sm:h-48" style="background-image: url({anime.background});">
				<div class="absolute inset-0 bg-black opacity-60"></div>
				<img src={anime.poster} alt={anime.title} class="absolute bottom-0 left-4 z-10 -mb-8 w-24 rounded-lg shadow-lg sm:-mb-12 sm:w-32 md:z-0" />
				<h2 class="brand-font title-truncate absolute bottom-0 left-32 mb-2 font-sans text-lg font-bold sm:left-40 sm:text-2xl md:right-2">
					{anime.title}
				</h2>
				<Dialog.Close>
					<Button class="absolute top-2 right-2 z-50 cursor-pointer rounded-full bg-transparent p-2 text-white hover:bg-gray-700" on:click={onClose}>
						<X size={24} class="cursor-pointer rounded-full bg-transparent text-white hover:bg-gray-700" />
					</Button>
				</Dialog.Close>
			</div>
			<div class="grow overflow-y-auto md:overflow-visible">
				<div class="p-4 pt-8 md:p-10 md:pt-16">
					<div class="mb-6 grid grid-cols-1 gap-4 md:mb-8 md:grid-cols-3 md:gap-8">
						<div>
							<label for="status" class="mb-2 block text-sm font-medium">Status</label>
							<div class="relative">
								<select
									id="status"
									bind:value={selectedStatus}
									on:change={() => {
										anime.status = selectedStatus;
									}}
									class="h-10 w-full cursor-pointer appearance-none rounded-md border border-white/10 bg-slate-950 px-3 py-2 text-sm text-white transition-colors outline-none focus:border-white/20 focus:ring-1 focus:ring-white/20"
								>
									<option value="watching">Oglądane</option>
									<option value="completed">Ukończone</option>
									<option value="on-hold">Wstrzymane</option>
									<option value="dropped">Porzucone</option>
									<option value="planning">Planowane</option>
									<option value="repeating">Powtarzane</option>
								</select>
								<div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
									<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
										<polyline points="6 9 12 15 18 9"></polyline>
									</svg>
								</div>
							</div>
						</div>
						<div>
							<label for="score" class="mb-2 block text-sm font-medium">Ocena</label>
							<div class="relative">
								{#if showSmileys}
									<div class="flex w-full justify-between gap-2">
										{#each [{ icon: Frown, color: 'text-red-500', score: 1 }, { icon: Meh, color: 'text-yellow-500', score: 2 }, { icon: Smile, color: 'text-green-500', score: 3 }] as { icon, color, score }}
											<button
												class="flex-1 p-2 {anime.score === score ? 'bg-gray-700' : 'bg-gray-800'} flex items-center justify-center rounded-lg"
												on:click={() => (anime.score = score)}
												on:keydown={(e) => {
													if (e.key === 'Enter' || e.key === ' ') {
														anime.score = score;
													}
												}}
											>
												<svelte:component this={icon} size={24} class="{color} {anime.score === score ? '' : ''}" />
											</button>
										{/each}
									</div>
								{:else if showStars}
									<div class="flex gap-1">
										{#each Array(5) as _, i}
											<button
												class="cursor-pointer p-2 transition-colors hover:scale-110"
												on:click={() => (anime.score = i + 1)}
												on:keydown={(e) => {
													if (e.key === 'Enter' || e.key === ' ') {
														anime.score = i + 1;
													}
												}}
											>
												<!-- svelte-ignore missing-declaration -->
												<Star class="{anime.score > i ? 'fill-yellow-400 text-yellow-400' : 'text-gray-600'} transition-colors hover:text-yellow-300" size={20} />
											</button>
										{/each}
									</div>
								{:else}
									<Input type="text" id="score" bind:value={anime.score} on:input={(e) => validateInput('score', e.target.value)} on:blur={(e) => validateInput('score', e.target.value)} on:keydown={(e) => handleInputKeyDown(e, 'score')} min="0" max={maxScore} {step} class="py-3 pr-10 pl-4 md:py-4" />
									<div class="absolute inset-y-0 right-0 flex flex-col">
										<Button variant="ghost" size="icon" class="h-1/2 cursor-pointer" on:click={() => increment('score')} tabindex="-1">
											<ChevronUp class="h-4 w-4" />
										</Button>
										<Button variant="ghost" size="icon" class="h-1/2 cursor-pointer" on:click={() => decrement('score')} tabindex="-1">
											<ChevronDown class="h-4 w-4" />
										</Button>
									</div>
								{/if}
							</div>
						</div>
						<div>
							<label for="episodeProgress" class="mb-2 block text-sm font-medium">Postęp odcinków</label>
							<div class="relative">
								<Input type="text" id="episodeProgress" bind:value={anime.episodeProgress} on:input={(e) => validateInput('episodeProgress', e.target.value)} on:blur={(e) => validateInput('episodeProgress', e.target.value)} on:keydown={(e) => handleInputKeyDown(e, 'episodeProgress')} min="0" max={anime.totalEpisodes || undefined} class="py-3 pr-10 pl-4 md:py-4" />
								<div class="absolute inset-y-0 right-0 flex flex-col">
									<Button variant="ghost" size="icon" class="h-1/2 cursor-pointer" on:click={() => increment('episodeProgress')} tabindex="-1">
										<ChevronUp class="h-4 w-4" />
									</Button>
									<Button variant="ghost" size="icon" class="h-1/2 cursor-pointer" on:click={() => decrement('episodeProgress')} tabindex="-1">
										<ChevronDown class="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div>
							<label for="startDate" class="mb-2 block text-sm font-medium">Data rozpoczęcia</label>
							<Popover.Root>
								<Popover.Trigger asChild let:builder>
									<Button variant="outline" class={cn('w-full cursor-pointer justify-start text-left font-normal', !anime.startDate && 'text-muted-foreground')} builders={[builder]}>
										<CalendarIcon class="mr-2 h-4 w-4" />
										{anime.startDate ? df.format(new Date(anime.startDate)) : 'Wybierz datę rozpoczęcia'}
									</Button>
								</Popover.Trigger>
								<Popover.Content class="w-auto p-0">
									<Calendar bind:value={anime.startDate} initialFocus />
								</Popover.Content>
							</Popover.Root>
						</div>
						<div>
							<label for="finishDate" class="mb-2 block text-sm font-medium">Data zakończenia</label>
							<Popover.Root>
								<Popover.Trigger asChild let:builder>
									<Button variant="outline" class={cn('w-full cursor-pointer justify-start text-left font-normal', !anime.finishDate && 'text-muted-foreground')} builders={[builder]}>
										<CalendarIcon class="mr-2 h-4 w-4" />
										{anime.finishDate ? df.format(new Date(anime.finishDate)) : 'Wybierz datę zakończenia'}
									</Button>
								</Popover.Trigger>
								<Popover.Content class="w-auto p-0">
									<Calendar bind:value={anime.finishDate} initialFocus />
								</Popover.Content>
							</Popover.Root>
						</div>
						<div>
							<label for="totalRewatches" class="mb-2 block text-sm font-medium">Liczba ponownych obejrzeń</label>
							<div class="relative">
								<Input type="text" id="totalRewatches" bind:value={anime.totalRewatches} on:input={(e) => validateInput('totalRewatches', e.target.value)} on:blur={(e) => validateInput('totalRewatches', e.target.value)} on:keydown={(e) => handleInputKeyDown(e, 'totalRewatches')} min="0" class="py-3 pr-10 pl-4 md:py-4" />
								<div class="absolute inset-y-0 right-0 flex flex-col">
									<Button variant="ghost" size="icon" class="h-1/2 cursor-pointer" on:click={() => increment('totalRewatches')} tabindex="-1">
										<ChevronUp class="h-4 w-4" />
									</Button>
									<Button variant="ghost" size="icon" class="h-1/2 cursor-pointer" on:click={() => decrement('totalRewatches')} tabindex="-1">
										<ChevronDown class="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
					</div>
					<div class="mt-6 md:mt-8">
						<label for="notes" class="mb-2 block text-sm font-medium">Notatki</label>
						<Textarea id="notes" bind:value={anime.notes} rows="3" class="p-3 md:p-4" />
					</div>
					<div class="mt-6 flex justify-between md:mt-8">
						<Button variant="outline" class="w-24 cursor-pointer border-red-800 text-red-300 hover:bg-red-900 hover:text-red-100" on:click={onDelete}>Usuń</Button>
						<Button variant="outline" class="w-24 cursor-pointer border-blue-800 text-blue-300 hover:bg-blue-900 hover:text-blue-100" on:click={handleSave} disabled={isSubmitting}>
							{isSubmitting ? 'Zapisuję...' : 'Zapisz'}
						</Button>
					</div>
				</div>
			</div>
		{/if}
	</Dialog.Content>
</Dialog.Root>

<style>
	.title-truncate {
		display: -webkit-box;
		line-clamp: 2;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		max-height: 3.5em;
	}

	@media (min-width: 640px) {
		.title-truncate {
			line-clamp: 1;
			-webkit-line-clamp: 1;
			max-height: 2.5em;
		}
	}
	.brand-font {
		font-family: 'Bricolage Grotesque', sans-serif;
	}
</style>
