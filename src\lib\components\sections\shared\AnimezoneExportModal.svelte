<script>
    import { createEventDispatcher } from 'svelte';
    import * as Dialog from '$lib/components/ui/dialog';
    import * as Progress from '$lib/components/ui/progress';
    import { Button } from '$lib/components/ui/button';
    import { Input } from '$lib/components/ui/input';
    import { toast } from 'svelte-sonner';
    import { Download, AlertTriangle } from 'lucide-svelte';
    import AnimezoneMatchModal from './AnimezoneMatchModal.svelte';

    export let open = false;

    const dispatch = createEventDispatcher();

    let animezoneUrl = '';
    let isLoading = false;
    let error = '';
    let exportData = null;
    let showMatchModal = false;
    let step = 'input'; // 'input', 'summary'

    function closeModal() {
        open = false;
        animezoneUrl = '';
        error = '';
        exportData = null;
        step = 'input';
        dispatch('close');
    }

    function resetForm() {
        animezoneUrl = '';
        error = '';
        exportData = null;
        step = 'input';
    }

    async function handleExport() {
        if (!animezoneUrl) {
            error = 'Proszę podać URL profilu AnimeZone';
            return;
        }

        // Check if the URL contains the required pattern
        if (!animezoneUrl.includes('animezone.pl/user/')) {
            error = 'Nieprawidłowy URL profilu AnimeZone. Powinien zawierać "animezone.pl/user/"';
            return;
        }

        try {
            isLoading = true;
            error = '';

            const response = await fetch('https://export.lycoris.cafe/api/animezone-export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ animeZoneUrl: animezoneUrl })
            });

            let responseData;
            try {
                responseData = await response.json();
            } catch (jsonError) {
                throw new Error('Nieprawidłowa odpowiedź z serwera. Spróbuj ponownie później.');
            }

            if (!response.ok) {
                throw new Error(responseData.message || 'Nie udało się wyeksportować listy AnimeZone');
            }

            // Use the parsed JSON response
            exportData = responseData;

            // Check if there was an error from the server
            if (exportData.error) {
                error = exportData.errorMessage || 'Wystąpił błąd podczas eksportu listy AnimeZone';
                toast.error(error);
                return;
            }

            // Move to summary step
            step = 'summary';

            if (exportData.success === exportData.total) {
                toast.success('Lista AnimeZone została pomyślnie wyeksportowana!');
            } else {
                toast.info('Niektóre anime nie zostały dopasowane. Możesz je dopasować ręcznie.');
            }
        } catch (err) {
            console.error('Error exporting AnimeZone list:', err);
            error = err.message || 'Nie udało się wyeksportować listy AnimeZone';
        } finally {
            isLoading = false;
        }
    }

    function downloadXml() {
        if (!exportData || !exportData.xml) return;

        // Create a blob from the XML string
        const blob = new Blob([exportData.xml], { type: 'application/xml' });

        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'animezone_export.xml';

        // Add to the DOM and trigger the download
        document.body.appendChild(a);
        a.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success('Plik XML został pobrany!');
    }

    function handleShowMatchModal() {
        showMatchModal = true;
    }

    async function handleSaveMatches(additionalMatches) {
        if (!additionalMatches || additionalMatches.length === 0) return;

        try {
            isLoading = true;

            // Get the existing matches from the initial export
            // We need to reconstruct the format expected by the server
            const existingMatches = [];

            if (exportData && exportData.originalMatches) {
                // Use the original matches if available
                existingMatches.push(...exportData.originalMatches);
            }

            console.log(`Sending ${existingMatches.length} existing matches and ${additionalMatches.length} new matches`);

            // Send both existing and manual matches to the server to generate a new XML
            const response = await fetch('https://export.lycoris.cafe/api/animezone-export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    manualMatches: additionalMatches,
                    existingMatches: existingMatches
                })
            });

            let result;
            try {
                result = await response.json();
            } catch (jsonError) {
                throw new Error('Nieprawidłowa odpowiedź z serwera. Spróbuj ponownie później.');
            }

            if (!response.ok) {
                throw new Error(result?.message || 'Nie udało się zaktualizować dopasowań');
            }

            if (!result || !result.xml) {
                throw new Error('Nieprawidłowa odpowiedź z serwera. Spróbuj ponownie później.');
            }

            // Update the exportData with the new matches and XML
            if (exportData) {
                exportData.success += additionalMatches.length;
                // Only decrease the multiple counter if the match came from multiple matches
                // For failed matches, decrease the failed counter instead
                const fromMultiple = additionalMatches.filter(match =>
                    exportData.multipleMatches.some(m => m.title === match.animeZoneAnime.title)
                ).length;

                const fromFailed = additionalMatches.length - fromMultiple;

                if (fromMultiple > 0) {
                    exportData.multiple -= fromMultiple;
                }

                if (fromFailed > 0) {
                    exportData.failed -= fromFailed;
                }

                exportData.xml = result.xml;

                // Add the new matches to the original matches array
                if (!exportData.originalMatches) {
                    exportData.originalMatches = [];
                }

                exportData.originalMatches.push(...additionalMatches);
            }

            toast.success('Dopasowania zostały zaktualizowane!');
        } catch (err) {
            console.error('Error updating matches:', err);
            toast.error('Nie udało się zaktualizować dopasowań');
        } finally {
            isLoading = false;
            // Don't close the modal automatically to allow users to continue matching
            // showMatchModal = false;
        }
    }

    $: hasUnmatchedAnime = exportData && exportData.failed > 0;
</script>

<Dialog.Root bind:open on:close={closeModal}>
    <Dialog.Content class="w-[95vw] sm:max-w-[500px] max-h-[85vh] overflow-y-auto">
        <Dialog.Header>
            <Dialog.Title>Eksport listy AnimeZone</Dialog.Title>
            <Dialog.Description class="text-xs sm:text-sm">
                Wyeksportuj swoją listę anime z AnimeZone do formatu XML, który możesz zaimportować do AniList lub MyAnimeList.
            </Dialog.Description>
        </Dialog.Header>

        {#if step === 'input'}
            <div class="py-4 space-y-4">
                <div class="space-y-2">
                    <label for="animezone-url" class="text-sm font-medium">URL profilu AnimeZone</label>
                    <Input
                        id="animezone-url"
                        type="text"
                        placeholder="https://animezone.pl/user/username"
                        bind:value={animezoneUrl}
                        disabled={isLoading}
                    />
                    {#if error}
                        <p class="text-sm text-red-500">{error}</p>
                    {/if}
                </div>

                <div class="p-4 bg-gray-800 rounded-md">
                    <h3 class="mb-2 font-medium">Jak to działa?</h3>
                    <p class="mb-2 text-xs text-gray-300 sm:text-sm">
                        Pełna integracja z AnimeZone byłaby czasochłonna (jeżeli w ogóle możliwa) oraz podatna na częste awarie, dlatego udostępniamy <strong>eksport</strong> twojej listy anime z AnimeZone do formatu XML.
                    </p>
                    <ol class="pl-5 space-y-1 text-xs text-gray-300 list-decimal sm:text-sm">
                        <li>Wklej URL do swojego profilu AnimeZone</li>
                        <li>Kliknij "Eksportuj" aby pobrać plik XML</li>
                        <li>Zaimportuj pobrany plik XML do AniList lub MyAnimeList</li>
                    </ol>
                </div>

                <div class="p-4 bg-gray-800 rounded-md">
                    <h3 class="mb-2 font-medium">Jak zaimportować do AniList?</h3>
                    <ol class="pl-5 space-y-1 text-xs text-gray-300 list-decimal sm:text-sm">
                        <li>Zaloguj się</li>
                        <li>Przejdź do <a href="https://anilist.co/settings/import" target="_blank" rel="noopener noreferrer" class="text-blue-400 break-words hover:underline">https://anilist.co/settings/import</a></li>
                        <li>Wybierz opcję "MyAnimeList"</li>
                        <li>Prześlij pobrany plik XML</li>
                    </ol>
                </div>
            </div>

            <Dialog.Footer class="flex flex-col-reverse gap-2 sm:flex-row sm:gap-0">
                <Button variant="outline" on:click={closeModal} class="w-full cursor-pointer sm:w-auto">Anuluj</Button>
                <Button
                    on:click={handleExport}
                    class="bg-[#ee8585] hover:bg-[#8ec3f4] text-black cursor-pointer w-full sm:w-auto"
                    disabled={isLoading}
                >
                    {#if isLoading}
                        <div class="flex items-center">
                            <div class="w-4 h-4 mr-2 border-2 border-black rounded-full animate-spin border-t-transparent"></div>
                            Może to potrwać kilka minut...
                        </div>
                    {:else}
                        Eksportuj
                    {/if}
                </Button>
            </Dialog.Footer>
        {:else if step === 'summary'}
            <div class="py-4 space-y-4">
                <div class="p-4 bg-gray-800 rounded-md">
                    <h3 class="mb-4 font-medium">Podsumowanie eksportu</h3>

                    <div class="mb-4">
                        <div class="flex justify-between mb-1 text-sm">
                            <span>Dopasowane anime: {exportData.success} z {exportData.total}</span>
                            <span>{Math.round((exportData.success / exportData.total) * 100)}%</span>
                        </div>
                        <Progress.Root value={Math.round((exportData.success / exportData.total) * 100)} class="h-2">
                            <Progress.Indicator
                                style="transform: translateX(-{100 - Math.round((exportData.success / exportData.total) * 100)}%)"
                                class="h-full bg-green-500"
                            />
                        </Progress.Root>
                    </div>

                    <div class="grid grid-cols-2 gap-1 text-center sm:gap-2">
                        <div class="p-1 bg-gray-700 rounded sm:p-2">
                            <div class="text-lg font-bold text-green-500 sm:text-xl">{exportData.success}</div>
                            <div class="text-[10px] sm:text-xs text-gray-300">Dopasowane</div>
                        </div>
                        <div class="p-1 bg-gray-700 rounded sm:p-2">
                            <div class="text-lg font-bold text-red-500 sm:text-xl">{exportData.failed}</div>
                            <div class="text-[10px] sm:text-xs text-gray-300">Brak dopasowań</div>
                        </div>
                    </div>
                </div>

                {#if hasUnmatchedAnime}
                    <div class="p-3 border border-yellow-700 rounded-md sm:p-4 bg-yellow-900/50">
                        <div class="flex items-start">
                            <AlertTriangle class="w-4 sm:w-5 h-4 sm:h-5 mr-2 text-yellow-500 flex-shrink-0 mt-0.5" />
                            <div>
                                <h3 class="mb-1 text-sm font-medium text-yellow-500 sm:text-base">Uwaga: Niekompletny eksport</h3>
                                <p class="text-xs text-gray-300 sm:text-sm">
                                    {exportData.failed} anime z twojej listy nie zostało automatycznie dopasowanych.
                                    Możesz ręcznie dopasować anime, aby uwzględnić je w eksporcie.
                                </p>
                                <Button
                                    on:click={handleShowMatchModal}
                                    variant="outline"
                                    class="mt-2 text-xs border-[#ee8585] text-[#ee8585] cursor-pointer hover:bg-[#ee8585]/10 w-full sm:w-auto"
                                >
                                    Dopasuj ręcznie
                                </Button>
                            </div>
                        </div>
                    </div>
                {/if}



                <div class="p-3 bg-gray-800 rounded-md sm:p-4">
                    <h3 class="mb-2 font-medium">Co dalej?</h3>
                    <p class="mb-2 text-xs text-gray-300 sm:text-sm">
                        Pobierz plik XML i zaimportuj go do AniList lub MyAnimeList.
                    </p>
                    <ol class="pl-5 space-y-1 text-xs text-gray-300 list-decimal sm:text-sm">
                        <li>Kliknij przycisk "Pobierz XML" poniżej</li>
                        <li>Przejdź do <a href="https://anilist.co/settings/import" target="_blank" rel="noopener noreferrer" class="text-blue-400 break-words hover:underline">https://anilist.co/settings/import</a></li>
                        <li>Wybierz opcję "MyAnimeList" i prześlij pobrany plik</li>
                    </ol>
                </div>
            </div>

            <Dialog.Footer class="flex flex-col-reverse gap-2 sm:flex-row sm:gap-0">
                <Button variant="outline" on:click={resetForm} class="w-full cursor-pointer sm:w-auto">Wróć</Button>
                <Button
                    on:click={downloadXml}
                    class="bg-[#ee8585] hover:bg-[#8ec3f4] text-black cursor-pointer w-full sm:w-auto"
                >
                    <Download class="w-4 h-4 mr-2" />
                    Pobierz XML
                </Button>
            </Dialog.Footer>
        {/if}
    </Dialog.Content>
</Dialog.Root>

<AnimezoneMatchModal
    bind:open={showMatchModal}
    multipleMatches={exportData?.multipleMatches || []}
    failedMatches={exportData?.failedMatches || []}
    onSave={handleSaveMatches}
/>
