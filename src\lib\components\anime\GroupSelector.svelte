<script>
	import { createEventDispatcher, onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { userStore } from '$lib/stores/userLogin';
	import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '$lib/components/ui/dialog';
	import { toast } from 'svelte-sonner';

	export let otherGroups = null;
	export let episodeId = null;
	export let selectedGroup = null;
	export let episode = null;
	export let isMobile = false;

	const dispatch = createEventDispatcher();

	// Admin state
	let isAdmin = false;
	let editMode = false;

	// Dialog states
	let showAddPlayerDialog = false;
	let showDeleteConfirmDialog = false;

	// Data
	let allGroups = {};
	let hasSecondarySource = false;

	// Form data
	let newPlayerUrl = '';
	let selectedGroupForNewPlayer = '';
	let selectedQuality = 'HD';
	let selectedAudioLanguage = 'jp';
	let selectedSubtitleLanguage = 'pl';
	let playerToDelete = { groupName: '', playerUrl: '' };

	// Check admin status
	function checkAdminStatus() {
		const hasAdminRole = $userStore?.user_metadata?.profile?.role === 'admin';
		isAdmin = hasAdminRole;
	}

	// Watch for user store changes
	$: if ($userStore) {
		checkAdminStatus();
	}

	// Initialize data
	onMount(() => {
		checkAdminStatus();
		createGroupsList();
	});

	function createGroupsList() {
		if (episode?.secondarySource) {
			hasSecondarySource = true;
			allGroups = { ...episode.allGroups };
		} else if (otherGroups) {
			allGroups = { ...otherGroups };
		}
	}

	function getSortedGroupEntries(groups) {
		if (!groups) return [];
		return Object.entries(groups).sort(([a], [b]) => {
			if (a === 'lycoris.cafe') return -1;
			if (b === 'lycoris.cafe') return 1;
			return a.localeCompare(b);
		});
	}

	function selectGroup(groupName) {
		selectedGroup = groupName;
		editMode = false;

		if (groupName === 'lycoris.cafe') {
			dispatch('select', {
				group: groupName,
				playerInfo: {
					type: 'lycoris.cafe',
					url: null
				}
			});
		} else if (allGroups[groupName]?.players?.length > 0) {
			const firstPlayer = allGroups[groupName].players[0];
			const playerType = Object.keys(firstPlayer).find(key => 
				key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language'
			);
			const playerUrl = firstPlayer[playerType];

			dispatch('select', {
				group: groupName,
				playerInfo: {
					type: playerType,
					url: playerUrl
				}
			});
		}
	}

	function selectSpecificPlayer(playerType, playerUrl, groupName) {
		dispatch('select', {
			group: groupName,
			playerInfo: {
				type: playerType,
				url: playerUrl
			}
		});
	}

	function toggleEditMode() {
		if (selectedGroup) {
			editMode = false;
			return;
		}
		editMode = !editMode;
	}

	function openAddPlayerDialog(groupName) {
		selectedGroupForNewPlayer = groupName;
		newPlayerUrl = '';
		selectedQuality = 'HD';
		selectedAudioLanguage = 'jp';
		selectedSubtitleLanguage = 'pl';
		showAddPlayerDialog = true;
	}

	async function addPlayer() {
		if (!newPlayerUrl.trim() || !selectedGroupForNewPlayer) {
			toast.error('Proszę wypełnić wszystkie pola');
			return;
		}

		try {
			const response = await fetch('/api/admin/add-player', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify({
					episodeId,
					groupName: selectedGroupForNewPlayer,
					playerUrl: newPlayerUrl.trim(),
					quality: selectedQuality,
					audioLanguage: selectedAudioLanguage,
					subtitleLanguage: selectedSubtitleLanguage
				})
			});

			if (response.ok) {
				toast.success('Player został dodany pomyślnie');
				showAddPlayerDialog = false;
				
				// Update local data
				if (!allGroups[selectedGroupForNewPlayer]) {
					allGroups[selectedGroupForNewPlayer] = { players: [] };
				}
				
				const newPlayer = {};
				newPlayer[newPlayerUrl.includes('dailymotion') ? 'dailymotion' : 'vk'] = newPlayerUrl.trim();
				newPlayer.quality = selectedQuality;
				newPlayer.audio_language = selectedAudioLanguage;
				newPlayer.subtitle_language = selectedSubtitleLanguage;
				
				allGroups[selectedGroupForNewPlayer].players.push(newPlayer);
				allGroups = { ...allGroups };
				
				dispatch('groupsUpdated', { allGroups });
			} else {
				const errorData = await response.json();
				toast.error(errorData.error || 'Błąd podczas dodawania playera');
			}
		} catch (error) {
			console.error('Error adding player:', error);
			toast.error('Błąd podczas dodawania playera');
		}
	}

	function confirmDeletePlayer(groupName, playerUrl) {
		playerToDelete = { groupName, playerUrl };
		showDeleteConfirmDialog = true;
	}

	async function deletePlayer() {
		try {
			const response = await fetch('/api/admin/delete-player', {
				method: 'DELETE',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify({
					episodeId,
					groupName: playerToDelete.groupName,
					playerUrl: playerToDelete.playerUrl
				})
			});

			if (response.ok) {
				toast.success('Player został usunięty pomyślnie');
				showDeleteConfirmDialog = false;

				// Update local data
				if (allGroups[playerToDelete.groupName]) {
					allGroups[playerToDelete.groupName].players = allGroups[playerToDelete.groupName].players.filter((player) => {
						const playerType = Object.keys(player).find((key) => 
							key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language'
						);
						return player[playerType] !== playerToDelete.playerUrl;
					});

					if (allGroups[playerToDelete.groupName].players.length === 0) {
						delete allGroups[playerToDelete.groupName];
					}

					allGroups = { ...allGroups };
					dispatch('groupsUpdated', { allGroups });
				}
			} else {
				const errorData = await response.json();
				toast.error(errorData.error || 'Błąd podczas usuwania playera');
			}
		} catch (error) {
			console.error('Error deleting player:', error);
			toast.error('Błąd podczas usuwania playera');
		}
	}

	function preservePlayerName(playerType) {
		const playerNames = {
			'dailymotion': 'Dailymotion',
			'vk': 'VK',
			'lycoris.cafe': 'lycoris.cafe'
		};
		return playerNames[playerType] || playerType;
	}
</script>

{#if (allGroups && Object.keys(allGroups).length > 0) || (otherGroups && Object.keys(otherGroups).length > 0)}
	{#if isMobile}
		<!-- Mobile Layout -->
		<div class="group-selector-mobile w-full bg-gray-900 rounded-lg">
			<!-- Header -->
			<div class="p-4 bg-gray-700 rounded-t-lg">
				<h2 class="text-lg font-bold text-center text-white">Wybierz grupę tłumaczeniową</h2>
				{#if isAdmin && !selectedGroup}
					<div class="flex justify-center mt-2">
						<Button variant={editMode ? 'default' : 'outline'} size="sm" on:click={toggleEditMode} class="hover:cursor-pointer hover:bg-gray-600">
							{editMode ? 'Zakończ edycję' : 'Edytuj'}
						</Button>
					</div>
				{/if}
			</div>

			<!-- Mobile Groups List -->
			<div class="p-4 space-y-3">
				{#each getSortedGroupEntries(hasSecondarySource ? allGroups : otherGroups || {}) as [groupName, groupData]}
					<div class="bg-gray-800 rounded-lg overflow-hidden {selectedGroup === groupName ? 'ring-2 ring-[#8ec3f4]' : ''}">
						<!-- Main Group Card -->
						<div
							class="p-4 cursor-pointer transition-all duration-200 {selectedGroup === groupName ? 'bg-[#8ec3f4]/20' : 'hover:bg-gray-700'}"
							on:click={() => !editMode && selectGroup(groupName)}
						>
							<div class="flex items-center justify-between">
								<div class="flex items-center space-x-3">
									<div class="w-10 h-10 bg-gray-700 rounded-full overflow-hidden flex items-center justify-center">
										<img src="/android-chrome-192x192.png" alt="{groupName} logo" class="w-full h-full object-cover" />
									</div>
									<div>
										<h3 class="font-semibold text-white text-base">{groupName}</h3>
										<div class="flex items-center space-x-2 mt-1">
											<span class="px-2 py-1 text-xs bg-blue-900 text-white rounded">
												{#if groupName === 'lycoris.cafe'}HD{:else if groupData.players && groupData.players.length > 0}{groupData.players[0].quality || 'HD'}{/if}
											</span>
											<div class="flex items-center space-x-1">
												<img src="/jp.svg" alt="Japanese" class="w-4 h-3" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
												<span class="text-xs text-gray-300">JP</span>
											</div>
											<div class="flex items-center space-x-1">
												<img src="/pl.svg" alt="Polish" class="w-4 h-3" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
												<span class="text-xs text-gray-300">PL</span>
											</div>
										</div>
									</div>
								</div>
								<div class="flex items-center space-x-2">
									{#if editMode && isAdmin && !selectedGroup && groupName !== 'lycoris.cafe'}
										<Button
											variant="outline"
											size="sm"
											on:click={(e) => {
												e.stopPropagation();
												openAddPlayerDialog(groupName);
											}}
											class="text-xs"
										>
											Dodaj
										</Button>
									{:else}
										<Button
											variant={selectedGroup === groupName ? 'default' : 'outline'}
											size="sm"
											class="{selectedGroup === groupName ? 'bg-[#8ec3f4] text-black hover:bg-[#8ec3f4]/80' : ''}"
											on:click={(e) => {
												e.stopPropagation();
												selectGroup(groupName);
											}}
										>
											{selectedGroup === groupName ? 'Wybrano' : 'Wybierz'}
										</Button>
									{/if}
								</div>
							</div>
						</div>

						<!-- Players List for Mobile -->
						{#if groupName !== 'lycoris.cafe' && groupData.players && groupData.players.length > 0}
							<div class="border-t border-gray-700">
								{#each groupData.players as player}
									{@const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language')}
									{@const playerUrl = player[playerType]}
									<div
										class="p-3 pl-6 bg-gray-750 border-b border-gray-700 last:border-b-0 cursor-pointer hover:bg-gray-700 transition-colors"
										on:click={() => !editMode && selectSpecificPlayer(playerType, playerUrl, groupName)}
									>
										<div class="flex items-center justify-between">
											<div class="flex items-center space-x-2">
												<span class="text-sm text-white">{preservePlayerName(playerType)}</span>
												<span class="px-2 py-1 text-xs bg-blue-900 text-white rounded">{player.quality || 'HD'}</span>
											</div>
											<div class="flex items-center space-x-2">
												{#if editMode && isAdmin && !selectedGroup}
													<Button
														variant="destructive"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															confirmDeletePlayer(groupName, playerUrl);
														}}
														class="text-xs"
													>
														Usuń
													</Button>
												{:else}
													<Button
														variant="outline"
														size="sm"
														class="text-xs"
														on:click={(e) => {
															e.stopPropagation();
															selectSpecificPlayer(playerType, playerUrl, groupName);
														}}
													>
														Wybierz
													</Button>
												{/if}
											</div>
										</div>
									</div>
								{/each}
							</div>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	{:else}
		<!-- Desktop Layout -->
		<div class="group-selector mx-auto my-4 flex max-h-[85vh] min-h-[500px] flex-col px-6 py-4 sm:px-16 sm:py-8 scrollable mb-6 w-full max-w-5xl min-w-0 overflow-y-auto rounded-lg bg-gray-900 transition-all duration-300">
			<!-- Header -->
			<div class="flex-shrink-0 min-w-0 p-4 mb-2 overflow-hidden bg-gray-700 rounded-lg shadow-lg sm:p-6">
				<div class="flex flex-col items-center justify-between gap-3 {isAdmin && !selectedGroup ? 'sm:mb-2 sm:flex-row' : 'sm:flex-col'} min-w-0 sm:items-center">
					<h2 class="text-xl font-bold text-white sm:text-2xl {isAdmin && !selectedGroup ? '' : 'text-center'}">Wybierz grupę tłumaczeniową</h2>
					{#if isAdmin && !selectedGroup}
						<div class="flex justify-end gap-2">
							<Button variant={editMode ? 'default' : 'outline'} size="sm" on:click={toggleEditMode} class="hover:cursor-pointer hover:bg-gray-600">
								{editMode ? 'Zakończ edycję' : 'Edytuj'}
							</Button>
						</div>
					{/if}
				</div>
			</div>

			<!-- Groups List -->
			<div class="flex flex-col flex-1 min-w-0 py-4 overflow-x-hidden">
				<div class="min-w-0 p-2">
					<table class="w-full min-w-0 overflow-hidden border-collapse rounded-lg">
						<thead class="sticky top-0 z-10 bg-black">
							<tr>
								<th class="p-3 font-medium text-left text-gray-400">Grupa</th>
								<th class="p-3 font-medium text-center text-gray-400">Jakość</th>
								<th class="p-3 font-medium text-center text-gray-400">Audio</th>
								<th class="p-3 font-medium text-center text-gray-400">Napisy</th>
								<th class="p-3 font-medium text-right text-gray-400">
									{#if editMode && isAdmin}
										Akcje
									{/if}
								</th>
							</tr>
						</thead>
						<tbody>
							{#each getSortedGroupEntries(hasSecondarySource ? allGroups : otherGroups || {}) as [groupName, groupData]}
								<tr class="group transition-all duration-300 hover:cursor-pointer {selectedGroup === groupName ? 'bg-[#8ec3f4]/80' : 'bg-gray-700 hover:bg-gray-600'}" on:click={() => !editMode && selectGroup(groupName)}>
									<!-- Group Logo and Name -->
									<td class="p-3 border-b border-gray-800">
										<div class="flex items-center">
											<div class="flex items-center justify-center w-10 h-10 mr-3 overflow-hidden bg-gray-800 rounded-full">
												<img src="/android-chrome-192x192.png" alt="{groupName} logo" class="object-cover w-full h-full" />
											</div>
											<span class="text-lg font-medium text-white">{groupName}</span>
										</div>
									</td>

									<!-- Quality -->
									<td class="p-3 text-center border-b border-gray-800">
										{#if groupName === 'lycoris.cafe'}
											<span class="px-2 py-1 text-xs text-white bg-blue-900 rounded">HD</span>
										{:else if groupData.players && groupData.players.length > 0}
											<span class="px-2 py-1 text-xs text-white bg-blue-900 rounded">{groupData.players[0].quality || 'HD'}</span>
										{/if}
									</td>

									<!-- Audio -->
									<td class="p-3 text-center border-b border-gray-800">
										<div class="flex items-center justify-center">
											<div class="relative w-6 h-4 mr-1">
												<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
											</div>
											<span class="text-sm text-white">JP</span>
										</div>
									</td>

									<!-- Subtitles -->
									<td class="p-3 text-center border-b border-gray-800">
										<div class="flex items-center justify-center">
											<div class="relative w-6 h-4 mr-1">
												<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
											</div>
											<span class="text-sm text-white">PL</span>
										</div>
									</td>

									<!-- Actions -->
									<td class="p-3 text-right border-b border-gray-800">
										{#if editMode && isAdmin && !selectedGroup}
											<div class="flex justify-end gap-2">
												{#if groupName !== 'lycoris.cafe'}
													<Button
														variant="outline"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															openAddPlayerDialog(groupName);
														}}
														class="hover:cursor-pointer"
													>
														Dodaj player
													</Button>
												{/if}
											</div>
										{:else}
											<Button
												variant={selectedGroup === groupName ? 'default' : 'outline'}
												size="sm"
												class="hover:cursor-pointer {selectedGroup === groupName ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
												on:click={(e) => {
													e.stopPropagation();
													selectGroup(groupName);
												}}
												aria-label="Wybierz grupę {groupName}"
												aria-pressed={selectedGroup === groupName}
											>
												{selectedGroup === groupName ? 'Wybrano' : 'Wybierz'}
											</Button>
										{/if}
									</td>
								</tr>

								<!-- Show players for non-lycoris.cafe groups -->
								{#if groupName !== 'lycoris.cafe' && groupData.players && groupData.players.length > 0}
									{#each groupData.players as player}
										{@const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language')}
										{@const playerUrl = player[playerType]}
										{@const playerQuality = player.quality || 'HD'}
										{@const playerAudio = player.audio_language || 'jp'}
										{@const playerSubtitle = player.subtitle_language || 'pl'}
										<tr class="transition-all duration-300 bg-gray-600 hover:cursor-pointer hover:bg-gray-550" on:click={() => !editMode && selectSpecificPlayer(playerType, playerUrl, groupName)}>
											<td class="p-3 pl-12 border-b border-black">
												<span class="text-white">{preservePlayerName(playerType)}</span>
											</td>
											<td class="p-3 text-center border-b border-black">
												<span class="px-2 py-1 text-xs text-white bg-blue-900 rounded">{playerQuality}</span>
											</td>
											<td class="p-3 text-center border-b border-black">
												<div class="flex items-center justify-center">
													<div class="relative w-6 h-4 mr-1">
														<img src="/{playerAudio}.svg" alt={playerAudio === 'jp' ? 'Japanese' : 'Chinese'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
													</div>
													<span class="text-sm text-white">{playerAudio.toUpperCase()}</span>
												</div>
											</td>
											<td class="p-3 text-center border-b border-black">
												<div class="flex items-center justify-center">
													<div class="relative w-6 h-4 mr-1">
														<img src="/{playerSubtitle}.svg" alt={playerSubtitle === 'pl' ? 'Polish' : 'English'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
													</div>
													<span class="text-sm text-white">{playerSubtitle.toUpperCase()}</span>
												</div>
											</td>
											<td class="p-3 text-right border-b border-black">
												{#if editMode && isAdmin && !selectedGroup}
													<Button
														variant="destructive"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															confirmDeletePlayer(groupName, playerUrl);
														}}
														class="hover:cursor-pointer"
													>
														Usuń
													</Button>
												{:else if !editMode}
													<Button
														variant="outline"
														size="sm"
														class="hover:cursor-pointer"
														on:click={(e) => {
															e.stopPropagation();
															selectSpecificPlayer(playerType, playerUrl, groupName);
														}}
														aria-label="Wybierz player {preservePlayerName(playerType)} dla grupy {groupName}"
													>
														Wybierz
													</Button>
												{/if}
											</td>
										</tr>
									{/each}
								{/if}
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	{/if}
{:else}
	<div class="flex items-center justify-center p-8 text-gray-400">
		<p>Brak dostępnych grup tłumaczeniowych dla tego odcinka</p>
	</div>
{/if}

<!-- Add Player Dialog -->
<Dialog bind:open={showAddPlayerDialog}>
	<DialogContent class="sm:max-w-md">
		<DialogHeader>
			<DialogTitle>Dodaj nowy player</DialogTitle>
		</DialogHeader>
		<div class="grid gap-4 py-4">
			<div class="grid gap-2">
				<label for="player-url">URL playera</label>
				<input
					id="player-url"
					bind:value={newPlayerUrl}
					placeholder="https://..."
					class="flex w-full h-10 px-3 py-2 text-sm border rounded-md border-input bg-background ring-offset-background"
				/>
			</div>
			<div class="grid gap-2">
				<label for="quality">Jakość</label>
				<select bind:value={selectedQuality} class="flex w-full h-10 px-3 py-2 text-sm border rounded-md border-input bg-background ring-offset-background">
					<option value="HD">HD</option>
					<option value="FHD">FHD</option>
					<option value="4K">4K</option>
				</select>
			</div>
			<div class="grid gap-2">
				<label for="audio">Język audio</label>
				<select bind:value={selectedAudioLanguage} class="flex w-full h-10 px-3 py-2 text-sm border rounded-md border-input bg-background ring-offset-background">
					<option value="jp">Japoński</option>
					<option value="cn">Chiński</option>
				</select>
			</div>
			<div class="grid gap-2">
				<label for="subtitle">Język napisów</label>
				<select bind:value={selectedSubtitleLanguage} class="flex w-full h-10 px-3 py-2 text-sm border rounded-md border-input bg-background ring-offset-background">
					<option value="pl">Polski</option>
					<option value="en">Angielski</option>
				</select>
			</div>
		</div>
		<DialogFooter>
			<Button variant="outline" on:click={() => showAddPlayerDialog = false}>Anuluj</Button>
			<Button on:click={addPlayer}>Dodaj</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<!-- Delete Confirmation Dialog -->
<Dialog bind:open={showDeleteConfirmDialog}>
	<DialogContent class="sm:max-w-md">
		<DialogHeader>
			<DialogTitle>Potwierdź usunięcie</DialogTitle>
		</DialogHeader>
		<div class="py-4">
			<p>Czy na pewno chcesz usunąć ten player? Ta akcja jest nieodwracalna.</p>
		</div>
		<DialogFooter>
			<Button variant="outline" on:click={() => showDeleteConfirmDialog = false}>Anuluj</Button>
			<Button variant="destructive" on:click={deletePlayer}>Usuń</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<style>
	.group-selector {
		transition: all 0.3s ease;
		box-sizing: border-box;
		overflow-x: hidden;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
		display: flex;
		flex-direction: column;
	}

	.scrollable {
		scrollbar-width: thin;
		scrollbar-color: #4b5563 #374151;
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}

	.scrollable::-webkit-scrollbar {
		width: 6px;
	}

	.scrollable::-webkit-scrollbar-track {
		background: #374151;
		border-radius: 3px;
	}

	.scrollable::-webkit-scrollbar-thumb {
		background: #4b5563;
		border-radius: 3px;
	}

	.scrollable::-webkit-scrollbar-thumb:hover {
		background: #6b7280;
	}

	.bg-gray-550 {
		background-color: #6b7280;
	}

	.hover\:bg-gray-550:hover {
		background-color: #6b7280;
	}

	.bg-gray-750 {
		background-color: #4a5568;
	}

	/* Mobile-specific styles */
	.group-selector-mobile {
		max-height: 70vh;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}

	.group-selector-mobile::-webkit-scrollbar {
		width: 4px;
	}

	.group-selector-mobile::-webkit-scrollbar-track {
		background: #374151;
		border-radius: 2px;
	}

	.group-selector-mobile::-webkit-scrollbar-thumb {
		background: #4b5563;
		border-radius: 2px;
	}

	.group-selector-mobile::-webkit-scrollbar-thumb:hover {
		background: #6b7280;
	}
</style>
