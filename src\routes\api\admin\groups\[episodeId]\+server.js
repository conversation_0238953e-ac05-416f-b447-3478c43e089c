// src/routes/api/admin/groups/[episodeId]/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminOrMod } from '$lib/utils/roleUtils';

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ params, locals }) {
  // Check if user is authenticated
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is an admin or moderator
  const hasAdminRole = await isAdminOrMod(user.id);

  if (!hasAdminRole) {
    return json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    const episodeId = params.episodeId;

    if (!episodeId) {
      return json({ error: 'Missing episode ID' }, { status: 400 });
    }

    // Get the episode data from anime_new table
    const { data, error } = await supabase
      .from('anime_new')
      .select('id, episode_title, episode_number, translating_group, player_source, external_player_link, quality, audio_language, subtitle_language')
      .eq('id', episodeId)
      .single();

    if (error) {
      console.error('Error fetching episode groups:', error);
      return json({ error: 'Failed to fetch episode groups' }, { status: 500 });
    }

    if (!data) {
      return json({ error: 'Episode not found' }, { status: 404 });
    }

    return json({
      id: data.id,
      title: data.episode_title,
      episodeNumber: data.episode_number,
      translatingGroup: data.translating_group,
      playerSource: data.player_source,
      externalPlayerLink: data.external_player_link,
      quality: data.quality,
      audioLanguage: data.audio_language,
      subtitleLanguage: data.subtitle_language
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
