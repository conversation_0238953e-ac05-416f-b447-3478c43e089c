<script>
	import { page } from '$app/stores';
	import { House as HomeSolid, Calendar as CalendarMonthSolid, Search as SearchSolid, UserRound as UserSolid } from 'lucide-svelte';
	import * as Avatar from '$lib/components/ui/avatar';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	import { userStore } from '$lib/stores/userLogin.js';
	import { clearCache } from '$lib/utils/cacheUtils';

	const navigationItems = [
		{ path: '/', label: 'Home', icon: HomeSolid },
		{ path: '/search', label: 'Szukaj', icon: SearchSolid },
		{ path: '/schedule', label: 'Harmonogram', icon: CalendarMonthSolid }
	];

	let showUserModal = false;

	$: session = $page.data.session;
	$: user = $page.data.user;
	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;

	async function handleLogout() {
		clearCache();
		$page.data.supabase.auth.signOut();
		userStore.logout();
		await sleep(1000);
		window.location.reload();
	}

	function handleCloseModal() {
		showUserModal = false;
	}
</script>

<nav class="fixed bottom-0 left-0 right-0 z-50 py-2 bg-gray-900 md:hidden" aria-label="Mobile navigation">
	<ul class="flex items-center justify-around">
		{#each navigationItems as { path, label, icon }}
			<li>
				<a href={path} class="flex flex-col items-center px-2 py-1" aria-current={$page.url.pathname === path ? 'page' : undefined}>
					<div class="flex items-center justify-center w-8 h-8">
						<svelte:component this={icon} class="w-6 h-6 text-white" />
					</div>
					<span class="sr-only">{label}</span>
				</a>
			</li>
		{/each}

		<li>
			<a href="https://discord.gg/lycoriscafe" data-umami-event="openDiscordNavbarMobile" class="flex flex-col items-center px-2 py-1" target="_blank" rel="noopener noreferrer">
				<div class="flex items-center justify-center w-8 h-8">
					<img src="/discord-outline.svg" alt="Discord" class="w-6 h-6" />
				</div>
				<span class="sr-only">Discord</span>
			</a>
		</li>

		<li>
			<button class="flex flex-col items-center px-2 py-1" on:click={() => (showUserModal = true)}>
				{#if isLoggedIn}
					<Avatar.Root class="w-8 h-8">
						<Avatar.Image src={user?.user_metadata?.avatar} alt={user?.user_metadata?.name} />
						<Avatar.Fallback>{user?.user_metadata?.name?.[0]}</Avatar.Fallback>
					</Avatar.Root>
				{:else}
					<div class="flex items-center justify-center w-8 h-8 bg-gray-800 rounded-full">
						<UserSolid class="w-6 h-6 text-white" />
					</div>
				{/if}
				<span class="sr-only">{isLoggedIn ? 'Profile' : 'Login'}</span>
			</button>
		</li>
	</ul>
</nav>

<UserProfileModal bind:open={showUserModal} {user} onClose={handleCloseModal} onLogout={handleLogout} />
