// src/routes/anime/[animeId]/[animeTitle]/watch/[episode]/+page.js
import { error } from '@sveltejs/kit';

export async function load({ params, parent }) {
	const { anime } = await parent();

	if (!anime) {
		throw error(404, 'Anime not found');
	}

	const currentEpisode = anime.episodes.find(ep => ep.number === parseInt(params.episode));
	const nextEpisodeData = anime.episodes.find(ep => ep.number === parseInt(params.episode) + 1) || null;

	if (!currentEpisode) {
		throw error(404, 'Episode not found');
	}

	return {
		anime,
		currentEpisode,
		nextEpisodeData
	};
}