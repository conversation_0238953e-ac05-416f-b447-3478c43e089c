<script>
	import { Tv, CheckCircle, ExternalLink, Plus, Share } from 'lucide-svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { getCachedColor, setCachedColor, generateAnimeUrl } from '$lib/myUtils';
	import tinycolor from 'tinycolor2';
	import { progressStore } from '$lib/stores/progressStore';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { getPreferredTitle } from '$lib/utils/titleHelper';

	export let episode;
	export let isDragging = false;
	export let provider = 'anilist';
	export let preferRomaji;
	let imageLoaded = false;
	let initialRender = true;

	function handleImageLoad() {
		imageLoaded = true;
	}

	// Normalize data fields based on provider
	$: animeId = provider === 'mal' ? episode.mal_id || episode.id : episode.id;
	$: title = episode.title;
	$: image = episode.image || episode.cover_image || episode.main_picture?.large || episode.main_picture?.medium;
	$: currentEpisode = episode.current_episode || episode.num_episodes_watched || 0;
	$: totalEpisodes = episode.total_episodes || episode.num_episodes || 12;
	$: releasedEpisodes = episode.released_episodes || episode.num_episodes || currentEpisode || 0;
	$: unwatchedEpisodes = episode.unwatched_episodes || Math.max(0, releasedEpisodes - currentEpisode);

	// Get the latest progress from the store, using the correct ID based on provider
	$: currentProgress = $progressStore[animeId]?.[currentEpisode]?.progress || 0;

	// Calculate watch progress percentage
	$: watchProgress = currentProgress > 85 ? 100 : currentProgress;

	// Calculate released episodes progress
	$: releasedProgress = (releasedEpisodes / (totalEpisodes || releasedEpisodes)) * 100;

	$: isAiring = releasedEpisodes !== totalEpisodes || totalEpisodes === null;

	$: hasNewEpisodes = episode.has_new_episodes || (currentEpisode < releasedEpisodes && releasedEpisodes !== totalEpisodes);

	$: nextEpisodeToWatch = currentEpisode >= releasedEpisodes ? currentEpisode : currentEpisode + 1;

	// Generate URL with correct ID
	$: episodeUrl = `${generateAnimeUrl({ ...episode, id: episode.anilist_id || episode.id })}/watch/${nextEpisodeToWatch}`;

	const handleEpisodeProgress = (e) => {
		const progress = Math.floor((e.detail.currentTime / e.detail.duration) * 100);
		if (progress > 0) {
			progressStore.updateProgress(animeId, currentEpisode, progress);
		}
	};

	let isHovered = false;
	let dominantColor = '#ffffff';

	function isMobileDevice() {
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			let adjustedColor = color;
			while (adjustedColor.getBrightness() < 190) {
				adjustedColor = adjustedColor.lighten(10);
			}
			adjustedColor = adjustedColor.saturate(100);

			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			dominantColor = cachedColor;
			return;
		}

		if (isMobileDevice()) {
			dominantColor = '#ffffff';
			return;
		}

		const color = await extractDominantColor(imgSrc);
		dominantColor = color;
		setCachedColor(imgSrc, color);
	}

	function handleKeyDown(event) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			window.location.href = episodeUrl;
		}
	}

	onMount(async () => {
		if (browser && image) {
			await getDominantColor(image);
		}

		setTimeout(() => {
			initialRender = false;
		}, 0);
	});
	$: unseenEpisodes = unwatchedEpisodes;
</script>

<div
	class="card-container group relative block cursor-pointer overflow-visible bg-gray-900 shadow-lg hover:z-20 {!initialRender ? 'transition-all duration-300 ease-in-out md:hover:scale-105' : ''}"
	style="margin-top: 10px; margin-bottom: 10px;"
>
	<a
		href={episodeUrl}
		class="block"
		on:mouseenter={() => !initialRender && (isHovered = true)}
		on:mouseleave={() => !initialRender && (isHovered = false)}
		on:mousedown|preventDefault
		on:click={(e) => {
			if (isDragging) {
				e.preventDefault();
			}
		}}
		on:keydown={handleKeyDown}
		aria-label="Kontynuuj oglądanie {title} od odcinka {nextEpisodeToWatch}"
	>
		<div class="relative w-full pb-[150%]">
			{#if hasNewEpisodes && unseenEpisodes > 0}
				<div class="absolute top-2 left-2 z-10 transition-opacity duration-300" style="opacity: {imageLoaded ? 1 : 0}">
					<div class="rounded-md bg-[#ee8585] px-2 py-0.5 text-[12px] font-bold text-black shadow-md">NOWE (+{unseenEpisodes})</div>
				</div>
			{:else if releasedEpisodes - currentEpisode !== 0 && unseenEpisodes > 0}
				<div class="absolute top-2 left-2 z-10 transition-opacity duration-300" style="opacity: {imageLoaded ? 1 : 0}">
					<div class="rounded-md bg-[#8ec3f4] px-2 py-0.5 text-[12px] font-bold text-black shadow-md">+{unseenEpisodes}</div>
				</div>
			{/if}
			{#if !imageLoaded}
				<Skeleton class="absolute inset-0 h-full w-full rounded-lg" />
			{/if}
			<img
				src={image}
				alt="Okładka {title}"
				class="block-interaction absolute top-0 left-0 h-full w-full rounded-lg object-cover transition-opacity duration-300"
				style="opacity: {imageLoaded ? 1 : 0};"
				on:load={handleImageLoad}
			/>
			<div class="absolute inset-0 bg-linear-to-t from-gray-900/20 to-transparent"></div>

			<div class="absolute top-2 right-2 transition-opacity duration-300" style="opacity: {imageLoaded ? 1 : 0}">
				<div class="rounded-full bg-gray-900 p-1 opacity-70">
					{#if isAiring}
						<Tv size={14} class="text-blue-400" />
						<span class="sr-only">Emitowane</span>
					{:else}
						<CheckCircle size={14} class="text-green-400" />
						<span class="sr-only">Zakończone</span>
					{/if}
				</div>
			</div>
		</div>

		<div class="fade-in absolute bottom-0 w-full space-y-1 rounded-b-lg bg-gray-800 p-2 opacity-90">
			<h3 class="title-text cursor-pointer text-xs font-bold {!initialRender ? 'transition-colors duration-300' : ''}" style:color={isHovered && !initialRender ? dominantColor : '#ffffff'}>
				{getPreferredTitle(episode, preferRomaji)}
			</h3>
			<div class="flex justify-between text-[10px] text-gray-300 sm:text-xs">
				<span>{currentEpisode} / {totalEpisodes}</span>
				<span>{Math.round((currentEpisode / totalEpisodes) * 100)}%</span>
			</div>
			<div class="relative h-1 w-full rounded-full bg-gray-700">
				<div
					class="absolute h-1 rounded-full {!initialRender ? 'transition-colors duration-300' : ''}"
					style:background-color={isHovered ? `${dominantColor}50` : 'rgba(96, 165, 250, 0.5)'}
					style:width="{releasedProgress}%"
					role="progressbar"
					aria-valuenow={releasedProgress}
					aria-valuemin="0"
					aria-valuemax="100"
					aria-label="Postęp wydanych odcinków"
				/>
				<div
					class="absolute h-1 rounded-full {!initialRender ? 'transition-colors duration-300' : ''}"
					style:background-color={isHovered ? dominantColor : '#60a5fa'}
					style:width="{Math.round((currentEpisode / totalEpisodes) * 100)}%"
					role="progressbar"
					aria-valuenow={Math.round((currentEpisode / totalEpisodes) * 100)}
					aria-valuemin="0"
					aria-valuemax="100"
					aria-label="Postęp obejrzanych odcinków"
				/>
			</div>
		</div>
	</a>
</div>

<style>
	.block-interaction {
		pointer-events: none;
	}

	.title-text {
		text-align: left;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		line-height: 1.2;
		max-height: 2.4em;
		word-break: break-word;
	}

	:global(.tooltip-content) {
		background-color: #1f2937;
		color: #ffffff;
		border: 1px solid #374151;
		padding: 0.5rem;
		border-radius: 0.375rem;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
	}

	.card-container {
		width: 150px;
	}

	@media (min-width: 640px) {
		.card-container {
			width: 150px;
		}
	}

	@media (min-width: 1024px) {
		.card-container {
			width: 200px;
		}
	}

	@media (min-width: 1537px) {
		.card-container {
			width: 200px;
		}
	}

	.fade-in {
		animation: fadeIn 300ms ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
</style>
