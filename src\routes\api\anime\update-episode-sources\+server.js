import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminByRole } from '$lib/utils/roleUtils';

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request, locals }) {
  // Check if user is authenticated
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is an admin
  const hasAdminRole = await isAdminByRole(user.id);

  if (!hasAdminRole) {
    return json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    const { episodeId, translatingGroup, playerSource, externalPlayerLink, quality, audioLanguage, subtitleLanguage } = await request.json();

    if (!episodeId) {
      return json({ error: 'Missing episode ID' }, { status: 400 });
    }

    // Update the fields in the anime_new table
    const { data, error } = await supabase
      .from('anime_new')
      .update({ 
        translating_group: translatingGroup,
        player_source: playerSource,
        external_player_link: externalPlayerLink,
        quality: quality,
        audio_language: audioLanguage,
        subtitle_language: subtitleLanguage
      })
      .eq('id', episodeId);

    if (error) {
      console.error('Error updating episode sources:', error);
      return json({ error: 'Failed to update episode sources' }, { status: 500 });
    }

    return json({ success: true });
  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET endpoint to fetch available translating groups and player sources
export async function GET({ locals }) {
  // Check if user is authenticated
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Fetch all translating groups
    const { data: translatingGroups, error: translatingGroupsError } = await supabase
      .from('translating_groups')
      .select('*')
      .order('name');

    if (translatingGroupsError) {
      console.error('Error fetching translating groups:', translatingGroupsError);
      return json({ error: 'Failed to fetch translating groups' }, { status: 500 });
    }

    // Fetch all player sources
    const { data: playerSources, error: playerSourcesError } = await supabase
      .from('player_sources')
      .select('*')
      .order('name');

    if (playerSourcesError) {
      console.error('Error fetching player sources:', playerSourcesError);
      return json({ error: 'Failed to fetch player sources' }, { status: 500 });
    }

    return json({ 
      translatingGroups,
      playerSources
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
