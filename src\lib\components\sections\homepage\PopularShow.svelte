<script>
	import { Star, TrendingUp, List, Tv } from 'lucide-svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { getCachedColor, setCachedColor, generateAnimeUrl } from '$lib/myUtils';
	import tinycolor from 'tinycolor2';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { getPreferredTitle } from '$lib/utils/titleHelper';

	export let show;
	export let isDragging = false;
	export let preferRomaji;

	let isHovered = false;
	let dominantColor = '#ffffff';
	let isMobileLandscape = false;
	let imageLoaded = false;

	function handleImageLoad() {
		imageLoaded = true;
	}
	$: episodesText = isMobileLandscape || isMobileDevice() ? 'odc.' : 'odcinków';
	$: formatText = isMobileLandscape && show.format?.includes('TV') ? 'TV' : show.format;

	function isMobileDevice() {
		if (!browser) return false;
		let isMobile = window.innerWidth < 640;
		if (isMobile) return true;
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			// let adjustedColor = color.saturate(10);
			let adjustedColor = color;
			while (adjustedColor.getBrightness() < 190) {
				adjustedColor = adjustedColor.lighten(10);
			}
			adjustedColor = adjustedColor.saturate(100);
			// while (adjustedColor.getBrightness() > 180) {
			// 	adjustedColor = adjustedColor.darken(10);
			// }

			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			dominantColor = cachedColor;
			return;
		}

		if (isMobileDevice()) {
			dominantColor = '#ffffff';
			return;
		}

		const color = await extractDominantColor(imgSrc);
		dominantColor = color;
		setCachedColor(imgSrc, color);
	}

	function handleKeyDown(event) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			window.location.href = generateAnimeUrl(show);
		}
	}

	function checkMobileLandscape() {
		isMobileLandscape = window.innerWidth <= 9999 && window.innerHeight <= 500;
	}

	onMount(async () => {
		if (browser) {
			await getDominantColor(show.image);
			checkMobileLandscape();
			window.addEventListener('resize', checkMobileLandscape);
		}
	});
</script>

<div
	class="relative block overflow-visible transition-all duration-300 ease-in-out bg-gray-900 shadow-lg cursor-pointer card-container group hover:z-20 md:hover:scale-105"
	style="margin-top: 10px; margin-bottom: 10px;"
>
	<a
		href={generateAnimeUrl(show)}
		class="block"
		on:mouseenter={() => (isHovered = true)}
		on:mouseleave={() => (isHovered = false)}
		on:mousedown|preventDefault
		on:click={(e) => {
			if (isDragging) {
				e.preventDefault();
			}
		}}
		on:keydown={handleKeyDown}
		aria-label="Zobacz szczegóły dla {show.title}"
	>
		<div class="relative w-full pb-[150%]">
			{#if !imageLoaded}
				<Skeleton class="absolute inset-0 w-full h-full rounded-lg" />
			{/if}
			<img
				src={show.image}
				alt="Okładka {show.title}"
				class="absolute top-0 left-0 object-cover w-full h-full transition-opacity duration-300 rounded-lg block-interaction"
				style="opacity: {imageLoaded ? 1 : 0};"
				on:load={handleImageLoad}
			/>
			<div class="absolute inset-0 bg-linear-to-t from-gray-900/20 to-transparent"></div>

			<div class="absolute transition-opacity duration-300 top-2 right-2" style="opacity: {imageLoaded ? 1 : 0}">
				<div class="p-1 bg-gray-900 rounded-full opacity-70">
					<Tv size={14} class="text-blue-400" />
				</div>
			</div>
		</div>

		<div class="absolute bottom-0 w-full p-2 space-y-1 bg-gray-800 rounded-b-lg fade-in opacity-90">
			<h3 class="text-xs font-bold transition-colors duration-300 cursor-pointer title-text" style:color={isHovered ? dominantColor : '#ffffff'}>
				{getPreferredTitle(show, preferRomaji)}
			</h3>
			<div class="flex flex-col text-[10px] text-gray-300 sm:text-xs">
				<div class="flex justify-between">
					<span class="flex items-center">
						<TrendingUp size={12} class="mr-1 text-blue-300" aria-hidden="true" />
						{#if show.rankings && Array.isArray(show.rankings)}
							{#if show.rankings.find((item) => item.type === 'POPULAR' && item.allTime === false && item.season !== null)?.rank}
								<span class="font-bold text-white">
									#{show.rankings.find((item) => item.type === 'POPULAR' && item.allTime === false && item.season !== null).rank} &nbsp;
								</span>
							{:else}
								<span class="font-bold text-white">#{show.index + 1} &nbsp;</span>
							{/if}
						{:else}
							<span class="font-bold text-white">#{show.index + 1} &nbsp;</span>
						{/if}
					</span>
					<span class="flex items-center">
						<Star size={12} class="mr-1 text-yellow-400" aria-hidden="true" />
						{#if show.rating === null || show.rating === 0}
							<span class="font-bold text-white">?</span>&nbsp;
						{:else}
							<span class="font-bold text-white">{show.rating.toFixed(1)}</span>&nbsp;
						{/if}
						{#if show.rankings && Array.isArray(show.rankings)}
							{#if show.rankings.find((item) => item.type === 'RATED' && item.allTime === false && item.season !== null)?.rank}
								<span>
									(#{show.rankings.find((item) => item.type === 'RATED' && item.allTime === false && item.season !== null).rank})
								</span>
							{:else}
								<span>(#{show.rankingSpot || '?'})</span>
							{/if}
						{:else}
							<span>(#{show.rankingSpot || '?'})</span>
						{/if}
					</span>
				</div>
				<div class="flex justify-between">
					<span class="flex items-center">
						<List size={12} class="mr-1" aria-hidden="true" />
						<span>{show.totalEpisodes ? show.totalEpisodes : '?'} {episodesText}</span>
					</span>
					<span>{formatText}</span>
				</div>
			</div>
		</div>
	</a>
</div>

<style>
	.block-interaction {
		pointer-events: none;
	}

	.title-text {
		text-align: left;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		line-height: 1.2;
		max-height: 2.4em;
		word-break: break-word;
	}

	:global(.tooltip-content) {
		background-color: #1f2937;
		color: #ffffff;
		border: 1px solid #374151;
		padding: 0.5rem;
		border-radius: 0.375rem;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
	}

	.card-container {
		width: 150px;
	}

	@media (min-width: 640px) {
		.card-container {
			width: 150px;
		}
	}

	@media (min-width: 1024px) {
		.card-container {
			width: 200px;
		}
	}

	@media (min-width: 1537px) {
		.card-container {
			width: 200px;
		}
	}

	.fade-in {
		animation: fadeIn 300ms ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
</style>
