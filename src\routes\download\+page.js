export const csr = true;

export async function load({ url, fetch }) {
  try {
    const response = await fetch(`/api/download${url.search}`);

    if (response.redirected) {
      window.location.href = response.url;
      return {};
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || errorData.error || 'Download failed');
    }

    const data = await response.json();
    return data;

  } catch (error) {
    console.error('Error in download page load:', error);
    return {
      error: error.message || 'Failed to process download request'
    };
  }
}