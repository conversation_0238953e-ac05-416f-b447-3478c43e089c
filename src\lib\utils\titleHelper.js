import settingsStore from '$lib/stores/settingsStore';
import { get } from 'svelte/store';

/**
 * Returns the appropriate title based on user preferences
 * @param {Object} anime - The anime object containing title information
 * @param {string} defaultTitle - Optional fallback if neither title is available
 * @returns {string} The title to display
 */
export function getPreferredTitle(anime, preferRomaji, defaultTitle = 'Nieznane anime') {

  if (!anime) return defaultTitle;
  const settings = get(settingsStore);
  // If romaji is preferred, use the primary title (which is usually romaji)
  // Otherwise use the English title if available, or fall back to the primary title
  if (preferRomaji) {
    return anime.title || anime.anime_title || anime.english_title || defaultTitle;
  } else {
    return anime.english_title || anime.englishTitle || anime.title || defaultTitle;
  }
}