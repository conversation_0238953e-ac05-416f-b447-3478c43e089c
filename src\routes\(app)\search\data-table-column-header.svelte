<script>
	import { ArrowD<PERSON>, ArrowUp, EyeOff } from 'lucide-svelte';
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';

	export let header;
	export let onSort;
	export let currentSort = { field: null, direction: null };
	export let canSort = false;
	export let canHide = false;
	export let onHide;

	$: isActive = currentSort.field === header.toLowerCase();
	$: sortDirection = isActive ? currentSort.direction : null;

	function handleSort(direction) {
		onSort({
			field: header.toLowerCase(),
			direction
		});
	}
</script>

{#if header}
	<div class="flex items-center space-x-2">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger asChild let:builder>
				<Button variant="ghost" builders={[builder]} class="h-8 data-[state=open]:bg-accent" size="sm">
					<span>{header}</span>
					{#if canSort && sortDirection}
						{#if sortDirection === 'desc'}
							<ArrowDown class="ml-2 h-4 w-4" />
						{:else if sortDirection === 'asc'}
							<ArrowUp class="ml-2 h-4 w-4" />
						{/if}
					{/if}
				</Button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content align="start">
				{#if canSort}
					<DropdownMenu.Item on:click={() => handleSort('asc')} class="cursor-pointer">
						<ArrowUp class="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
						Rosnąco
					</DropdownMenu.Item>
					<DropdownMenu.Item on:click={() => handleSort('desc')} class="cursor-pointer">
						<ArrowDown class="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
						Malejąco
					</DropdownMenu.Item>
				{/if}
				{#if canHide}
					<DropdownMenu.Separator />
					<DropdownMenu.Item on:click={onHide} class="cursor-pointer">
						<EyeOff class="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
						Ukryj
					</DropdownMenu.Item>
				{/if}
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>
{:else}
	<span>{header}</span>
{/if}

<style>
	:global(.dropdown-item) {
		display: flex;
		align-items: center;
		padding: 0.5rem 1rem;
		font-size: 0.875rem;
		color: var(--foreground);
		cursor: pointer;
	}

	:global(.dropdown-item:hover) {
		background-color: var(--muted);
	}

	:global(.dropdown-item:focus) {
		outline: none;
		background-color: var(--muted);
	}
</style>
