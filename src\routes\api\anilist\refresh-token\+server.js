//src/routes/api/anilist/refresh-token
import { json } from '@sveltejs/kit';
import { refreshAnilistToken } from '$lib/utils/anilist';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabaseAdmin = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ locals }) {
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const newToken = await refreshAnilistToken(user.id, supabaseAdmin);

    if (!newToken) {
      return json({ error: 'Failed to refresh token' }, { status: 401 });
    }

    return json({ access_token: newToken });
  } catch (error) {
    console.error('Token refresh error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}