//src/routes/api/home/<USER>
import { json } from '@sveltejs/kit';
import { formatDistanceToNow } from 'date-fns';
import { pl } from 'date-fns/locale';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);


function processHeroData(heroItems) {
  const categories = {
    recentHighQuality: heroItems.filter(show => {
      const releaseDate = new Date(show.latest_episode.airDate);
      const isRecent = Date.now() - releaseDate <= 2 * 24 * 60 * 60 * 1000;
      return isRecent && show.rating >= 7.5;
    }),
    topRanked: heroItems.filter(show => {
      // Check for seasonal ranking using the raw rankings array
      if (show.rankings?.raw && Array.isArray(show.rankings.raw)) {
        const seasonalRanking = show.rankings.raw.find(r =>
          r.type === 'POPULAR' &&
          r.allTime === false &&
          r.season !== null &&
          r.context === 'most popular');
        return seasonalRanking && seasonalRanking.rank <= 5 && show.rating >= 8.0;
      }
      return false;
    }),
    trending: heroItems.filter(show =>
      show.trending > 500 &&
      show.rating >= 7.0
    ),
    //not currently used
    hidden_gems: heroItems.filter(show =>
      show.rating >= 10 &&
      show.popularity < 800 &&
      show.trending < 300
    ),
    regular: heroItems.filter(show =>
      show.latest_episode.airDate &&
      new Date(show.latest_episode.airDate) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    )
  };

  const finalSelection = [];

  if (categories.recentHighQuality.length) {
    const randomIndex = Math.floor(Math.random() * categories.recentHighQuality.length);
    finalSelection.push({
      ...categories.recentHighQuality[randomIndex],
      heroReason: 'Nowy Wysoko Oceniany'
    });
  }

  if (categories.topRanked.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.topRanked.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      // Get the seasonal ranking
      const seasonalRanking = show.rankings?.raw?.find(r =>
        r.type === 'POPULAR' &&
        r.allTime === false &&
        r.season !== null &&
        r.context === 'most popular');

      finalSelection.push({
        ...show,
        heroReason: seasonalRanking ? `#${seasonalRanking.rank} W tym sezonie` : 'Wysoka Ocena'
      });
    }
  }

  if (categories.trending.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.trending.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Popularne'
      });
    }
  }

  if (categories.hidden_gems.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.hidden_gems.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Ukryty Klejnot'
      });
    }
  }

  if (categories.regular.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.regular.filter(show =>
      !finalSelection.some(f => f.id === show.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Z ostatnich 7 dni'
      });
    }
  }

  const remainingPool = heroItems
    .filter(show =>
      !finalSelection.some(f => f.id === show.id) &&
      show.rating >= 1 &&
      new Date(show.latest_episode.airDate) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    )
    .sort((a, b) => b.rating - a.rating);

  while (finalSelection.length < 5 && remainingPool.length) {
    const randomIndex = Math.floor(Math.random() * Math.min(remainingPool.length, 5));
    const show = remainingPool.splice(randomIndex, 1)[0];

    // Get the seasonal ranking
    const seasonalRanking = show.rankings?.raw?.find(r =>
      r.type === 'POPULAR' &&
      r.allTime === false &&
      r.season !== null &&
      r.context === 'most popular');

    finalSelection.push({
      ...show,
      heroReason: seasonalRanking ? `#${seasonalRanking.rank} W tym sezonie` : 'Polecane'
    });
  }

  return finalSelection.sort(() => Math.random() - 0.5);
}

export async function GET({ locals }) {
  const currentDate = new Date();
  const currentYear = 2025;
  const currentSeason = 'SPRING';
  const isLoggedIn = !!locals.session;

  try {
    // Define the promises to be resolved
    const promises = [
      supabase.rpc('get_hero_data_new', {
        current_season: currentSeason,
        current_year: currentYear
      }),
      supabase.rpc('get_new_releases_grouped', {
        limit_count: 40
      }),
      supabase.rpc('get_popular_now_new', {
        current_season: currentSeason,
        current_year: currentYear
      }),
      supabase.rpc('get_upcoming_episodes_new', {
        timestamp_ms: Date.now()
      }),
      supabase.rpc('get_release_schedule_new', {
        start_timestamp: currentDate.setHours(0, 0, 0, 0),
        end_timestamp: currentDate.setHours(23, 59, 59, 999)
      }),
      // Fetch translating groups for badges
      supabase
        .from('translating_groups')
        .select('name, logo_url')
        .order('name')
    ];

    // Only add the fake watching data for non-logged in users
    if (!isLoggedIn) {
      promises.push(
        supabase.rpc('get_continue_watching_fake', {
          limit_count: 12
        })
      );
    }

    const responses = await Promise.all(promises);

    const [
      heroResponse,
      newReleasesResponse,
      popularNowResponse,
      upcomingEpisodesResponse,
      releaseScheduleResponse,
      translatingGroupsResponse,
      continueWatchingResponse
    ] = responses;

    // Only check the first 6 responses for errors (excluding continue watching)
    [heroResponse, newReleasesResponse, popularNowResponse,
      upcomingEpisodesResponse, releaseScheduleResponse, translatingGroupsResponse].forEach(response => {
        if (response.error) throw response.error;
      });

    const heroItems = heroResponse.data.map(anime => ({
      ...anime,
      short_synopsis: anime.synopsis?.slice(0, 235) +
        (anime.synopsis?.length >= 235 ? '...' : '')
    }));

    const newReleasesData = (newReleasesResponse.data || []).map(release => ({
      id: release.id,
      anilist_id: release.anilist_id, // Add anilist_id for grouping
      title: release.romaji_title,
      english_title: release.english_title,
      episode: release.episode_number,
      total_episodes: release.episodes,
      image: release.thumbnail_link || release.banner_image, // Use thumbnail_link with banner_image fallback
      duration: release.date_added, // Use date_added for the time display
      year: release.season_year,
      type: release.format,
      preview: release.thumbnail_link,
      translating_group: release.translating_group || 'lycoris_cafe'
    }));

    return json({
      heroData: processHeroData(heroItems),
      newReleasesData,
      popularNowData: popularNowResponse.data,
      upcomingEpisodesData: upcomingEpisodesResponse.data,
      releaseScheduleData: releaseScheduleResponse.data,
      continueWatchingData: !isLoggedIn && continueWatchingResponse ? continueWatchingResponse.data : [],
      translatingGroups: translatingGroupsResponse.data || [],
      isLoggedIn
    });

  } catch (error) {
    console.error('Error in home API:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        heroData: [],
        newReleasesData: [],
        popularNowData: [],
        upcomingEpisodesData: [],
        releaseScheduleData: [],
        continueWatchingData: [],
        isLoggedIn: false
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}