// stores/modal.js
import { writable } from 'svelte/store';

const createModalStore = () => {
  const { subscribe, set, update } = writable({
    type: null,
    props: {},
    isOpen: false
  });

  return {
    subscribe,
    open: (type, props) =>
      update(() => ({ type, props, isOpen: true })),
    close: () =>
      update(state => ({ ...state, isOpen: false }))
  };
};

export const modalStore = createModalStore();