<script>
	import { House as HomeSolid, Calendar as CalendarMonthSolid, Search as SearchSolid, UserRound as UserSolid, LogOut, MoreHorizontal } from 'lucide-svelte';
	import { page } from '$app/stores';
	import { fade } from 'svelte/transition';
	import { onMount, createEventDispatcher } from 'svelte';
	import { userStore } from '$lib/stores/userLogin.js';
	import * as Avatar from '$lib/components/ui/avatar';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	import { goto } from '$app/navigation';
	import { clearCache } from '$lib/utils/cacheUtils';

	// Create event dispatcher
	const dispatch = createEventDispatcher();

	let isHovered = false;
	let navbarElement;
	let closeThreshold = 172;
	let isMobileLandscape = false;
	let isMouseWithinWindow = true;
	let showUserModal = false;
	let mouseCheckInterval;
	let mouseX = 0;
	let mouseY = 0;

	$: session = $page.data.session;
	$: user = $page.data.user;
	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;

	function handleOpenStremioModal() {
		// Dispatch event to parent component instead of managing state locally
		dispatch('showStremioModal');
	}

	function sleep(ms) {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	async function handleLogout() {
		clearCache();
		$page.data.supabase.auth.signOut();
		userStore.logout();
		await sleep(1000);
		window.location.reload();
	}

	onMount(() => {
		checkMobileLandscape();
		window.addEventListener('resize', checkMobileLandscape);
		window.addEventListener('mouseleave', handleWindowMouseLeave);
		window.addEventListener('mouseenter', handleWindowMouseEnter);
		window.addEventListener('mousemove', trackMousePosition);

		return () => {
			window.removeEventListener('resize', checkMobileLandscape);
			window.removeEventListener('mouseleave', handleWindowMouseLeave);
			window.removeEventListener('mouseenter', handleWindowMouseEnter);
			window.removeEventListener('mousemove', trackMousePosition);
			if (mouseCheckInterval) clearInterval(mouseCheckInterval);
		};
	});

	function trackMousePosition(event) {
		mouseX = event.clientX;
		mouseY = event.clientY;
	}

	function checkMousePosition() {
		if (!navbarElement || !isHovered || isMobileLandscape) {
			// If navbar is not hovered anymore, clear the interval
			if (mouseCheckInterval) {
				clearInterval(mouseCheckInterval);
				mouseCheckInterval = null;
			}
			return;
		}

		const rect = navbarElement.getBoundingClientRect();

		// Check if mouse is outside navbar's expanded area
		if (mouseX > rect.left + closeThreshold || mouseX < rect.left || mouseY < rect.top || mouseY > rect.bottom) {
			isHovered = false;
			// Clear the interval when navbar is closed
			clearInterval(mouseCheckInterval);
			mouseCheckInterval = null;
		}
	}

	function checkMobileLandscape() {
		isMobileLandscape = window.innerWidth <= 9999 && window.innerHeight <= 500;
	}

	function handleMouseEnter() {
		if (!isMobileLandscape) {
			isHovered = true;
			// Start the interval check when navbar is opened
			if (!mouseCheckInterval) {
				mouseCheckInterval = setInterval(checkMousePosition, 200);
			}
		}
	}

	function handleMouseLeave(event) {
		if (!isMouseWithinWindow) {
			isHovered = false;
			// Clear the interval when navbar is closed
			if (mouseCheckInterval) {
				clearInterval(mouseCheckInterval);
				mouseCheckInterval = null;
			}
		}
	}

	function handleWindowMouseLeave() {
		isMouseWithinWindow = false;
	}

	function handleWindowMouseEnter() {
		isMouseWithinWindow = true;
	}

	function handleMouseMove(event) {
		if (!navbarElement || isMobileLandscape) return;

		const rect = navbarElement.getBoundingClientRect();
		const mouseX = event.clientX;

		if (isHovered && mouseX > rect.left + closeThreshold) {
			isHovered = false;
		} else if (!isHovered && mouseX <= rect.left + closeThreshold) {
			isHovered = true;
		}
	}

	function handleCloseModal() {
		showUserModal = false;
	}
</script>

<aside
	bind:this={navbarElement}
	class="no-select fixed top-0 left-2 !z-[999] hidden h-full w-16 flex-col items-center justify-start bg-gray-900 py-4 transition-all duration-300 ease-in-out md:flex"
	class:navbar-expanded={isHovered && !isMobileLandscape}
	on:mouseenter={handleMouseEnter}
	on:mouseleave={handleMouseLeave}
	on:mousemove={handleMouseMove}
>
	<nav class="flex flex-col items-start justify-start w-full h-full space-y-8" aria-label="Main navigation">
		<div class="ml-[-6px] flex w-full items-center pr-0 pl-4">
			<a href="/" class="flex items-center">
				<img src="/android-chrome-192x192.png" alt="lycoris.cafe logo" class="w-10 h-10 rounded-full" />
				{#if isHovered && !isMobileLandscape}
					<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> lycoris.cafe </span>
				{/if}
			</a>
		</div>

		<nav class="flex flex-col items-start justify-center w-full h-full space-y-8" aria-label="Main navigation">
			{#if isLoggedIn && user?.user_metadata}
				<div class="ml-[-6px] flex w-full items-center pr-0 pl-4">
					<button class="flex items-center cursor-pointer" on:click={() => (showUserModal = true)}>
						<Avatar.Root class="w-10 h-10">
							<Avatar.Image src={user.user_metadata.avatar} alt={user.user_metadata.name} />
							<Avatar.Fallback>{user.user_metadata.name[0]}</Avatar.Fallback>
						</Avatar.Root>
						{#if isHovered && !isMobileLandscape}
							<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}>
								{user.user_metadata.name}
							</span>
						{/if}
					</button>
				</div>
			{:else}
				<div class="flex items-center w-full px-4">
					<button on:click={() => (showUserModal = true)} class="flex items-center cursor-pointer">
						<div class="ml-[-6px] flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-gray-800">
							<UserSolid class="w-6 h-6 text-white" />
						</div>
						{#if isHovered && !isMobileLandscape}
							<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Zaloguj się </span>
						{/if}
					</button>
				</div>
			{/if}

			<div class="flex items-center w-full px-4">
				<a href="/" class="flex items-center" aria-label="Home" aria-current={$page.url.pathname === '/' ? 'page' : undefined}>
					<div class="ml-[-6px] flex h-10 w-10 items-center justify-center">
						<HomeSolid class="text-white h-7 w-7" />
					</div>
					{#if isHovered && !isMobileLandscape}
						<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Główna </span>
					{/if}
				</a>
			</div>

			<div class="flex items-center w-full px-4">
				<a href="/search" class="flex items-center" aria-label="Szukaj" aria-current={$page.url.pathname === '/search' ? 'page' : undefined}>
					<div class="ml-[-6px] flex h-10 w-10 items-center justify-center">
						<SearchSolid class="text-white h-7 w-7" />
					</div>
					{#if isHovered && !isMobileLandscape}
						<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Szukaj </span>
					{/if}
				</a>
			</div>

			<div class="flex items-center w-full px-4">
				<a href="/schedule" class="flex items-center" aria-label="Harmonogram" aria-current={$page.url.pathname === '/schedule' ? 'page' : undefined}>
					<div class="ml-[-6px] flex h-10 w-10 items-center justify-center">
						<CalendarMonthSolid class="text-white h-7 w-7" />
					</div>
					{#if isHovered && !isMobileLandscape}
						<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Harmonogram </span>
					{/if}
				</a>
			</div>

			<div class="flex items-center w-full px-4">
				<a href="https://discord.gg/lycoriscafe" data-umami-event="openDiscordNavbar" class="flex items-center" aria-label="Discord" target="_blank">
					<div class="ml-[-6px] flex h-10 w-10 items-center justify-center">
						<img src="/discord-outline.svg" alt="Discord" class="text-white h-7 w-7" />
					</div>
					{#if isHovered && !isMobileLandscape}
						<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Discord </span>
					{/if}
				</a>
			</div>

			<div class="flex items-center w-full px-4">
				<button class="flex items-center cursor-pointer" on:click={handleOpenStremioModal} aria-label="Więcej">
					<div class="ml-[-6px] flex h-10 w-10 items-center justify-center">
						<MoreHorizontal class="text-white h-7 w-7" />
					</div>
					{#if isHovered && !isMobileLandscape}
						<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Więcej </span>
					{/if}
				</button>
			</div>

			{#if isLoggedIn}
				<div class="flex items-center w-full px-4">
					<button class="flex items-center cursor-pointer" on:click={handleLogout} aria-label="Wyloguj się">
						<div class="ml-[-6px] flex h-10 w-10 items-center justify-center">
							<LogOut class="text-white h-7 w-7" />
						</div>
						{#if isHovered && !isMobileLandscape}
							<span class="ml-3 text-sm text-white truncate" transition:fade={{ duration: 200 }}> Wyloguj się </span>
						{/if}
					</button>
				</div>
			{/if}
		</nav>
	</nav>
</aside>

<UserProfileModal bind:open={showUserModal} {user} onClose={handleCloseModal} onLogout={handleLogout} />

<style>
	.navbar-expanded {
		width: 256px; /* 64rem * 4 = 256px */
		background-color: rgba(16, 24, 40, 0.5);
		-webkit-backdrop-filter: blur(16px); /* Safari */
		backdrop-filter: blur(16px);
	}

	.no-select {
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	/* Transition for expansion effect */
	aside {
		transition:
			width 300ms ease-in-out,
			background-color 300ms ease-in-out,
			backdrop-filter 300ms ease-in-out;
	}
</style>
