// src/routes/auth/myanimelist/+server.js
import { redirect, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { MAL_CLIENT_ID, MAL_CLIENT_SECRET } from '$env/static/private';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY, AUTH_PASSWORD_SECRET } from '$env/static/private';
import crypto from 'crypto';

const supabaseAdmin = createClient(
    PUBLIC_SUPABASE_URL,
    SUPABASE_SERVICE_ROLE_KEY,
    {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    }
);

export const GET = async ({ url, locals, cookies }) => {
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    console.log('Starting MAL auth callback with code:', code?.substring(0, 10) + '...');

    if (!code) {
        console.error('No authorization code provided in URL params');
        throw error(400, 'Missing authorization code');
    }

    try {
        // Get the code_verifier from cookies
        const codeVerifier = cookies.get('mal_code_verifier');

        if (!codeVerifier) {
            console.error('No code verifier found in cookies');
            throw error(400, 'Missing code verifier for PKCE flow. Please try again.');
        }

        // Token exchange with MyAnimeList
        console.log('Exchanging code for token with PKCE...');
        const tokenResponse = await fetch('https://myanimelist.net/v1/oauth2/token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                client_id: MAL_CLIENT_ID,
                client_secret: MAL_CLIENT_SECRET,
                grant_type: 'authorization_code',
                code: code,
                code_verifier: codeVerifier,
                redirect_uri: `${url.origin}/auth/myanimelist`
            })
        });

        // Clear the code_verifier cookie as it's no longer needed
        cookies.delete('mal_code_verifier', { path: '/' });

        const tokenData = await tokenResponse.json();

        if (!tokenResponse.ok) {
            console.error('Token exchange failed:', tokenData);
            throw error(tokenResponse.status, 'Failed to exchange token');
        }

        // Get MAL user info
        console.log('Fetching user info...');
        const userResponse = await fetch('https://api.myanimelist.net/v2/users/@me', {
            headers: {
                Authorization: `Bearer ${tokenData.access_token}`
            }
        });

        const userData = await userResponse.json();
        console.log('Got user info for:', userData.name);

        const email = `${userData.name}-${userData.id}-<EMAIL>`;
        const password = crypto.createHash('sha256')
            .update(`${userData.id}-${AUTH_PASSWORD_SECRET}`)
            .digest('hex');

        // First check if a user with this profile_id already exists
        console.log('Checking if user with MAL ID exists:', userData.id);
        const { data: existingProfile, error: profileQueryError } = await supabaseAdmin
            .from('profiles')
            .select('id')
            .eq('profile_id', userData.id)
            .single();

        if (profileQueryError && profileQueryError.code !== 'PGRST116') {
            console.error('Error checking for existing profile:', profileQueryError);
            throw error(500, 'Failed to check for existing user');
        }

        // If we found a profile with this MAL ID
        if (existingProfile) {
            console.log('Found existing profile with MAL ID:', userData.id);

            // Get the auth user associated with this profile
            const { data: existingUser, error: userQueryError } = await supabaseAdmin.auth.admin.getUserById(existingProfile.id);

            if (userQueryError) {
                console.error('Error fetching existing user:', userQueryError);
                throw error(500, 'Failed to fetch existing user');
            }

            // Fetch the user's role from the profiles table
            const { data: profileData, error: profileFetchError } = await supabaseAdmin
                .from('profiles')
                .select('role')
                .eq('id', existingProfile.id)
                .single();

            if (profileFetchError) {
                console.error('Error fetching user role:', profileFetchError);
            }

            // Update the user's metadata (including potentially changed username and role)
            await supabaseAdmin.auth.admin.updateUserById(existingProfile.id, {
                user_metadata: {
                    ...existingUser.user.user_metadata,
                    id: userData.id,
                    name: userData.name,
                    avatar: userData.picture || '/default-avatar.svg', // Default avatar if none provided
                    mal_token: tokenData.access_token,
                    mal_refresh_token: tokenData.refresh_token,
                    provider: 'mal',
                    token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
                    profile: {
                        ...existingUser.user.user_metadata?.profile,
                        role: profileData?.role || 'user'
                    }
                }
            });

            // Update profile name if it changed
            await supabaseAdmin
                .from('profiles')
                .update({
                    name: userData.name,
                    avatar: userData.picture || '/default-avatar.svg',
                    updated_at: new Date().toISOString()
                })
                .eq('id', existingProfile.id);

            // Sign in the user with their credentials
            const { data: signInData, error: signInError } = await locals.supabase.auth.signInWithPassword({
                email: existingUser.user.email,
                password
            });

            if (signInError) {
                console.error('Failed to sign in existing user:', signInError);
                throw error(500, 'Failed to sign in');
            }

            locals.session = signInData.session;
            locals.user = signInData.user;
            console.log('Existing user signed in successfully');
            throw redirect(303, '/');
        }

        // If no existing profile found, try to sign in with email/password as fallback
        console.log('No profile found with MAL ID, attempting to sign in by email...');
        const { data: signInData, error: signInError } = await locals.supabase.auth.signInWithPassword({
            email,
            password
        });

        if (!signInError && signInData.user) {
            console.log('Existing user signed in successfully by email');

            // Fetch the user's role from the profiles table
            const { data: profileData, error: profileFetchError } = await supabaseAdmin
                .from('profiles')
                .select('role')
                .eq('id', signInData.user.id)
                .single();

            if (profileFetchError) {
                console.error('Error fetching user role:', profileFetchError);
            }

            // Update existing user's token and include role in metadata
            await supabaseAdmin.auth.admin.updateUserById(signInData.user.id, {
                user_metadata: {
                    ...signInData.user.user_metadata,
                    id: userData.id,
                    name: userData.name,
                    avatar: userData.picture || '/default-avatar.svg', // Default avatar if none provided
                    mal_token: tokenData.access_token,
                    mal_refresh_token: tokenData.refresh_token,
                    provider: 'mal',
                    token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
                    profile: {
                        ...signInData.user.user_metadata?.profile,
                        role: profileData?.role || 'user'
                    }
                }
            });

            locals.session = signInData.session;
            locals.user = signInData.user;
            throw redirect(303, '/');
        }

        // Double-check one more time that a profile with this ID doesn't exist
        // This is a safety check to prevent duplicate profiles
        const { data: doubleCheckProfile, error: doubleCheckError } = await supabaseAdmin
            .from('profiles')
            .select('id')
            .eq('profile_id', userData.id)
            .maybeSingle();

        if (doubleCheckError) {
            console.error('Error during double-check for existing profile:', doubleCheckError);
            throw error(500, 'Failed to verify user does not exist');
        }

        if (doubleCheckProfile) {
            console.error('Profile with this MAL ID already exists, but sign-in failed:', userData.id);
            throw error(409, 'A user with this MAL ID already exists but sign-in failed. Please contact support.');
        }

        // If sign in failed, create new user with admin client
        console.log('User not found, creating new account...');
        const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
            email,
            password,
            email_confirm: true,
            user_metadata: {
                id: userData.id,
                name: userData.name,
                avatar: userData.picture,
                mal_token: tokenData.access_token,
                mal_refresh_token: tokenData.refresh_token,
                provider: 'mal',
                token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
                profile: {
                    role: 'user' // Default role for new users
                }
            }
        });

        if (createError) {
            console.error('Failed to create user:', createError);
            throw error(500, 'Failed to create user account');
        }

        const { error: profileError } = await supabaseAdmin
            .from('profiles')
            .insert({
                id: newUser.user.id,
                profile_id: userData.id,
                name: userData.name,
                avatar: userData.picture,
                role: 'user'
            });

        if (profileError) {
            console.error('Failed to create profile:', profileError);
            // Clean up the created user since profile creation failed
            await supabaseAdmin.auth.admin.deleteUser(newUser.user.id);
            throw error(500, 'Failed to create user profile');
        }

        // Sign in the new user
        const { data: newSignInData, error: newSignInError } = await locals.supabase.auth.signInWithPassword({
            email,
            password
        });

        if (newSignInError) {
            console.error('Failed to sign in new user:', newSignInError);
            throw error(500, 'Failed to sign in');
        }

        locals.session = newSignInData.session;
        locals.user = newSignInData.user;

        console.log('New user created and signed in successfully');
        throw redirect(303, '/');

    } catch (err) {
        // Check if this is a redirect to home page (successful case)
        if (err.constructor.name === 'Redirect' && err.status === 303 && err.location === '/') {
            throw err;
        }

        // Log and handle other errors
        console.error('Auth flow failed:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        throw redirect(303, `/?error=auth_failed&message=${encodeURIComponent(JSON.stringify(errorMessage))}`);
    }
}