<script>
	import { onMount, onDestroy } from 'svelte';
	import { goto } from '$app/navigation';
	import { format, parseISO, isEqual, differenceInSeconds, differenceInMinutes, addDays, startOfWeek, parse, isAfter, addHours } from 'date-fns';
	import { pl } from 'date-fns/locale';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import * as ContextMenu from '$lib/components/ui/context-menu/index.js';
	import * as Select from '$lib/components/ui/select';
	import { Star, TrendingUp, List, Tv, Eye, CheckCircle, PauseCircle, XCircle, Clock, X, X as XIcon } from 'lucide-svelte';
	import { getCachedColor, setCachedColor, generateAnimeUrl } from '$lib/myUtils';
	import { parseDate } from '@internationalized/date';
	import AnimeEditDialog from '$lib/components/sections/anime/AnimeEditDialog.svelte';
	import { But<PERSON> } from '$lib/components/ui/button';
	import { fade } from 'svelte/transition';
	import NavbarDesktop from '$lib/components/sections/navbar/NavbarDesktop.svelte';
	import NavbarMobile from '$lib/components/sections/navbar/NavbarMobile.svelte';
	import { userStore } from '$lib/stores/userLogin.js';
	import { slide } from 'svelte/transition';
	import { FilterIcon, CheckIcon } from 'lucide-svelte';
	import { getPreferredTitle } from '$lib/utils/titleHelper';
	import { browser } from '$app/environment';
	import { cacheKeys, getCachedData, setCachedData } from '$lib/utils/cacheUtils';
	import MoreModal from '$lib/components/sections/shared/MoreModal.svelte';

	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;
	import tinycolor from 'tinycolor2';
	export let scheduleData, preferRomaji;
	let mobileFilterOpen = false;
	let colorAlwaysOn = true;
	let scheduleContainer;
	let timelineLabels = [];
	let selectedAnime = null;
	let dialogOpen = false;
	let isMobile = false;
	let scheduleWrapper;
	let headerWrapper;
	let currentTime = new Date();
	let currentDay;
	let selectedStatuses = [];
	let timeLeftMap = new Map();
	let touchStartX = null;
	let titleColors = new Map();
	let showNotInList = isLoggedIn ? false : true;
	// function getStatusColor(status) {
	// 	switch (status) {
	// 		case 'watching':
	// 			return '#4ade80'; // Green
	// 		case 'completed':
	// 			return '#60a5fa'; // Blue
	// 		case 'on-hold':
	// 			return '#fbbf24'; // Yellow/Amber
	// 		case 'dropped':
	// 			return '#f87171'; // Red
	// 		case 'planning':
	// 			return '#a78bfa'; // Purple
	// 		default:
	// 			return '#ffffff'; // White
	// 	}
	// }

	// Add state for the Stremio modal
	let showStremioModal = false;

	function handleCloseStremioModal() {
		showStremioModal = false;
	}

	const statusIcons = {
		watching: Eye,
		completed: CheckCircle,
		on_hold: PauseCircle,
		dropped: XCircle,
		planning: Clock,
		not_in_list: X,
		current: Eye, // Add aliases
		paused: PauseCircle
	};

	const STATUS_MAPPING = {
		CURRENT: 'watching',
		PLANNING: 'planning',
		COMPLETED: 'completed',
		DROPPED: 'dropped',
		PAUSED: 'on-hold',
		CURRENT: 'watching', // Duplicate to handle different cases
		'IN PROGRESS': 'watching'
	};

	const statusOptions = [
		{ value: 'watching', label: 'Oglądane', icon: Eye },
		{ value: 'completed', label: 'Ukończone', icon: CheckCircle },
		{ value: 'on-hold', label: 'Wstrzymane', icon: PauseCircle },
		{ value: 'dropped', label: 'Porzucone', icon: XCircle },
		{ value: 'planning', label: 'Planowane', icon: Clock }
	];

	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	let showUserModal = false;

	function applyOffset(airDate, offset) {
		return new Date(airDate - 2 * 3600000);
	}

	function handleLoginPrompt() {
		showUserModal = true;
	}

	function handleCloseModal() {
		showUserModal = false;
	}

	function roundToHalfHour(date) {
		const d = new Date(date);
		const minutes = d.getMinutes();

		// Round to nearest half hour
		if (minutes >= 45) {
			// Round up to next hour
			d.setHours(d.getHours() + 1);
			d.setMinutes(0);
		} else if (minutes >= 15 && minutes < 45) {
			// Round to half hour
			d.setMinutes(30);
		} else {
			// Round down to current hour
			d.setMinutes(0);
		}

		d.setSeconds(0);
		d.setMilliseconds(0);
		return d;
	}

	function getStatusColor(status, anime) {
		// Add anime parameter
		const mappedStatus = STATUS_MAPPING[status] || status?.toLowerCase();

		// Check if anime is not in list
		if (!status || (status === 'planning' && (!anime?.progress || anime?.progress === 0))) {
			return '#e74c3c'; // Distinct color for not in list
		}

		switch (mappedStatus) {
			case 'watching':
			case 'current':
				return '#2ecc71';
			case 'completed':
				return '#3498db';
			case 'on-hold':
				return '#f39c12';
			case 'dropped':
				return '#e74c3c';
			case 'planning':
				return '#c2c2c2'; // Different color from not in list
			default:
				return '#c2c2c2';
		}
	}

	$: {
		currentDay = format(currentTime, 'eee');
		updateAllTimeLeft();
	}

	let weekDates = [];

	$: {
		const today = new Date();
		const weekStart = startOfWeek(today, { weekStartsOn: 1 });
		weekDates = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
	}

	// Transform anime data into schedule format
	$: baseSchedule = weekDates.map((date) => {
		const dayName = format(date, 'eee');

		const animeForDay = scheduleData
			.filter((anime) => {
				if (!anime.nextAiringEpisode || !anime.nextAiringEpisode.airingAt) return false;

				// Filter out delayed anime from schedule display
				if (anime.delayedAiring || anime.isDelayed) return false;

				// Apply offset to the airing date for filtering by day
				const airDate = applyOffset(new Date(anime.nextAiringEpisode.airingAt), anime.airing_schedule_offset || 0);
				return format(airDate, 'eee') === dayName;
			})
			.map((anime) => {
				const id = anime.id;
				// Apply offset to display time
				const airDate = applyOffset(new Date(anime.nextAiringEpisode.airingAt), anime.airing_schedule_offset || 0);
				const roundedDate = roundToHalfHour(airDate);

				if (!titleColors.has(id)) {
					titleColors.set(id, '#ffffff');
				}

				return {
					...anime,
					title: anime.title,
					episode: anime.nextAiringEpisode.episode,
					time: format(addHours(roundedDate, 1), roundedDate.getMinutes() === 30 ? 'HH:30' : 'HH:00'),
					status: anime.status,
					poster: anime.poster,
					background: anime.background,
					id: id,
					englishTitle: anime.englishTitle,
					totalEpisodes: anime.totalEpisodes,
					type: anime.format,
					year: anime.seasonYear,
					genres: anime.genres,
					synopsis: anime.synopsis,
					rating: anime.rating,
					popularity: anime.popularity,
					hasAired: anime.hasAired || airDate < new Date()
				};
			})
			.sort((a, b) => parse(a.time, 'HH:mm', new Date()) - parse(b.time, 'HH:mm', new Date()));

		return {
			day: dayName,
			anime: animeForDay
		};
	});

	function determineIfDelayed(airDate) {
		const today = new Date();
		const weekStart = startOfWeek(today, { weekStartsOn: 1 });
		const oneWeekFromStart = addDays(weekStart, 7);

		return airDate > oneWeekFromStart;
	}

	function getDelayedTimeLeft(anime) {
		// Check if this anime has delayed airing
		if (anime.delayedAiring) {
			return 'delayed-airing-text';
		}

		if (!anime.nextAiringEpisode || !anime.nextAiringEpisode.airingAt) return 'Unknown';

		// Apply offset to the airing time
		const airDate = applyOffset(new Date(anime.nextAiringEpisode.airingAt), anime.airing_schedule_offset || 0);

		const diff = airDate.getTime() - new Date().getTime();

		const days = Math.floor(diff / (1000 * 60 * 60 * 24));
		const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

		return `${days}d ${padWithZero(hours)}h ${padWithZero(minutes)}m`;
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			let adjustedColor = color;
			while (adjustedColor.getBrightness() < 190) {
				adjustedColor = adjustedColor.lighten(10);
			}
			adjustedColor = adjustedColor.saturate(100);

			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	function isMobileDevice() {
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			return cachedColor;
		}

		const color = await extractDominantColor(imgSrc);
		setCachedColor(imgSrc, color);
		return color;
	}

	// Time calculations
	function getNextAiringDate(time, day) {
		const [hours, minutes] = time.split(':').map(Number);
		const today = new Date();
		const weekStart = startOfWeek(today, { weekStartsOn: 1 });
		const dayIndex = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].indexOf(day);
		let airDate = addDays(weekStart, dayIndex);
		airDate.setHours(hours, minutes, 0, 0);

		if (airDate < today) {
			airDate = addDays(airDate, 7);
		}

		return airDate;
	}

	function getTimeLeft(time, day) {
		const airDate = getNextAiringDate(time, day);
		const diff = airDate.getTime() - new Date().getTime();

		const days = Math.floor(diff / (1000 * 60 * 60 * 24));
		const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
		const seconds = Math.floor((diff % (1000 * 60)) / 1000);

		if (days > 0) {
			return `${days}d ${padWithZero(hours)}h ${padWithZero(minutes)}m`;
		}
		return `${padWithZero(hours)}h ${padWithZero(minutes)}m ${padWithZero(seconds)}s`;
	}

	function padWithZero(num) {
		return num.toString().padStart(2, '0');
	}

	// Mobile scroll handling
	function handleTouchStart(e) {
		touchStartX = e.touches[0].clientX;
	}

	function handleTouchMove(e) {
		if (!touchStartX) return;
		const touchEndX = e.touches[0].clientX;
		const diff = touchStartX - touchEndX;
		if (scheduleWrapper) {
			scheduleWrapper.scrollLeft += diff;
			if (headerWrapper) {
				headerWrapper.scrollLeft = scheduleWrapper.scrollLeft;
			}
		}
		touchStartX = touchEndX;
	}

	function handleTouchEnd() {
		touchStartX = null;
	}

	function handleScroll() {
		if (!isMobile && scheduleWrapper && headerWrapper) {
			headerWrapper.scrollLeft = scheduleWrapper.scrollLeft;
		}
	}

	function updateWeekDates() {
		const today = new Date();
		// Get Monday of this week
		const weekStart = startOfWeek(today, { weekStartsOn: 1 });

		// Create array of all 7 days
		weekDates = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
	}

	onMount(async () => {
		updateWeekDates();
		checkMobile();
		window.addEventListener('resize', checkMobile);

		const timeInterval = setInterval(() => {
			currentTime = new Date();
			updateAllTimeLeft();
		}, 1000);

		if (scheduleWrapper) {
			scheduleWrapper.addEventListener('scroll', handleScroll);
			scheduleWrapper.addEventListener('touchstart', handleTouchStart);
			scheduleWrapper.addEventListener('touchmove', handleTouchMove);
			scheduleWrapper.addEventListener('touchend', handleTouchEnd);
		}

		// Initialize colors for all anime
		for (const day of baseSchedule) {
			for (const anime of day.anime) {
				if (anime.poster) {
					const color = await getDominantColor(anime.poster);
					titleColors.set(anime.id, color);
					titleColors = titleColors; // Trigger reactivity
				}
			}
		}

		updateTimelineLabels();
		updateAllTimeLeft();

		return () => {
			clearInterval(timeInterval);
			window.removeEventListener('resize', checkMobile);
			if (scheduleWrapper) {
				scheduleWrapper.removeEventListener('scroll', handleScroll);
				scheduleWrapper.removeEventListener('touchstart', handleTouchStart);
				scheduleWrapper.removeEventListener('touchmove', handleTouchMove);
				scheduleWrapper.removeEventListener('touchend', handleTouchEnd);
			}
		};
	});

	// Filter functions
	function filterAnimeByStatuses(schedule, statuses, showAll) {
		return schedule.map((day) => ({
			...day,
			anime: day.anime.filter((anime) => {
				// Check if anime is in user's list
				const inList = anime.status && !(anime.status === 'planning' && anime.progress === 0);

				// If not showing items not in list, filter out anything not in list
				if (!showAll && !inList) {
					return false;
				}

				// If no status filters are selected, show all remaining items
				if (!statuses.length) {
					return true;
				}

				// Otherwise, apply status filters
				const normalizedAnimeStatus = anime.status?.toUpperCase();
				const mappedStatus = STATUS_MAPPING[normalizedAnimeStatus] || normalizedAnimeStatus?.toLowerCase();
				return statuses.includes(mappedStatus);
			})
		}));
	}

	function toggleStatus(status) {
		const index = selectedStatuses.indexOf(status);
		if (index === -1) {
			selectedStatuses = [...selectedStatuses, status];
		} else {
			selectedStatuses = selectedStatuses.filter((s) => s !== status);
		}
	}

	function removeStatus(status) {
		selectedStatuses = selectedStatuses.filter((s) => s !== status);
	}

	function clearAllStatuses() {
		selectedStatuses = [];
	}

	// Status helpers
	function getStatusIcon(status) {
		const statusOption = statusOptions.find((opt) => opt.value === status);
		return statusOption ? statusOption.icon : Eye;
	}

	// Timeline management
	function updateTimelineLabels() {
		if (!filteredSchedule) return;

		timelineLabels = filteredSchedule
			.map((day) => {
				const dayIndex = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].indexOf(day.day);
				const labelDate = addDays(startOfWeek(currentTime, { weekStartsOn: 1 }), dayIndex);

				if (day.anime.length === 0) return null;

				const firstAnimeTime = parse(day.anime[0].time, 'HH:mm', labelDate);
				const lastAnimeTime = parse(day.anime[day.anime.length - 1].time, 'HH:mm', labelDate);

				let currentTimeOnDay = new Date(labelDate);
				currentTimeOnDay.setHours(currentTime.getHours(), currentTime.getMinutes(), 0, 0);

				if (currentTimeOnDay < firstAnimeTime) {
					currentTimeOnDay = new Date(firstAnimeTime);
				} else if (currentTimeOnDay > lastAnimeTime) {
					currentTimeOnDay = new Date(lastAnimeTime);
				}

				const totalMinutes = differenceInMinutes(lastAnimeTime, firstAnimeTime);
				const currentMinutes = differenceInMinutes(currentTimeOnDay, firstAnimeTime);
				const percentage = Math.max(0, Math.min(100, (currentMinutes / totalMinutes) * 100));

				return {
					day: day.day,
					time: format(currentTimeOnDay, 'HH:mm'),
					percentage
				};
			})
			.filter(Boolean);
	}

	function updateAllTimeLeft() {
		if (!filteredSchedule) return;
		filteredSchedule.forEach((day) => {
			day.anime.forEach((anime) => {
				const key = `${day.day}-${anime.title}-${anime.episode}-${anime.time}`;
				timeLeftMap.set(key, getTimeLeft(anime.time, day.day));
			});
		});
		timeLeftMap = timeLeftMap;
	}

	function getTimelinePosition(animeList) {
		const todayDate = format(currentTime, 'yyyy-MM-dd');
		const currentTimeString = format(currentTime, 'HH:mm');
		const currentDateTime = parseISO(`${todayDate}T${currentTimeString}`);

		let lastAiredIndex = -1;
		for (let i = 0; i < animeList.length; i++) {
			// Convert string time to date object for comparison
			const animeTimeString = animeList[i].time;
			const animeDateTime = parseISO(`${todayDate}T${animeTimeString}`);

			if (isAfter(currentDateTime, animeDateTime) || isEqual(currentDateTime, animeDateTime)) {
				lastAiredIndex = i;
			} else {
				break;
			}
		}

		return lastAiredIndex;
	}

	// Mobile helpers
	function checkMobile() {
		isMobile = window.innerWidth < 640;
		if (isMobile) {
			setTimeout(scrollToCurrentDay, 100);
		}
	}

	function scrollToCurrentDay() {
		if (isMobile && scheduleContainer) {
			const currentDayElement = scheduleContainer.querySelector(`.day-${format(currentTime, 'eee').toLowerCase()}`);
			if (currentDayElement) {
				currentDayElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
			}
		}
	}

	// Event handlers
	function handleAnimeClick(anime, event) {
		// Check if the click event originated from the poster image or its container
		const isPosterClick = event.target.tagName === 'IMG' || event.target.closest('img') || event.target.classList.contains('poster-container');

		if (isPosterClick) {
			// Always redirect to anime page for poster clicks
			goto(generateAnimeUrl(anime));
			return;
		}

		// For the rest of the anime container, keep the split behavior
		const rect = event.currentTarget.getBoundingClientRect();
		const clickY = event.clientY - rect.top;
		const halfHeight = rect.height / 2;

		if (clickY <= halfHeight) {
			goto(generateAnimeUrl(anime));
		} else {
			if (isLoggedIn) {
				openDialog(anime);
			} else {
				goto(generateAnimeUrl(anime));
			}
		}
	}

	function openDialog(anime) {
		selectedAnime = {
			...anime,
			mal_id: anime.mal_id,
			score: anime.score || 0,
			episodeProgress: anime.episode,
			// Don't use parseDate for Date objects
			startDate: anime.startDate || null,
			finishDate: anime.finishDate || null,
			totalRewatches: anime.totalRewatches || 0,
			notes: anime.notes || ''
		};
		dialogOpen = true;
	}

	function closeDialog() {
		dialogOpen = false;
	}

	function saveChanges(updatedAnime) {
		// Invalidate caches
		if (browser) {
			const mediaCacheKey = `${cacheKeys.ANILIST_MEDIA}${updatedAnime.id}`;
			const continueWatchingCacheKeyAL = `${cacheKeys.ANILIST_WATCHING}${updatedAnime.id}`;
			const continueWatchingCacheKeyMAL = `${cacheKeys.MAL_WATCHING}${updatedAnime.mal_id}`;
			const continueWatchingCacheKeyMAL2 = `${cacheKeys.MAL_WATCHING}`;
			let HomeCW = JSON.parse(localStorage.getItem(cacheKeys.HOME_DATA));

			// Find entry of id "anime.id" and update its current_episode
			if (HomeCW && HomeCW.continueWatchingData) {
				HomeCW.continueWatchingData = HomeCW.continueWatchingData.map((item) => {
					if (parseInt(item.id) === parseInt(updatedAnime.id)) {
						return {
							...item,
							current_episode: updatedAnime.episodeProgress,
							updated_at: new Date().toISOString()
						};
					}
					return item;
				});
				localStorage.setItem(cacheKeys.HOME_DATA, JSON.stringify(HomeCW));
			}

			localStorage.removeItem(mediaCacheKey);
			localStorage.removeItem(continueWatchingCacheKeyAL);
			localStorage.removeItem(continueWatchingCacheKeyMAL);
			localStorage.removeItem(continueWatchingCacheKeyMAL2);
		}
		closeDialog();
	}

	function deleteEntry() {
		closeDialog();
	}

	function addToWatchlist(anime) {
		// Implementation for adding to watchlist
	}

	function shareAnime(anime) {
		// Implementation for sharing anime
	}

	function isShowInPast(dayName, timeString, anime) {
		// Get the current day name
		const currentDayName = format(new Date(), 'eee');

		// Get days of the week in order
		const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

		// Find indices
		const dayIndex = daysOfWeek.indexOf(dayName);
		const currentDayIndex = daysOfWeek.indexOf(currentDayName);

		// Check if the day is before the current day
		if (dayIndex < currentDayIndex) return true;

		// If it's the current day, check if the time has passed
		if (dayIndex === currentDayIndex) {
			const [showHour] = timeString.split(':').map(Number);
			const currentHour = new Date().getHours();
			return showHour <= currentHour;
		}

		return false;
	}

	// Reactive declarations
	$: filteredSchedule = filterAnimeByStatuses(baseSchedule, selectedStatuses, showNotInList);
	$: currentDaySchedule = filteredSchedule.find((day) => day.day === format(currentTime, 'eee'));
	$: timelinePosition = currentDaySchedule ? getTimelinePosition(currentDaySchedule.anime) : -1;

	function getAccessibleName(anime) {
		return `${anime.title}, Episode ${anime.episode}, airing at ${anime.time}. Status: ${anime.status}`;
	}
</script>

<div class="flex flex-col h-screen min-h-screen overflow-hidden schedule-container md:ml-16" bind:this={scheduleContainer} in:fade={{ duration: 150, delay: 150 }} out:fade={{ duration: 150 }}>
	<NavbarDesktop on:showStremioModal={() => (showStremioModal = true)} />
	<NavbarMobile />

	<!-- Header section -->
	<div class="header-section">
		<div class="flex flex-wrap items-center">
			<div class="flex items-center gap-4">
				<h1 class="text-lg font-semibold">Harmonogram</h1>
				{#if isLoggedIn}
					<div class="flex items-center gap-2">
						<div class="md:hidden">
							<Button variant="outline" size="icon" on:click={() => (mobileFilterOpen = true)} class="w-8 h-8 p-1">
								<FilterIcon size={16} />
							</Button>
						</div>

						<div class="hidden md:block">
							<Select.Root
								multiple
								class="w-full max-w-[200px] sm:max-w-[250px]"
								positioning={{
									strategy: 'fixed',
									placement: 'bottom-start',
									width: 'trigger'
								}}
							>
								<Select.Trigger
									class="flex h-8 w-full min-w-[140px] cursor-pointer items-center gap-2 rounded-md border border-white/20 bg-transparent px-2 py-1 text-xs text-white outline-hidden transition-colors hover:bg-white/5 focus:ring-2 focus:ring-white/20 sm:min-w-[250px] sm:text-sm"
								>
									<Select.Value placeholder="Filtruj" class="w-full text-xs text-left truncate sm:text-sm">
										{#if selectedStatuses.length > 0}
											<span class="inline-block max-w-full truncate">
												{selectedStatuses.map((status) => statusOptions.find((opt) => opt.value === status)?.label || status).join(', ')}
											</span>
										{:else}
											Filtruj
										{/if}
									</Select.Value>
								</Select.Trigger>
								<Select.Content class="w-[200px] rounded-md border border-white/20 bg-gray-900 shadow-lg sm:w-[250px]">
									<Select.Group>
										{#each statusOptions as option}
											<Select.Item
												value={option.value}
												class="flex cursor-pointer items-center gap-2 px-2 py-1.5 text-xs text-white outline-hidden select-none data-highlighted:bg-white/5 data-selected:bg-white/10 sm:text-sm"
												on:click={() => toggleStatus(option.value)}
											>
												<div class="flex items-center justify-center w-4 h-4 mr-1 sm:mr-2 sm:h-5 sm:w-5">
													<svelte:component
														this={option.icon}
														size={14}
														class="shrink-0"
														style="color: {option.value === 'watching'
															? '#2ecc71'
															: option.value === 'completed'
																? '#3498db'
																: option.value === 'on-hold'
																	? '#f39c12'
																	: option.value === 'dropped'
																		? '#e74c3c'
																		: option.value === 'planning'
																			? '#c2c2c2'
																			: '#c2c2c2'}"
													/>
												</div>
												<span class="flex-grow truncate">{option.label}</span>
												{#if selectedStatuses.includes(option.value)}
													<span class="ml-auto text-xs">✓</span>
												{/if}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
						</div>

						<!-- Mobile Filter Modal -->
						{#if mobileFilterOpen}
							<!-- svelte-ignore a11y-click-events-have-key-events -->
							<!-- svelte-ignore a11y-no-static-element-interactions -->
							<div class="fixed inset-0 z-[9999] flex items-end justify-center bg-black/50 backdrop-blur-sm" on:click|self={() => (mobileFilterOpen = false)}>
								<!-- svelte-ignore a11y-click-events-have-key-events -->
								<!-- svelte-ignore a11y-no-static-element-interactions -->
								<div class="max-h-[80vh] w-full max-w-md overflow-y-auto rounded-t-xl bg-gray-900 p-4" transition:slide={{ duration: 300 }} on:click|stopPropagation>
									<div class="flex items-center justify-between mb-4">
										<h2 class="text-lg font-semibold">Filtruj po statusie</h2>
										<Button variant="ghost" size="icon" on:click={() => (mobileFilterOpen = false)}>
											<X size={24} />
										</Button>
									</div>

									<div class="space-y-2">
										{#each statusOptions as option}
											<!-- svelte-ignore a11y-click-events-have-key-events -->
											<!-- svelte-ignore a11y-no-static-element-interactions -->
											<div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg cursor-pointer" on:click={() => toggleStatus(option.value)}>
												<div class="flex items-center gap-3">
													<div class="flex items-center justify-center w-5 h-5">
														<svelte:component this={option.icon} size={16} />
													</div>
													<span class="text-sm">{option.label}</span>
												</div>
												{#if selectedStatuses.includes(option.value)}
													<CheckIcon size={16} class="text-green-500" />
												{/if}
											</div>
										{/each}
									</div>

									<div class="flex justify-between mt-4">
										<Button variant="outline" on:click={clearAllStatuses} class="w-full mr-2">Wyczyść filtry</Button>
										<Button variant="default" on:click={() => (mobileFilterOpen = false)} class="w-full">Zastosuj</Button>
									</div>
								</div>
							</div>
						{/if}
					</div>
				{:else}
					<div class="relative z-50">
						<div class="flex items-center gap-2 p-2 rounded">
							<Button variant="outline" size="sm" on:click={handleLoginPrompt} class="z-50 cursor-pointer">Zaloguj się</Button>
							<p class="hidden text-sm text-gray-300 md:block">aby filtrować według twojej listy</p>
						</div>
					</div>
				{/if}
			</div>

			<div class="flex items-center gap-2 ml-4">
				<!-- Existing color toggle -->
				<button
					class="flex items-center h-8 gap-2 px-3 py-1 text-sm text-white transition-colors bg-transparent border rounded-md cursor-pointer border-white/20 outline-hidden hover:bg-white/5 focus:ring-2 focus:ring-white/20"
					on:click={() => (colorAlwaysOn = !colorAlwaysOn)}
					aria-pressed={colorAlwaysOn}
				>
					{#if colorAlwaysOn}
						<span class="h-2 w-2 rounded-full bg-[#ee8585]"></span>
						{isMobile ? 'Wł.' : 'Kolory Wł.'}
					{:else}
						<span class="w-2 h-2 bg-white rounded-full"></span>
						{isMobile ? 'Wył.' : 'Kolory Wył.'}
					{/if}
				</button>

				<!-- New toggle for showing items not in list -->
				{#if isLoggedIn}
					<button
						class="flex items-center h-8 gap-2 px-3 py-1 text-sm text-white transition-colors bg-transparent border rounded-md cursor-pointer border-white/20 outline-hidden hover:bg-white/5 focus:ring-2 focus:ring-white/20"
						on:click={() => (showNotInList = !showNotInList)}
						aria-pressed={showNotInList}
					>
						<span class="w-2 h-2 rounded-full" class:bg-[#ee8585]={showNotInList} class:bg-white={!showNotInList}></span>
						{isMobile ? (showNotInList ? 'Cał.' : 'Lista') : showNotInList ? 'Pokaż wszystko' : 'Tylko lista'}
					</button>
				{/if}
			</div>
		</div>
	</div>

	<div class="schedule-content">
		{#if !isMobile}
			<!-- Desktop view -->
			<div class="schedule-grid" bind:this={scheduleWrapper}>
				<div class="week-header" bind:this={headerWrapper}>
					<div class="grid grid-cols-7 p-2 text-center">
						{#each weekDates as date, index}
							<div class="no-select day-header {format(date, 'eee') === format(currentTime, 'eee') ? 'text-blue-400' : ''}" aria-label={format(date, 'EEEE, MMMM d', { locale: pl })}>
								<div class="text-sm font-bold">
									{format(date, 'eee', { locale: pl })}
									<span class="-ml-16 font-light {format(date, 'eee') === format(currentTime, 'eee') ? 'text-blue-400' : 'text-gray-300'}"> </span>
								</div>
							</div>
						{/each}
					</div>
				</div>

				<div class="days-grid">
					{#each filteredSchedule as day, dayIndex}
						<div class="relative p-3 day-container">
							<div class="timeline absolute top-0 bottom-0 left-7 w-0.5 bg-white opacity-30" aria-hidden="true"></div>

							{#if format(currentTime, 'eee') === day.day && timelinePosition === -1}
								<div class="relative z-30 flex items-center h-6 px-2 mb-4 text-xs text-white bg-blue-500 rounded timeline-label" aria-label="Current time">
									<div class="absolute left-0 w-0 h-0 -translate-x-full border-t-8 border-b-8 border-r-8 border-transparent border-r-blue-500" aria-hidden="true"></div>
									<span class="ml-1">{format(currentTime, 'HH:mm')}</span>
								</div>
							{/if}

							{#each day.anime as anime, animeIndex}
								<div class="anime-entry relative mt-8! mb-8! first:mt-0 last:mb-0">
									<div class="diamond absolute top-[5px] left-[16px] z-20 h-2 w-2 {currentDay === day.day ? 'bg-blue-500' : 'bg-white'}" aria-hidden="true"></div>

									<div class="time-info mb-2 flex items-center pl-[32px] text-xs">
										<span class="font-medium">{anime.time}</span>
									</div>
									<!-- svelte-ignore a11y-click-events-have-key-events -->

									<a
										href={generateAnimeUrl(anime)}
										class="relative flex items-start cursor-pointer anime-container"
										style="--dominant-color: {titleColors.get(anime.id) || '#ffffff'};"
										on:click={(event) => {
											if (event.button === 0) {
												event.preventDefault();
												handleAnimeClick(anime, event);
											}
										}}
										role="button"
										tabindex="0"
									>
										<img src={anime.poster} alt="" class="z-10 object-cover w-16 h-24 ml-1 mr-3 rounded shadow-md cursor-pointer no-select" />

										<div class="flex flex-col justify-between h-24 min-w-0 grow">
											<div>
												<h3
													class="mt-1 text-sm font-bold text-left transition-colors duration-300 anime-title line-clamp-2"
													style="color: {colorAlwaysOn ? titleColors.get(anime.id) || '#ffffff' : '#ffffff'};"
												>
													{getPreferredTitle(anime, preferRomaji)}
												</h3>
											</div>

											<div class="flex items-center text-sm text-gray-400">
												{#if isLoggedIn}
													<span>LISTA</span>
													<button
														class="ml-2 transition-colors cursor-pointer"
														style="color: {getStatusColor(anime.status, anime)};"
														aria-label={`${anime.status || 'Not in list'} - Edit ${anime.title}`}
													>
														{#if !anime.status || (anime.status === 'planning' && !anime.progress)}
															<svelte:component this={statusIcons.not_in_list} size={16} />
														{:else}
															<svelte:component this={statusIcons[anime.status?.toLowerCase()?.replace('-', '_')] || statusIcons.not_in_list} size={16} />
														{/if}
													</button>
												{/if}
											</div>
										</div>
									</a>
								</div>
								{#if format(currentTime, 'eee') === day.day && timelinePosition === animeIndex}
									<div class="relative z-30 flex items-center h-6 px-2 my-4 text-xs text-white bg-blue-500 rounded timeline-label" aria-label="Current time">
										<div class="absolute left-0 w-0 h-0 -translate-x-full border-t-8 border-b-8 border-r-8 border-transparent border-r-blue-500" aria-hidden="true"></div>
										<span class="ml-1">{format(currentTime, 'HH:mm')}</span>
									</div>
								{/if}
							{/each}
						</div>
					{/each}
				</div>
			</div>
		{:else}
			<!-- Mobile view -->
			<div class="flex flex-col">
				{#each filteredSchedule as day, dayIndex}
					<div class="mb-4 day-{day.day.toLowerCase()}">
						<div class="sticky top-0 z-40 p-2 text-center bg-gray-800">
							<div class="text-sm font-bold {day.day === format(currentTime, 'eee', { locale: pl }) ? 'text-blue-400' : ''}">
								{format(weekDates[dayIndex], 'eee', { locale: pl })}
								<span class="font-light {day.day === format(currentTime, 'eee', { locale: pl }) ? 'text-blue-400' : 'text-gray-300'}"> </span>
							</div>
						</div>

						<div class="relative p-3 day-container">
							<div class="timeline absolute top-0 bottom-0 left-7 w-0.5 bg-white opacity-30" aria-hidden="true"></div>

							{#if format(currentTime, 'eee') === day.day && timelinePosition === -1}
								<div class="relative z-30 flex items-center h-6 px-2 mb-4 text-xs text-white bg-blue-500 rounded timeline-label" aria-label="Current time">
									<div class="absolute left-0 w-0 h-0 -translate-x-full border-t-8 border-b-8 border-r-8 border-transparent border-r-blue-500" aria-hidden="true"></div>
									<span class="ml-1">{format(currentTime, 'HH:mm')}</span>
								</div>
							{/if}

							{#each day.anime as anime, animeIndex}
								<div class="anime-entry relative mt-8! mb-8! first:mt-0 last:mb-0">
									<div class="diamond absolute top-[5px] left-[16px] z-20 h-2 w-2 {currentDay === day.day ? 'bg-blue-500' : 'bg-white'}" aria-hidden="true"></div>

									<div class="time-info mb-2 flex items-center pl-[32px] text-xs">
										<span class="font-medium">{anime.time}</span>
									</div>

									<!-- svelte-ignore a11y-click-events-have-key-events -->
									<div
										class="relative flex items-start cursor-pointer anime-container"
										style="--dominant-color: {titleColors.get(anime.id) || '#ffffff'};"
										on:click={(event) => handleAnimeClick(anime, event)}
										role="button"
										tabindex="0"
									>
										<!-- Add filter to make delayed episodes' posters black and white -->
										<img src={anime.poster} alt={`Poster for ${anime.title}`} class="z-10 object-cover w-16 h-24 ml-1 mr-3 rounded shadow-md cursor-pointer no-select" />

										<div class="flex flex-col justify-between h-24 min-w-0 grow">
											<div>
												<h3
													class="mt-1 text-sm font-bold text-left transition-colors duration-300 anime-title line-clamp-2"
													style="color: {colorAlwaysOn ? titleColors.get(anime.id) || '#ffffff' : '#ffffff'};"
												>
													{getPreferredTitle(anime, preferRomaji)}
												</h3>
											</div>

											<div class="flex items-center text-sm text-gray-400">
												<span>LISTA</span>
												{#if isLoggedIn}
													<button
														class="ml-2 transition-colors cursor-pointer"
														style="color: {getStatusColor(anime.status, anime)};"
														aria-label={`${anime.status || 'Not in list'} - Edit ${anime.title}`}
													>
														{#if !anime.status || (anime.status === 'planning' && !anime.progress)}
															<svelte:component this={statusIcons.not_in_list} size={16} />
														{:else}
															<svelte:component this={statusIcons[anime.status?.toLowerCase()?.replace('-', '_')] || statusIcons.not_in_list} size={16} />
														{/if}
													</button>
												{/if}
											</div>
										</div>
									</div>
								</div>
								{#if format(currentTime, 'eee') === day.day && timelinePosition === animeIndex}
									<div class="relative z-30 flex items-center h-6 px-2 my-4 text-xs text-white bg-blue-500 rounded timeline-label" aria-label="Aktualny czas">
										<div class="absolute left-0 w-0 h-0 -translate-x-full border-t-8 border-b-8 border-r-8 border-transparent border-r-blue-500" aria-hidden="true"></div>
										<span class="ml-1">{format(currentTime, 'HH:mm')}</span>
									</div>
								{/if}
							{/each}
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>
</div>

{#if showStremioModal}
	<MoreModal {handleCloseStremioModal} />
{/if}

<AnimeEditDialog bind:open={dialogOpen} anime={selectedAnime} onClose={closeDialog} onSave={saveChanges} onDelete={deleteEntry} />
<UserProfileModal bind:open={showUserModal} user={null} onClose={handleCloseModal} />

<style>
	:root {
		--header-height: 3.5rem;
		--navbar-width: 4rem;
	}

	.schedule-container {
		position: relative;
		background-color: rgb(17, 24, 39);
		color: white;
	}

	.header-section {
		position: sticky;
		top: 0;
		z-index: 50;
		padding: 0.5rem 1rem;
		border-bottom: 1px solid rgb(55, 65, 81);
		background-color: rgb(17, 24, 39);
	}

	.schedule-content {
		flex: 1;
		min-height: 0;
		overflow: auto;
		position: relative;
		-webkit-overflow-scrolling: touch;
	}

	.schedule-grid {
		width: 100%;
		min-width: 1400px;
		height: 100%;
		position: relative;
	}

	.week-header {
		position: sticky;
		top: 0;
		z-index: 40;
		background-color: rgb(31, 41, 55);
		border-bottom: 1px solid rgb(55, 65, 81);
	}

	.days-grid {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		gap: 0.5rem;
		padding: 0.5rem;
	}

	.timeline {
		position: absolute;
		left: 31px;
		top: 0;
		bottom: 0;
		width: 2px;
		background-color: rgba(255, 255, 255, 0.3);
	}

	.timeline-label {
		position: relative;
		z-index: 30;
		margin: 1rem 0;
		height: 1.5rem;
		display: flex;
		align-items: center;
		background-color: rgb(59, 130, 246);
		padding: 0 0.5rem;
		font-size: 0.75rem;
		color: white;
		border-radius: 0.25rem;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		width: fit-content;
		min-width: 60px;
		max-width: 80px;
		margin-left: 20px;
	}

	.anime-entry {
		position: relative;
		margin: 2rem 0;
	}

	.anime-entry:first-child {
		margin-top: 0;
	}

	.anime-entry:last-child {
		margin-bottom: 0;
	}

	.anime-container {
		position: relative;
		display: flex;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.anime-container:hover {
		transform: translateY(-2px);
	}

	.anime-container:active {
		transform: translateY(0);
	}

	.diamond {
		position: absolute;
		left: 16px;
		top: 5px;
		height: 8px;
		width: 8px;
		transform: rotate(45deg);
		z-index: 20;
	}

	.status-badge {
		display: flex;
		align-items: center;
		gap: 0.25rem;
		padding: 0.5rem;
		border-radius: 9999px;
		font-size: 0.75rem;
	}

	.anime-title {
		transition: color 0.3s ease;
	}

	.anime-container:hover .anime-title {
		color: var(--dominant-color, #ffffff) !important;
	}

	.time-info {
		display: flex;
		align-items: center;
		margin-bottom: 0.5rem;
	}

	.schedule-content::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	.schedule-content::-webkit-scrollbar-track {
		background: rgba(0, 0, 0, 0.1);
	}

	.schedule-content::-webkit-scrollbar-thumb {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 4px;
	}

	.schedule-content::-webkit-scrollbar-thumb:hover {
		background: rgba(255, 255, 255, 0.3);
	}

	.schedule-content {
		scrollbar-width: thin;
		scrollbar-color: rgba(255, 255, 255, 0.2) rgba(0, 0, 0, 0.1);
	}

	@media (max-width: 640px) {
		.schedule-grid {
			min-width: auto;
			width: 100%;
			-webkit-overflow-scrolling: touch;
			scroll-behavior: smooth;
		}

		.days-grid {
			display: block;
		}

		.day-container {
			padding: 0.5rem 1rem;
		}

		.anime-entry {
			margin: 0.75rem 0;
		}

		.time-info {
			padding-left: 1.5rem;
		}

		.diamond {
			left: 0.75rem;
		}

		.anime-container {
			min-width: 0;
			padding-right: 1rem;
		}

		.anime-title {
			max-width: calc(100vw - 7rem);
			font-size: 0.875rem;
		}

		.time-info {
			font-size: 0.75rem;
		}

		.schedule-content {
			overflow-x: hidden;
			overflow-y: auto;
		}
	}

	.no-select {
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes slideUp {
		from {
			transform: translateY(10px);
			opacity: 0;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}

	.fade-in {
		animation: fadeIn 0.3s ease-out;
	}

	.slide-up {
		animation: slideUp 0.3s ease-out;
	}

	.status-badge:hover {
		background-color: rgba(255, 255, 255, 0.1);
	}

	.anime-container:focus {
		outline: 2px solid rgb(59, 130, 246);
		outline-offset: 2px;
	}

	@media (hover: none) {
		.anime-container:hover {
			transform: none;
		}

		.anime-container:active {
			transform: translateY(2px);
		}
	}
</style>
