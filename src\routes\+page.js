export const ssr = false;
import { browser } from '$app/environment';
import { cacheKeys, getCachedData, setCachedData } from '$lib/utils/cacheUtils';

const ANILIST_API = 'https://graphql.anilist.co';

// Keep existing AniList query
const WATCHING_QUERY = `
query ($userId: Int) {
  MediaListCollection(userId: $userId, type: ANIME, status: CURRENT) {
    lists {
      entries {
        id
        mediaId
        progress
        updatedAt
        media {
          id
          title {
            romaji
            english
          }
          coverImage {
            large
          }
          episodes
        }
      }
    }
  }
}
`;

// Standardize data from AniList format
function mapAniListEntryToCommon(entry, coverImage, latestEpisode) {
  const updatedAt = new Date(entry.updatedAt * 1000);
  const validDate = !isNaN(updatedAt.getTime()) ?
    updatedAt.toISOString() :
    new Date().toISOString();

  return {
    id: entry.mediaId,
    title: entry.media.title.romaji,
    english_title: entry.media.title.english,
    image: coverImage,
    current_episode: entry.progress,
    total_episodes: entry.media.episodes,
    updated_at: validDate,
    released_episodes: latestEpisode || 0
  };
}

// Standardize data from MAL format
function mapMALEntryToCommon(entry, coverImage, latestEpisode, anilistId) {
  const updatedAt = new Date(entry.list_status.updated_at * 1000);
  const validDate = !isNaN(updatedAt.getTime()) ?
    updatedAt.toISOString() :
    new Date().toISOString();
  return {
    id: entry.node.id,
    anilist_id: anilistId, // Add the mapped AniList ID
    title: entry.node.title,
    english_title: entry.node.title,
    image: coverImage || entry.node.main_picture?.large || entry.node.main_picture?.medium,
    current_episode: entry.list_status.num_episodes_watched,
    total_episodes: entry.node.num_episodes || 0,
    updated_at: validDate,
    released_episodes: latestEpisode || entry.node.num_episodes || 0
  };
}

async function fetchAnilistMappings(malIds) {
  if (!malIds.length) return new Map();

  try {
    const response = await fetch('/api/mal/mapping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ malIds })
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch AniList mappings: ${response.status}`);
    }

    const { mappings } = await response.json();
    return new Map(mappings.map(m => [m.mal_id, m.anilist_id]));
  } catch (error) {
    console.error('Error fetching AniList mappings:', error);
    return new Map();
  }
}

// Keep existing AniList function with minor updates
async function fetchAniListWatching(token, userId) {
  const cacheKey = `${cacheKeys.ANILIST_WATCHING}`;
  if (browser) {
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  const response = await fetch(ANILIST_API, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      query: WATCHING_QUERY,
      variables: { userId: parseInt(userId) }
    })
  });

  if (!response.ok) {
    throw new Error(`AniList API returned ${response.status}`);
  }

  const data = await response.json();
  if (data.errors) {
    throw new Error(data.errors[0]?.message || 'AniList API returned errors');
  }

  const entries = data.data.MediaListCollection.lists[0]?.entries || [];

  if (browser) {
    setCachedData(cacheKey, entries);
  }

  return entries;
}

// Add new function for MAL
async function fetchMALWatching(token, username) {
  const cacheKey = `${cacheKeys.MAL_WATCHING}`;
  if (browser) {
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  // Use our server as a proxy instead of direct MAL API access
  const response = await fetch('/api/mal/proxy/watching', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      token
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch MAL watching list: ${response.status}`);
  }

  const data = await response.json();
  const allEntries = data.entries || [];

  if (browser) {
    setCachedData(cacheKey, allEntries);
  }

  return allEntries;
}

export const load = async ({ fetch, parent }) => {
  const { user } = await parent();

  const fetchHomeData = async () => {
    if (browser) {
      const cachedData = getCachedData(cacheKeys.HOME_DATA);
      if (cachedData) {
        return cachedData;
      }
    }

    const homeResponse = await fetch('/api/home');
    if (!homeResponse.ok) {
      throw new Error('Failed to load home data');
    }
    const homeData = await homeResponse.json();

    // Detect which service the user is logged in with
    const provider = user?.user_metadata?.provider;
    const isLoggedIn = !!user?.user_metadata;
    const isAniList = provider === 'anilist' && user?.user_metadata?.anilist_token;
    const isMAL = provider === 'mal' && user?.user_metadata?.mal_token;

    if (isLoggedIn) {
      try {
        // Different handling based on provider
        if (isAniList) {
          await processAniListData(user, homeData);
        } else if (isMAL) {
          await processMALData(user, homeData);
        }
      } catch (error) {
        console.error('Error processing user data:', error);
        // Silently handle errors to prevent disrupting the main flow
      }
    }

    if (browser) {
      setCachedData(cacheKeys.HOME_DATA, homeData);
    }

    return homeData;
  };

  // Extract AniList processing logic
  async function processAniListData(user, homeData) {
    const validIdsResponse = await fetch('/api/anilist/watching');
    if (!validIdsResponse.ok) {
      throw new Error('Failed to fetch valid anime IDs');
    }
    const { validAnimeIds } = await validIdsResponse.json();

    const anilistEntries = await fetchAniListWatching(
      user.user_metadata.anilist_token,
      user.user_metadata.id
    );

    const validEntries = anilistEntries.filter(entry =>
      validAnimeIds.includes(entry.mediaId)
    );

    if (validEntries.length && homeData.continueWatchingData) {
      // Get latest episodes
      const latestEpisodes = await fetchLatestEpisodes(
        validEntries.map(entry => entry.mediaId),
        'anilist'
      );

      // Get metadata - pass 'anilist' as provider
      const metadata = await fetchAnimeMetadata(
        validEntries.map(entry => entry.mediaId),
        'anilist'
      );

      // Create metadata maps
      const latestEpisodesMap = new Map(
        latestEpisodes.map(ep => [ep.anilist_id, ep.latest_episode])
      );

      const metadataMap = new Map(
        metadata.map(m => [m.anilist_id, m.cover_image])
      );

      // Map entries to common format
      const aniListMap = new Map(
        validEntries.map(entry => {
          const commonEntry = mapAniListEntryToCommon(
            entry,
            metadataMap.get(entry.mediaId),
            latestEpisodesMap.get(entry.mediaId)
          );
          return [entry.mediaId, commonEntry];
        })
      );

      // Merge with local data
      mergeWithLocalData(homeData, aniListMap);
    }
  }

  // New MAL processing logic
  async function processMALData(user, homeData) {
    const validIdsResponse = await fetch('/api/mal/watching');
    if (!validIdsResponse.ok) {
      throw new Error('Failed to fetch valid anime IDs');
    }
    const { validAnimeIds } = await validIdsResponse.json();

    const malEntries = await fetchMALWatching(
      user.user_metadata.mal_token,
      user.user_metadata.name
    );

    const validEntries = malEntries.filter(entry =>
      validAnimeIds.includes(entry.node.id)
    );

    if (validEntries.length && homeData.continueWatchingData) {
      // Get MAL IDs to fetch mappings
      const malIds = validEntries.map(entry => entry.node.id);

      // Fetch AniList to MAL ID mappings
      const anilistMappings = await fetchAnilistMappings(malIds);

      // Get latest episodes
      const latestEpisodes = await fetchLatestEpisodes(
        validEntries.map(entry => entry.node.id),
        'mal'
      );

      // Get metadata - pass 'mal' as provider
      const metadata = await fetchAnimeMetadata(
        validEntries.map(entry => entry.node.id),
        'mal'
      );

      // Create metadata maps
      const latestEpisodesMap = new Map(
        latestEpisodes.map(ep => [ep.mal_id, ep.latest_episode])
      );

      const metadataMap = new Map(
        metadata.map(m => [m.mal_id, m.cover_image])
      );

      // Map entries to common format
      const malMap = new Map(
        validEntries.map(entry => {
          const malId = entry.node.id;
          const anilistId = anilistMappings.get(malId) || null;

          const commonEntry = mapMALEntryToCommon(
            entry,
            metadataMap.get(malId),
            latestEpisodesMap.get(malId),
            anilistId
          );
          return [malId, commonEntry];
        })
      );

      // Merge with local data
      mergeWithLocalData(homeData, malMap);
    }
  }

  // Shared helper functions
  async function fetchLatestEpisodes(animeIds, provider) {
    const cacheKey = `${provider === 'anilist' ? cacheKeys.ANILIST_LATEST : cacheKeys.MAL_LATEST}`;
    let latestEpisodes = browser ? getCachedData(cacheKey) : null;

    if (!latestEpisodes) {
      const endpoint = provider === 'anilist' ? '/api/anilist/latest' : '/api/mal/latest';
      const latestEpisodesResponse = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ animeIds })
      });

      const data = await latestEpisodesResponse.json();
      latestEpisodes = data.latestEpisodes;

      if (browser) {
        setCachedData(cacheKey, latestEpisodes);
      }
    }

    return latestEpisodes;
  }

  async function fetchAnimeMetadata(animeIds, provider) {
    // If provider isn't specified, determine from user store
    if (!provider) {
      provider = $userStore?.user_metadata?.provider || 'anilist';
    }

    // Generate cache key based on provider and IDs
    const metadataCacheKey = `${provider === 'anilist' ? cacheKeys.ANILIST_METADATA : cacheKeys.MAL_METADATA}`;
    let metadata = browser ? getCachedData(metadataCacheKey) : null;

    if (!metadata) {
      const metadataResponse = await fetch('/api/anime/metadata', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          animeIds,
          provider
        })
      });

      const data = await metadataResponse.json();
      metadata = data.metadata;

      if (browser) {
        setCachedData(metadataCacheKey, metadata);
      }
    }

    return metadata;
  }

  function mergeWithLocalData(homeData, serviceMap) {
    // Just use the entries from the service map directly
    const combinedWatching = Array.from(serviceMap.values());

    // Sort by most recently updated
    homeData.continueWatchingData = combinedWatching.sort((a, b) =>
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    );
  }

  return {
    streamed: {
      homeData: fetchHomeData().catch(error => {
        throw error;
      })
    }
  };
};