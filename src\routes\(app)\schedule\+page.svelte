<script>
	//src/routes/schedule/+page.svelte
	import NavbarDesktop from '$lib/components/sections/navbar/NavbarDesktop.svelte';
	import NavbarMobile from '$lib/components/sections/navbar/NavbarMobile.svelte';
	import { onMount } from 'svelte';
	import ScheduleContainer from '$lib/components/containers/schedule/ScheduleContainer.svelte';
	import { browser } from '$app/environment';

	export let data;
	let preferRomaji;
	if (!data.userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = data.userSettings.titleLanguage === 'romaji';
	}
	let mounted = false;

	onMount(() => {
		mounted = true;
	});

	// Define reactive title and description
	$: title = 'Harmonogram - Lycoris';
	$: description = 'Sprawdź kiedy wychodzą nowe odcinki anime. Śledź nadchodzące premiery i nie przegap żadnego odcinka na lycoris.cafe';
	$: imageUrl = 'https://pixeldrain.com/api/file/nzfyjq8f';
	$: canonicalUrl = browser ? window.location.href : 'https://lycoris.cafe/schedule';
</script>

<svelte:head>
	<!-- Basic metadata -->
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- OpenGraph tags -->
	<meta name="twitter:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={imageUrl} />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content="Lycoris.cafe - Harmonogram anime" />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="website" />
	<meta property="og:site_name" content="Lycoris" />

	<!-- Twitter Card tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={imageUrl} />
	<meta name="twitter:image:alt" content="Lycoris.cafe - Harmonogram anime" />

	<!-- Canonical URL -->
	<link rel="canonical" href={canonicalUrl} />

	<!-- Additional SEO metadata -->
	<meta name="keywords" content="anime, harmonogram, premiery anime, lycoris, lycoris cafe, tygodniowe emisje" />
	<meta name="theme-color" content="#ee8585" />
	<meta name="author" content="Lycoris" />
	<meta name="robots" content="index, follow" />
</svelte:head>

{#if mounted}
	<div class="bg-gray-900 opacity-100" id="main">
		<ScheduleContainer {preferRomaji} scheduleData={data.scheduleData} />
	</div>
{/if}

<style>
</style>
