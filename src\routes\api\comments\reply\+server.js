import { json, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY, CLAUDE_API_KEY } from '$env/static/private';
import DOMPurify from 'isomorphic-dompurify';
import Anthropic from '@anthropic-ai/sdk';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const anthropic = new Anthropic({
  apiKey: CLAUDE_API_KEY,
});

const MAX_COMMENT_LENGTH = 5000;
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_COMMENTS_PER_WINDOW = 10;

async function isUserBanned(userId) {
  const { data, error } = await supabase
    .from('profiles')
    .select('banned, banned_reason')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error checking ban status:', error);
    return { isBanned: false, reason: null };
  }

  return {
    isBanned: data?.banned || false,
    reason: data?.banned_reason
  };
}

async function checkForSpoilers(content) {
  try {
    const message = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 1024,
      messages: [{
        role: 'user',
        content: `Analyze this comment for an anime and determine if it contains spoilers.

Comment: "${content}"

Respond only with a JSON object:
{
  "isSpoiler": boolean,
}`
      }]
    });

    return JSON.parse(message.content[0].text);
  } catch (error) {
    console.error('Error checking for spoilers:', error);
    return { isSpoiler: false };
  }
}

async function checkForSpam(content, userHistory) {
  try {
    const message = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 1024,
      messages: [{
        role: 'user',
        content: `Analyze this comment and determine if it's likely spam. Consider:
- Promotional content or links
- Repetitive text
- Irrelevant content
- Suspicious patterns
- User's posting history: ${JSON.stringify(userHistory)}

Comment: "${content}"

Respond only with a JSON object:
{
  "isSpam": boolean,
  "confidence": number,
  "reason": string
}`
      }]
    });

    return JSON.parse(message.content[0].text);
  } catch (error) {
    console.error('Error checking for spam:', error);
    return { isSpam: false, confidence: 0, reason: '' };
  }
}

export async function POST({ request, locals }) {
  try {
    const { session, user } = await locals.safeGetSession();
    if (!session || !user) {
      throw error(401, 'Zaloguj się aby móc komentować');
    }

    // Check if user is banned
    const { isBanned, reason } = await isUserBanned(user.id);
    if (isBanned) {
      throw error(403, {
        message: 'Twoje konto zostało zbanowane, powód:',
        details: { reason }
      });
    }

    // Check rate limiting
    const recentComments = await supabase
      .from('comments')
      .select('created_at')
      .eq('user_id', user.id)
      .gte('created_at', new Date(Date.now() - RATE_LIMIT_WINDOW).toISOString())
      .limit(MAX_COMMENTS_PER_WINDOW);

    if (recentComments.data?.length >= MAX_COMMENTS_PER_WINDOW) {
      throw error(429, 'Zbyt szybko komentujesz, odczekaj chwilę i spróbuj ponownie.');
    }

    const { parentId, content, anilistId, episodeNumber } = await request.json();

    if (!content?.trim()) {
      throw error(400, 'Treść komentarza jest wymagana');
    }

    if (content.trim().length > MAX_COMMENT_LENGTH) {
      throw error(400, `Komentarz przekracza maksymalną długość ${MAX_COMMENT_LENGTH} znkaów`);
    }

    if (!parentId) {
      throw error(400, 'Parent comment ID is required');
    }

    // Verify parent comment exists
    const { data: parentComment, error: parentError } = await supabase
      .from('comments')
      .select('id')
      .eq('id', parentId)
      .single();

    if (parentError || !parentComment) {
      throw error(404, 'Parent comment not found');
    }

    // Get user's recent posting history
    const { data: userHistory } = await supabase
      .from('comments')
      .select('content, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    const sanitizedContent = DOMPurify.sanitize(content.trim());

    // Check for spoilers
    const spoilerCheck = await checkForSpoilers(sanitizedContent);

    // Check for spam
    const spamCheck = await checkForSpam(sanitizedContent, userHistory);

    // Create the reply
    const { data: newReply, error: insertError } = await supabase
      .from('comments')
      .insert({
        parent_id: parentId,
        user_id: user.id,
        author: user.user_metadata.name,
        avatar: user.user_metadata.avatar,
        content: sanitizedContent,
        anilist_id: anilistId,
        episode_number: episodeNumber,
        created_at: new Date().toISOString(),
        is_ai_detected_spoiler: spoilerCheck.isSpoiler,
        is_spam: spamCheck.isSpam,
        is_pending_review: spamCheck.isSpam,
        likes: 0,
        dislikes: 0
      })
      .select()
      .single();

    if (insertError) throw insertError;

    // Format response
    const response = {
      ...newReply,
      likes: 0,
      dislikes: 0,
      user_reactions: [],
      message: newReply.is_pending_review ? 'Twój komentarz oczekuje na akceptację przez moderatora.' : undefined
    };

    return json(response);
  } catch (err) {
    // console.error('Error in comment submission:', {
    //   error: err,
    //   message: err.message,
    //   status: err.status,
    //   stack: err.stack
    // });

    // Log additional context if available
    if (err.details) {
      console.error('Additional error details:', err.details);
    }
    if (err.hint) {
      console.error('Database hint:', err.hint);
    }
    if (err.code) {
      console.error('Error code:', err.code);
    }
    throw error(err.status || 500, {
      message: err.body.message || 'Error posting comment',
      details: err.body.details.reason || {},
      timestamp: new Date().toISOString()
    });
  }
}