// src/routes/auth/delete-account/+server.js
import { error, redirect } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabaseAdmin = createClient(
  PUBLIC_SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST({ locals }) {
  const { session } = await locals.safeGetSession();

  if (!session) {
    throw error(401, 'Unauthorized');
  }

  try {
    // Delete user's comment reactions
    const { error: reactionsDeleteError } = await supabaseAdmin
      .from('comment_reactions')
      .delete()
      .match({ user_id: session.user.id });

    if (reactionsDeleteError) {
      console.error('Failed to delete comment reactions:', reactionsDeleteError);
      throw error(500, 'Failed to delete comment reactions');
    }

    // Delete user's comment reports
    const { error: reportsDeleteError } = await supabaseAdmin
      .from('comment_reports')
      .delete()
      .match({ reporter_id: session.user.id });

    if (reportsDeleteError) {
      console.error('Failed to delete comment reports:', reportsDeleteError);
      throw error(500, 'Failed to delete comment reports');
    }

    // Delete user's comments
    const { error: commentsDeleteError } = await supabaseAdmin
      .from('comments')
      .delete()
      .match({ user_id: session.user.id });

    if (commentsDeleteError) {
      console.error('Failed to delete comments:', commentsDeleteError);
      throw error(500, 'Failed to delete comments');
    }

    // Delete user's profile
    const { error: profileDeleteError } = await supabaseAdmin
      .from('profiles')
      .delete()
      .match({ id: session.user.id });

    if (profileDeleteError) {
      console.error('Failed to delete profile:', profileDeleteError);
      throw error(500, 'Failed to delete profile');
    }

    // Delete user using admin client
    const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(
      session.user.id
    );

    if (deleteError) {
      console.error('Failed to delete user:', deleteError);
      throw error(500, 'Failed to delete account');
    }

    // Sign out the user
    await locals.supabase.auth.signOut();

    return new Response(null, { status: 200 });
  } catch (err) {
    console.error('Error deleting account:', err);
    throw error(500, 'An error occurred while deleting account');
  }
}