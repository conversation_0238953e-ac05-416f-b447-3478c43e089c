<script>
	import { onMount, onDestroy } from 'svelte';

	export let data;

	onMount(() => {
		if (data && data.url) {
			downloadFile();
		}
	});

	function downloadFile() {
		// Create a temporary anchor element
		const a = document.createElement('a');
		a.href = data.url;
		a.download = data.download;

		// Append to the body, click, and remove (for compatibility)
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);

		// Clean up after a short delay to ensure the download starts
		setTimeout(() => {
			if (data.cleanup) {
				data.cleanup();
			}
		}, 100);
	}

	// Clean up on component destruction
	onDestroy(() => {
		if (data && data.cleanup) {
			data.cleanup();
		}
	});
</script>

{#if data && data.url}
	<p>File download for "{data.download}" should start automatically.</p>
{/if}
