{"openapi": "3.0.0", "info": {"title": "Lycoris Cafe API", "version": "1.0.0"}, "servers": [{"url": "{subdomain}.lycoris.cafe", "variables": {"subdomain": {"default": "api", "description": "The subdomain for the API", "pattern": "^[a-zA-Z0-9-]+$"}}}, {"url": "lycoris.cafe"}], "paths": {"/api/getLink": {"get": {"summary": "Get link information", "parameters": [{"name": "link", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Link identifier"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}}}}}}}}}}}