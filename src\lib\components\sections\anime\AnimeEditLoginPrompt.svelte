<script>
	import * as Dialog from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';
	import { X } from 'lucide-svelte';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';

	export let open = false;
	export let onClose;

	let showUserModal = false;

	function handleLoginPrompt() {
		open = false;
		onClose();
		showUserModal = true;
	}

	function handleCloseModal() {
		showUserModal = false;
	}
</script>

<Dialog.Root
	{open}
	onOpenChange={(newOpen) => {
		open = newOpen;
		if (!newOpen) onClose();
	}}
>
	<Dialog.Content class="flex w-full max-w-md flex-col overflow-hidden bg-gray-900/80 p-6 backdrop-blur-xs">
		<div class="relative">
			<Dialog.Close></Dialog.Close>

			<div class="mt-4 mb-8 flex flex-col items-center text-center">
				<h2 class="mb-4 text-2xl font-bold">Zaloguj się</h2>
				<p class="mb-6 text-gray-300"><PERSON><PERSON><PERSON> by<PERSON> zalogowany, <br />aby edytowa<PERSON> swoją listę anime.</p>
				<Button
					variant="outline"
					class="w-full max-w-xs rounded bg-[#ee8585] px-3 py-2 text-sm font-bold text-black transition-colors duration-300 ease-in-out hover:bg-[#8ec3f4] hover:text-black"
					on:click={handleLoginPrompt}
				>
					Zaloguj się
				</Button>
			</div>
		</div>
	</Dialog.Content>
</Dialog.Root>

<UserProfileModal bind:open={showUserModal} user={null} onClose={handleCloseModal} />
