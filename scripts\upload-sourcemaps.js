#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';
import dotenv from 'dotenv';
dotenv.config();
/**
 * <PERSON>ript to upload source maps to PostHog
 * This script should be run after the build process
 */

const BUILD_DIR = './build';
const CLIENT_DIR = path.join(BUILD_DIR, 'client');

function log(message) {
  console.log(`[PostHog Sourcemaps] ${message}`);
}

function error(message) {
  console.error(`[PostHog Sourcemaps ERROR] ${message}`);
}

function checkEnvironmentVariables() {
  const requiredVars = ['POSTHOG_CLI_ENV_ID', 'POSTHOG_CLI_TOKEN'];
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    error(`Missing required environment variables: ${missing.join(', ')}`);
    error('Please set POSTHOG_CLI_ENV_ID and POSTHOG_CLI_TOKEN');
    error('POSTHOG_CLI_ENV_ID should be your PostHog project ID (environment ID)');
    error('POSTHOG_CLI_TOKEN should be a personal API key with error tracking write scope');
    error('');
    error('To find your environment ID:');
    error('1. Go to your PostHog project settings');
    error('2. Look for "Environment ID" or check the URL: /project/{ENV_ID}');
    error('');
    error('To create a personal API key:');
    error('1. Go to PostHog Settings > Personal API Keys');
    error('2. Create new key with "error tracking write" scope');
    process.exit(1);
  }

  // Validate the format of environment variables
  const envId = process.env.POSTHOG_CLI_ENV_ID;
  const token = process.env.POSTHOG_CLI_TOKEN;

  if (!/^\d+$/.test(envId)) {
    error(`POSTHOG_CLI_ENV_ID should be a numeric environment ID, got: ${envId}`);
    process.exit(1);
  }

  if (!token.startsWith('phx_')) {
    error(`POSTHOG_CLI_TOKEN should start with 'phx_', got: ${token.substring(0, 10)}...`);
    error('Make sure you are using a Personal API Key, not a Project API Key');
    process.exit(1);
  }
}

function checkBuildDirectory() {
  if (!existsSync(BUILD_DIR)) {
    error(`Build directory ${BUILD_DIR} does not exist. Please run 'npm run build' first.`);
    process.exit(1);
  }

  if (!existsSync(CLIENT_DIR)) {
    error(`Client build directory ${CLIENT_DIR} does not exist.`);
    process.exit(1);
  }
}

function getReleaseName() {
  // Try to get release name from package.json
  try {
    const fs = require('fs');
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const name = packageJson.name || 'unknown';
    const version = packageJson.version || '1.0.0';
    return `${name}@${version}`;
  } catch (err) {
    return 'lycoris-cafe@1.0.0'; // fallback
  }
}

function runCommand(command, description) {
  try {
    log(`${description}...`);

    // Add host parameter if POSTHOG_HOST is set
    const hostFlag = process.env.POSTHOG_HOST ? ` --host ${process.env.POSTHOG_HOST}` : '';

    // Add release name to avoid the warning
    const releaseName = getReleaseName();
    const releaseFlag = '';

    const fullCommand = command + hostFlag + releaseFlag;

    log(`Running: ${fullCommand}`);

    execSync(fullCommand, {
      stdio: 'inherit',
      cwd: process.cwd(),
      env: {
        ...process.env,
        // Ensure environment variables are passed
        POSTHOG_CLI_ENV_ID: process.env.POSTHOG_CLI_ENV_ID,
        POSTHOG_CLI_TOKEN: process.env.POSTHOG_CLI_TOKEN
      }
    });
    log(`${description} completed successfully.`);
  } catch (err) {
    error(`Failed to ${description.toLowerCase()}: ${err.message}`);
    if (err.status === 1) {
      error('This might be due to:');
      error('1. Invalid POSTHOG_CLI_TOKEN (should start with "phx_")');
      error('2. Incorrect POSTHOG_CLI_ENV_ID (should be numeric environment ID)');
      error('3. API key missing "error tracking write" scope');
      error('4. Wrong PostHog host URL');
    }
    process.exit(1);
  }
}

function main() {
  log('Starting PostHog source map upload process...');

  // Check prerequisites
  checkEnvironmentVariables();
  checkBuildDirectory();

  // Inject PostHog metadata into built files
  runCommand(
    `npx posthog-cli sourcemap inject --directory ${CLIENT_DIR}`,
    'Injecting PostHog metadata into built files'
  );

  // Upload source maps to PostHog
  runCommand(
    `npx posthog-cli sourcemap upload --directory ${CLIENT_DIR}`,
    'Uploading source maps to PostHog'
  );

  log('PostHog source map upload completed successfully!');
  log('Your error stack traces in PostHog will now show unminified code.');
}

// Run the script
main();
