//src/routes/api/anilist/watching/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabaseAdmin = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ locals }) {
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ watching: [] });
  }

  try {
    const { data: dbEntries, error } = await supabaseAdmin
      .from('anime_metadata')
      .select('anilist_id');

    if (error) {
      console.error('Database query error:', error);
      return json({ error: 'Failed to fetch database entries' }, { status: 500 });
    }

    return json({ 
      validAnimeIds: dbEntries.map(entry => entry.anilist_id) 
    });

  } catch (error) {
    console.error('Error fetching valid anime IDs:', error);
    return json({
      validAnimeIds: [],
      error: error.message
    });
  }
}