<script>
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Avatar } from '$lib/components/ui/avatar';
	import { LogOut, Settings, User } from 'lucide-svelte';
	import { goto } from '$app/navigation';
	import { userStore as userLogin } from '$lib/stores/userLogin';

	let username = $userLogin?.username || 'Gość';
	let email = $userLogin?.email || 'gosc@przykład.com';
	let avatarUrl = $userLogin?.avatar || '/default-avatar.svg';
</script>

<style>
	:global(.user-nav-dropdown) {
		z-index: 50;
	}

	:global(.user-nav-item:hover) {
		background-color: var(--muted);
	}

	:global(.user-nav-item:focus) {
		outline: none;
		background-color: var(--muted);
	}
</style>
