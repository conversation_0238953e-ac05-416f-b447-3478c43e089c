<script>
	import '/src/app.css';
	import { fade } from 'svelte/transition';
	import { page } from '$app/stores';
	import Footer from '$lib/components/sections/shared/Footer.svelte';
	import Modal from '$lib/components/sections/comments/Modal.svelte';
	import { onMount } from 'svelte';
	export let data;
	import { beforeNavigate, afterNavigate } from '$app/navigation';
	import { browser } from '$app/environment';
	import { dev } from '$app/environment';
	import { invalidate } from '$app/navigation';
	import { Toaster } from '$lib/components/ui/sonner';
	import posthog from 'posthog-js';
	import { userStore } from '$lib/stores/userLogin';
	import settingsStore, { loadSettings } from '$lib/stores/settingsStore';
	$: isLoggedIn = $userStore?.role === 'authenticated';
	$: enableOneko = $settingsStore.enableOneko;

	function loadCSS(href) {
		const link = document.createElement('link');
		link.rel = 'stylesheet';
		link.href = href;
		document.head.appendChild(link);
	}

	onMount(() => {
		if (isLoggedIn) {
			posthog.identify(
				$userStore.id,
				{ email: $userStore.user_metadata.name } // optional: set additional person properties
			);
			umami.identify($userStore.user_metadata.name);

			// Load user settings
			loadSettings();
		}

		/* THROW SOME LIBRARY TO DECTED DEV TOOLS HERE */
		const {
			data: { subscription }
		} = data.supabase.auth.onAuthStateChange(async (_, newSession) => {
			if (newSession?.expires_at !== data.session?.expires_at) {
				// Verify the user data by calling getUser
				const {
					data: { user },
					error
				} = await data.supabase.auth.getUser();
				if (!error && user) {
					invalidate('supabase:auth');
				}
			}
		});

		loadCSS('https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&display=swap');

		return () => subscription.unsubscribe();
	});

	if (browser) {
		// beforeNavigate(() => posthog.capture('$pageleave'));
		// afterNavigate(() => posthog.capture('$pageview'));
	}
</script>

<svelte:head>
	{#if browser && enableOneko}
		<script src="/oneko.js"></script>
	{/if}
</svelte:head>

<Modal />
<div class="relative">
	{#key data.url}
		<div in:fade={{ duration: 300, delay: 300 }} out:fade={{ duration: 300 }}>
			<Toaster theme="dark" richColors closeButton />
			<slot />
		</div>
	{/key}
</div>
