//src/routes/schedule/+page.js
export const ssr = false;

const ANILIST_API = 'https://graphql.anilist.co';

const MEDIA_LIST_QUERY = `
query ($userId: Int!, $mediaIds: [Int]) {
  Page(page: 1, perPage: 50) {
    mediaList(userId: $userId, mediaId_in: $mediaIds) {
      mediaId
      status
      score
      progress
      startedAt {
        year
        month
        day
      }
      completedAt {
        year
        month
        day
      }
      updatedAt
    }
  }
}`;

async function fetchAnilistStatuses(token, userId, mediaIds) {
  try {
    const response = await fetch(ANILIST_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        query: MEDIA_LIST_QUERY,
        variables: {
          userId: parseInt(userId),
          mediaIds: mediaIds.map(id => parseInt(id))
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch Anilist data: ${errorText}`);
    }

    const data = await response.json();
    return data.data.Page.mediaList || [];
  } catch (error) {
    console.error('Error fetching Anilist statuses:', error);
    return [];
  }
}

async function fetchMALStatuses(token, mediaIds) {
  try {
    // Use server proxy instead of direct MAL API access
    const response = await fetch('/api/mal/proxy/batch-statuses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        animeIds: mediaIds,
        token
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch MAL statuses');
    }

    const data = await response.json();
    return data.statuses;
  } catch (error) {
    console.error('Error fetching MAL statuses:', error);
    return [];
  }
}

// Helper function to map MAL status to AniList format
function mapMALStatusToAniList(status) {
  switch (status) {
    case 'watching': return 'CURRENT';
    case 'completed': return 'COMPLETED';
    case 'on_hold': return 'PAUSED';
    case 'dropped': return 'DROPPED';
    case 'plan_to_watch': return 'PLANNING';
    default: return 'PLANNING';
  }
}

// Helper function to parse MAL date format (YYYY-MM-DD) to object
function parseMALDate(dateString) {
  try {
    const [year, month, day] = dateString.split('-').map(Number);
    return { year, month, day };
  } catch (e) {
    return null;
  }
}

export async function load({ fetch, parent }) {
  try {
    const { session, user } = await parent();
    const response = await fetch('/api/schedule');

    if (!response.ok) {
      throw new Error('Failed to fetch schedule data');
    }

    let { scheduleData } = await response.json();

    // If user is logged in
    if (user?.user_metadata) {
      const provider = user.user_metadata.provider;
      const mediaIds = scheduleData.map(anime => parseInt(anime.id));

      let userData = [];

      // Fetch user data based on provider
      if (provider === 'anilist' && user.user_metadata.anilist_token) {
        const token = user.user_metadata.anilist_token;
        const userId = user.user_metadata.id;
        userData = await fetchAnilistStatuses(token, userId, mediaIds);
      }
      else if (provider === 'mal' && user.user_metadata.mal_token) {
        const token = user.user_metadata.mal_token;
        userData = await fetchMALStatuses(token, mediaIds);
      }

      // Enrich schedule data with user statuses if we have any
      if (userData.length > 0) {
        scheduleData = scheduleData.map(anime => {
          const userEntry = userData.find(entry => entry.mediaId === parseInt(anime.id));

          if (!userEntry) {
            return anime;
          }

          return {
            ...anime,
            status: userEntry.status?.toLowerCase() || 'planning',
            score: userEntry.score || 0,
            progress: userEntry.progress || 0,
            startDate: userEntry.startedAt ?
              new Date(
                userEntry.startedAt.year,
                (userEntry.startedAt.month || 1) - 1,
                userEntry.startedAt.day || 1
              ) :
              null,
            finishDate: userEntry.completedAt ?
              new Date(
                userEntry.completedAt.year,
                (userEntry.completedAt.month || 1) - 1,
                userEntry.completedAt.day || 1
              ) :
              null
          };
        });
      }
    }

    return {
      scheduleData
    };

  } catch (error) {
    console.error('Error in schedule page load:', error);
    return {
      scheduleData: [],
      error: error.message
    };
  }
}