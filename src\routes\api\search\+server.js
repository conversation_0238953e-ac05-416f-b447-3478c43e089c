// src/routes/search/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// src/routes/search/+server.js
export async function GET({ url, locals }) {
  try {
    const search = url.searchParams.get('search') || '';
    const genres = url.searchParams.get('genres')?.split(',').filter(Boolean) || [];
    const status = url.searchParams.get('status') || null;
    const format = url.searchParams.get('format') || null;
    const year = url.searchParams.get('year') ? parseInt(url.searchParams.get('year')) : null;
    const season = url.searchParams.get('season') || null;
    const source = url.searchParams.get('source') || null;
    const page = parseInt(url.searchParams.get('page')) || 1;
    const pageSize = parseInt(url.searchParams.get('pageSize')) || 12;
    const sortField = url.searchParams.get('sortField') || 'popularity';
    const sortDirection = url.searchParams.get('sortDirection') || 'desc';
    const preferRomaji = url.searchParams.get('preferRomaji') === 'true';

    // Convert single values to arrays for the RPC call
    const statusArray = status ? [status] : [];
    const formatArray = format ? [format] : [];
    const yearArray = year ? [year] : [];
    const seasonArray = season ? [season] : [];
    const sourceArray = source ? [source] : [];

    const { data, error } = await supabase.rpc('search_anime', {
      search_query: search,
      input_genres: genres,
      input_status: statusArray,
      input_format: formatArray,
      input_year: yearArray,
      input_season: seasonArray,
      input_source: sourceArray,
      page_number: page,
      page_size: pageSize,
      sort_field: sortField,
      sort_direction: sortDirection,
      prefer_romaji: preferRomaji // Pass this new parameter
    });

    if (error) throw error;

    const [results] = data;

    return json({
      data: results.results,
      total: parseInt(results.total_count),
      filterCounts: results.filter_counts,
      page,
      pageSize,
      hasMore: (page * pageSize) < parseInt(results.total_count),
      start: (page - 1) * pageSize,
      end: ((page - 1) * pageSize) + pageSize - 1
    });

  } catch (error) {
    console.error('API Error:', error);
    return json({ error: error.message }, { status: 500 });
  }
}