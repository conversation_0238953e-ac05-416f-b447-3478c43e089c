//src/routes/api/og/+server.js
import { ImageResponse } from '@ethercorps/sveltekit-og';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

const CACHE_CONTROL = 'public, max-age=604800, stale-while-revalidate=86400';

function generateCacheKey(params) {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');

  return Buffer.from(sortedParams).toString('base64').replace(/[/+=]/g, '');
}

function getTitleStyle(title) {
  if (title.length > 100) return 'text-3xl';
  if (title.length > 70) return 'text-4xl';
  if (title.length > 50) return 'text-5xl';
  return 'text-6xl';
}

function getEpisodeText(episodes) {
  // Convert to number and handle non-numeric cases
  const num = parseInt(episodes);
  if (isNaN(num) || episodes === '∞' || episodes === '??') return 'odcinków';

  // Get last digit and last two digits
  const lastDigit = num % 10;
  const lastTwoDigits = num % 100;

  // Rules for Polish declination
  if (lastTwoDigits >= 11 && lastTwoDigits <= 14) return 'odcinków';
  if (lastDigit === 1) return 'odcinek';
  if (lastDigit >= 2 && lastDigit <= 4) return 'odcinki';
  return 'odcinków';
}

function translateGenre(genre) {
  const genreTranslations = {
    Action: 'Akcja',
    Adventure: 'Przygodowe',
    Comedy: 'Komedia',
    Drama: 'Dramat',
    Fantasy: 'Fantasy',
    Horror: 'Horror',
    Mystery: 'Mystery',
    Romance: 'Romans',
    'Sci-Fi': 'Sci-Fi',
    'Slice of Life': 'Slice of Life',
    Sports: 'Sport',
    Supernatural: 'Nadprzyrodzone',
    Thriller: 'Thriller',
    Mecha: 'Mecha',
    Psychological: 'Psychologiczne',
    Music: 'Muzyczne',
    School: 'Szkolne',
    Seinen: 'Seinen',
    Shounen: 'Shounen',
    Shoujo: 'Shoujo',
    'Martial Arts': 'Sztuki Walki',
    Historical: 'Historyczne',
    Military: 'Militarne',
    Demons: 'Demony',
    Magic: 'Magia',
    Harem: 'Harem',
    Ecchi: 'Ecchi',
    Isekai: 'Isekai',
    Game: 'Gry',
    Parody: 'Parodia',
    Police: 'Policyjne',
    Space: 'Kosmos',
    Vampire: 'Wampiry'
  };

  return genreTranslations[genre] || genre;
}

// Function to get season translation
function translateSeason(season) {
  const seasonTranslations = {
    WINTER: 'Zima',
    SPRING: 'Wiosna',
    SUMMER: 'Lato',
    FALL: 'Jesień'
  };
  return seasonTranslations[season] || season;
}

export const GET = async ({ url, setHeaders }) => {
  try {
    // Check for required parameters
    if (!url.searchParams.has('id')) {
      throw error(400, 'Missing required parameter: id (anilist_id)');
    }

    const anilistId = url.searchParams.get('id');

    // Fetch anime data from Supabase
    const { data: metadataResponse, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('*')
      .eq('anilist_id', anilistId)
      .single();

    if (metadataError || !metadataResponse) {
      throw error(404, `Anime with ID ${anilistId} not found`);
    }

    // Fetch ranking data
    const { data: rankingResponse, error: rankingError } = await supabase
      .from('anime_rankings')
      .select('*')
      .eq('anilist_id', anilistId)
      .single();

    if (rankingError) {
      console.error('Error fetching ranking data:', rankingError);
    }

    // Prepare parameters for OG image
    const params = {
      title: metadataResponse.english_title || metadataResponse.romaji_title,
      rating: rankingResponse ? (rankingResponse.average_score / 10).toFixed(2) : '??',
      posterImage: metadataResponse.cover_image,
      bannerImage: metadataResponse.banner_image,
      studio: metadataResponse.studios?.[0] || 'Nieznane Studio',
      releaseDate: `${translateSeason(metadataResponse.season)} ${metadataResponse.season_year}`,
      episodes: metadataResponse.episodes?.toString() || '??',
      genres: metadataResponse.genres || []
    };

    // Translate genres
    const translatedGenres = params.genres.map(genre => translateGenre(genre.trim()));

    const titleStyle = getTitleStyle(params.title);

    // Generate cache key and set headers
    const cacheKey = generateCacheKey(params);
    setHeaders({
      'Cache-Control': CACHE_CONTROL,
      'ETag': `"${cacheKey}"`,
      'Content-Type': 'image/png'
    });

    // Load font
    const fontFile = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
    if (!fontFile.ok) {
      throw error(500, 'Failed to load font');
    }
    const fontData = await fontFile.arrayBuffer();

    const template = `
    <div tw="flex w-full h-full bg-[#1a1b26] relative">
      <!-- Background blur image -->
      <img
        src="${params.bannerImage}"
        tw="absolute w-full h-full"
        style="
          object-fit: cover;
          filter: blur(20px) brightness(0.3);
        "
      />

      <!-- Content section -->
      <div tw="flex flex-col w-3/4 px-24 py-16 justify-between relative">
        <span tw="text-[#ee8585] text-2xl mb-2">${params.releaseDate} • ${params.episodes} ${getEpisodeText(params.episodes)}</span>

        <div tw="flex flex-col mt-4">
          <h1 tw="text-white ${titleStyle} font-bold leading-tight">
            ${params.title}
          </h1>
          <h3 tw="text-[#ee8585] text-3xl mt-2">${params.studio}</h3>
        </div>

        <div tw="flex flex-col mt-auto">
          <div tw="flex items-center">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor" tw="text-[#ee8585] mr-3 mb-4">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
            </svg>
            <span tw="text-white text-7xl">${params.rating}</span>
          </div>

          <div tw="flex flex-wrap mt-2">
            ${translatedGenres.map(genre => `
              <span tw="text-gray-300 text-2xl px-6 py-1 rounded-full bg-[#24283b] mr-3 mb-3">${genre}</span>
            `).join('')}
          </div>
        </div>
      </div>

      <!-- Feature image -->
      <img
        src="${params.posterImage}"
        tw="absolute right-0 w-1/3 h-full"
        style="
          object-fit: cover;
          clip-path: polygon(20% 0, 100% 0, 100% 100%, 0% 100%);
        "
      />
    </div>
    `;

    const imageResponse = await new ImageResponse(template, {
      width: 1200,
      height: 630,
      fonts: [
        {
          name: 'Gilroy-SemiBold',
          data: fontData,
          weight: 600,
          style: 'normal'
        }
      ],
      headers: {
        'Cache-Control': CACHE_CONTROL,
        'ETag': `"${cacheKey}"`
      }
    });

    return imageResponse;

  } catch (e) {
    console.error('Error generating OG image:', e);

    // If it's already an error response from our validation, pass it through
    if (e.status === 400) {
      throw e;
    }

    // Otherwise return a generic error
    throw error(500, 'Failed to generate image');
  }
};