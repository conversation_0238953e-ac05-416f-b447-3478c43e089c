//src/lib/utils/anilist.js
import { ANILIST_CLIENT_ID, ANILIST_CLIENT_SECRET } from '$env/static/private';

export async function refreshAnilistToken(user_id, supabaseAdmin) {
  try {
    // Get current user data
    const { data: userData } = await supabaseAdmin.auth.admin.getUserById(user_id);
    if (!userData) return null;

    // Re-authenticate with Ani<PERSON><PERSON> using stored refresh token
    const refreshToken = userData.user_metadata.anilist_refresh_token;

    if (!refreshToken) return null;

    const response = await fetch('https://anilist.co/api/v2/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        grant_type: 'refresh_token',
        client_id: ANILIST_CLIENT_ID,
        client_secret: ANILIST_CLIENT_SECRET,
        refresh_token: refreshToken
      })
    });

    const tokenData = await response.json();

    if (!response.ok) return null;

    // Update user metadata with new token
    await supabaseAdmin.auth.admin.updateUserById(user_id, {
      user_metadata: {
        ...userData.user_metadata,
        anilist_token: tokenData.access_token,
        anilist_refresh_token: tokenData.refresh_token,
        token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
      }
    });

    return tokenData.access_token;
  } catch (error) {
    console.error('Error refreshing AniList token:', error);
    return null;
  }
}