// src/lib/utils/mal.js
import { MAL_CLIENT_ID, MAL_CLIENT_SECRET } from '$env/static/private';

export async function refreshMALToken(user_id, supabaseAdmin) {
  try {
    // Get current user data
    const { data: userData } = await supabaseAdmin.auth.admin.getUserById(user_id);
    if (!userData) return null;

    // Re-authenticate with MAL using stored refresh token
    const refreshToken = userData.user_metadata.mal_refresh_token;
    //FIXME: "Cannot read properties of undefined (reading 'mal_refresh_token')"
    if (!refreshToken) return null;

    const response = await fetch('https://myanimelist.net/v1/oauth2/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: MAL_CLIENT_ID,
        client_secret: MAL_CLIENT_SECRET,
        refresh_token: refreshToken
      })
    });

    const tokenData = await response.json();

    if (!response.ok) return null;

    // Update user metadata with new token
    await supabaseAdmin.auth.admin.updateUserById(user_id, {
      user_metadata: {
        ...userData.user_metadata,
        mal_token: tokenData.access_token,
        mal_refresh_token: tokenData.refresh_token,
        token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
      }
    });

    return tokenData.access_token;
  } catch (error) {
    console.error('Error refreshing MAL token:', error);
    return null;
  }
}