// src/routes/api/mal/mapping/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabaseAdmin = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request, locals }) {
  try {
    // Get MAL IDs from request
    const { malIds } = await request.json();

    if (!malIds || !Array.isArray(malIds) || malIds.length === 0) {
      return json({
        error: 'Invalid request. Please provide an array of MAL IDs.',
        mappings: []
      }, { status: 400 });
    }

    // Fetch mappings from database
    const { data, error } = await supabaseAdmin
      .from('anime_metadata')
      .select('mal_id, anilist_id')
      .in('mal_id', malIds)
      .not('anilist_id', 'is', null);

    if (error) {
      console.error('Database query error:', error);
      return json({
        error: 'Failed to fetch ID mappings from database',
        mappings: []
      }, { status: 500 });
    }

    // Return the mappings
    return json({
      mappings: data.map(item => ({
        mal_id: item.mal_id,
        anilist_id: item.anilist_id
      }))
    });

  } catch (error) {
    console.error('Error processing MAL to AniList mapping request:', error);
    return json({
      error: 'Internal server error',
      message: error.message,
      mappings: []
    }, { status: 500 });
  }
}