// src/routes/api/mal/proxy/anime-details/+server.js
import { json } from '@sveltejs/kit';

export async function POST({ request }) {
  const { animeId, token } = await request.json();

  if (!animeId || !token) {
    return json({ error: 'Missing required parameters' }, { status: 400 });
  }

  try {
    // Make the request to MAL API through the server
    const response = await fetch(`https://api.myanimelist.net/v2/anime/${animeId}?fields=my_list_status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`MAL API returned ${response.status}`);
    }

    const data = await response.json();
    return json(data);
  } catch (error) {
    console.error('Error fetching anime details from MAL:', error);
    return json({ error: error.message }, { status: 500 });
  }
}