/**
 * Admin user IDs for authorization checks
 * DEPRECATED: This is kept for backward compatibility.
 * New code should use the role-based authorization from roleUtils.js
 */
export const ADMIN_USERS = [
  'dcb189fb-005d-409c-a7cc-9ece546f98fe',
  '6e7a7fed-2c0c-4fb5-9495-7830fe3ad0fb'
];

/**
 * Check if a user ID is an admin
 * DEPRECATED: This is kept for backward compatibility.
 * New code should use the isAdmin function from roleUtils.js
 *
 * @param {string} userId - The user ID to check
 * @returns {boolean} - True if the user is an admin, false otherwise
 */
export function isAdmin(userId) {
  return ADMIN_USERS.includes(userId);
}
