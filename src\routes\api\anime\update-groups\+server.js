import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminByRole } from '$lib/utils/roleUtils';

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request, locals }) {
  // Check if user is authenticated
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is an admin
  const hasAdminRole = await isAdminByRole(user.id);

  if (!hasAdminRole) {
    return json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    const { episodeId, otherGroups } = await request.json();

    if (!episodeId) {
      return json({ error: 'Missing episode ID' }, { status: 400 });
    }

    // The other_groups field doesn't exist in anime_new table
    // This functionality has been replaced by the new group management system
    return json({ error: 'This API endpoint is deprecated. Use /api/anime/update-episode-sources instead.' }, { status: 410 });

    if (error) {
      console.error('Error updating groups:', error);
      return json({ error: 'Failed to update groups' }, { status: 500 });
    }

    return json({ success: true });
  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
