// src/routes/api/user/settings/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// GET endpoint to fetch user settings
export async function GET({ locals }) {
  try {
    const { session } = await locals.safeGetSession();

    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Fetch user profile settings
    const { data, error } = await supabase
      .from('profiles')
      .select('settings')
      .eq('id', session.user.id)
      .single();

    if (error) throw error;

    // Return settings or empty object if not found
    return json(data?.settings || {});
  } catch (error) {
    console.error('Error fetching user settings:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// POST endpoint to update user settings
export async function POST({ request, locals }) {
  try {
    const newSettings = await request.json();
    const { session } = await locals.safeGetSession();

    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }

    // First, get current settings
    const { data: currentProfileData, error: fetchError } = await supabase
      .from('profiles')
      .select('settings')
      .eq('id', session.user.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') throw fetchError;

    // Merge current settings with new settings
    const updatedSettings = {
      ...(currentProfileData?.settings || {}),
      ...newSettings
    };

    // Update the profile with merged settings
    const { data, error } = await supabase
      .from('profiles')
      .update({
        settings: updatedSettings,
        updated_at: new Date().toISOString()
      })
      .eq('id', session.user.id)
      .select();

    if (error) throw error;

    return json({
      success: true,
      settings: updatedSettings
    });
  } catch (error) {
    console.error('Error updating user settings:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}