import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
export async function GET(params) {
	let rommCode = params.url.searchParams.get('roomCode');
	const metadata = await getMetadataInfoFromRoomCode(rommCode);
	return new Response(JSON.stringify(metadata));
}

async function getMetadataInfoFromRoomCode(roomCode) {
	const { data: metadataQuery, error } = await supabase.from('rooms').select('*').eq('room_code', roomCode);
	if (error) {
		console.log(error);
		return null;
	}
	if (metadataQuery.length === 0) {
		return {
			error: `Room ${roomCode} does not exist`
		};
	}
	return metadataQuery[0];
}
