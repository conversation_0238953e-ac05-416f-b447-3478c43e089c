<script>
	//src/routes/embed/+page.svelte
	import posthog from 'posthog-js';
	import html2canvas from 'html2canvas';
	import { onMount, onDestroy } from 'svelte';
	import { Playerjs } from '$lib/components/player.js';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { error } from '@sveltejs/kit';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { Button } from '$lib/components/ui/button';
	import { progressStore } from '$lib/stores/progressStore';
	import { toast } from 'svelte-sonner';
	import { userStore } from '$lib/stores/userLogin';
	import { cacheKeys, getCachedData, setCachedData } from '$lib/utils/cacheUtils';
	import { isAdmin as checkIsAdmin } from '$lib/utils/clientRoleUtils';

	$: isLoggedIn = $userStore?.role === 'authenticated';

	// Initialize isAdmin variable
	let isAdmin = false;

	// Force admin check on component mount
	onMount(() => {
		checkAdminStatus();
	});

	// Function to check admin status
	function checkAdminStatus() {
		console.log('Checking admin status in VideoPlayer');
		console.log('User store:', $userStore);
		console.log('User metadata:', $userStore?.user_metadata);
		console.log('User profile:', $userStore?.user_metadata?.profile);

		// Check if user has admin role in user_metadata.profile
		const hasAdminRole = $userStore?.user_metadata?.profile?.role === 'admin';
		console.log('Has admin role:', hasAdminRole);

		// Update isAdmin
		isAdmin = hasAdminRole;
		console.log('isAdmin set to:', isAdmin);
	}

	// Watch for changes in the user store
	$: if ($userStore) {
		checkAdminStatus();
	}

	export let episodeInfo;
	export let nextEpisodeData;
	export let selectedPlayerType = null;
	export let selectedPlayerUrl = null;
	export let selectedGroup = null;
	let showErrorDialog = false;
	let currentError = null;
	let showTechnicalDetails = false;
	let showIOSWarning = false;
	let isIOS;
	let userSettings;
	const ANILIST_API = 'https://graphql.anilist.co';
	const provider = $userStore?.user_metadata?.provider;

	function checkIfIOS() {
		// iOS detection in web browsers
		return (
			['iPad Simulator', 'iPhone Simulator', 'iPod Simulator', 'iPad', 'iPhone', 'iPod'].includes(navigator.platform) ||
			// iPad on iOS 13 detection
			(navigator.userAgent.includes('Mac') && 'ontouchend' in document) ||
			/iPhone|iPad|iPod/.test(navigator.userAgent)
		);
	}

	let totalDuration;
	const viewedThreshold = 30; // seconds
	let watchedThreshold;
	let isIphone = true;
	let globalTime;
	let markerPeriods;
	let videoLink;
	const PHVideo = 'https://pixeldrain.com/api/file/2y7JfxnZ';
	if (episodeInfo && episodeInfo.markerPeriods) {
		markerPeriods = JSON.parse(episodeInfo.markerPeriods);
	} else {
		markerPeriods = [
			{ startTime: '00:00:00.00', endTime: '00:00:00.00' },
			{ startTime: '00:00:00.00', endTime: '00:00:00.00' }
		];
	}

	if (markerPeriods.length === 1) {
		markerPeriods.push({ startTime: '00:00:00.00', endTime: '00:00:00.00' });
	}

	let userPlayingSeconds = 0;
	let playingTimer;
	let userWatchedEpisode = false;
	let userViewedEpisode = false;
	let checkPlayingTimeTimer;
	let errorCounter = 0;
	let origin = $page.url.origin;
	let episodeID = episodeInfo.id;
	if (!episodeInfo.primarySource) {
		episodeInfo.primarySource = {
			HD: '',
			SD: '',
			FHD: '',
			preview: '',
			SourceMKV: ''
		};
	}
	let FHD = episodeInfo.primarySource.FHD;
	let HD = episodeInfo.primarySource.HD;
	let SD = episodeInfo.primarySource.SD;
	let Source = episodeInfo.primarySource.Source;
	let thumb = episodeInfo.thumbnail;
	let thumbnailFile = episodeInfo.thumbnailFile;
	let animeTitle = episodeInfo.anime_title;

	let episode = episodeInfo.number;
	let title = episodeInfo.title;
	let openingStart = convertToSeconds(markerPeriods[0].startTime);
	let openingEnd = convertToSeconds(markerPeriods[0].endTime);
	let endingStart = convertToSeconds(markerPeriods[1].startTime);
	let endingEnd = convertToSeconds(markerPeriods[1].endTime);
	let player;
	let progressUpdateInterval;
	let relElement;
	let relText;
	let isUIHidden = false;
	let playerTitleBar;
	let playerTimeline = [];
	let playerTimeline2 = [];
	let playerTitleBar2 = [];
	let nextEpisode = [];
	let frameStep = 0.041;
	let playerTimelineChrome;
	let playerTimelineFirefox;
	let playerTitleBar2Chrome;
	let playerTitleBar2Firefox;
	let PLDecoded;

	const errorMessages = {
		SOURCE_ERROR: {
			title: 'Błąd odtwarzania',
			message: 'Nie udało się załadować filmu z żadnego źródła. Spróbuj ponownie za chwilę.'
		},
		NETWORK_ERROR: {
			title: 'Problem z połączeniem',
			message: 'Błąd połączenia z serwerem. Spróbuj ponownie na innej przeglądarce wyłączając rozszerzenia typu adblock.'
		},
		PLAYBACK_INIT_ERROR: {
			title: 'Błąd inicjalizacji',
			message: 'Nie udało się uruchomić odtwarzacza. Odśwież stronę i spróbuj ponownie.'
		},
		// DECODE_ERROR: {
		// 	title: 'Błąd przetwarzania',
		// 	message: 'Wystąpił problem z przetwarzaniem danych filmu. Spróbuj ponownie później.'
		// },
		QUALITY_SOURCE_ERROR: {
			title: 'Błąd zmiany jakości',
			message: 'Nie udało się zmienić jakości filmu. Spróbuj ponownie.'
		},
		INIT_ERROR: {
			title: 'Błąd inicjalizacji',
			message: 'Nie udało się uruchomić interfejsu odtwarzacza. Odśwież stronę.'
		},
		DEFAULT: {
			title: 'Wystąpił błąd',
			message: 'Przepraszamy, coś poszło nie tak. Spróbuj odświeżyć stronę.'
		}
	};

	function decodeVideoLink(encodedUrl) {
		if (!encodedUrl) return null;

		// Check for our signature
		if (!encodedUrl.endsWith('LC')) {
			return encodedUrl;
		}

		// Remove signature
		encodedUrl = encodedUrl.slice(0, -2);

		try {
			// Reverse the scrambling
			const decoded = encodedUrl
				.split('')
				.reverse() // Reverse back
				.map((char) => {
					const code = char.charCodeAt(0);
					return String.fromCharCode(code - 7); // Shift back
				})
				.join('');

			// Decode base64
			return atob(decoded);
		} catch (error) {
			console.error('Error decoding URL:', error);
			return null;
		}
	}

	function handleFetchError(error, context) {
		let errorData = {
			message: 'Failed to fetch data',
			context,
			timestamp: new Date().toISOString(),
			errorType: 'FETCH_ERROR',
			details: {
				context,
				userMessage: 'Could not load the video. Please try refreshing the page or try again later.'
			}
		};

		// If the error is a server response
		if (error.serverError) {
			errorData = {
				...error.serverError,
				context,
				details: {
					...error.serverError.details,
					context
				}
			};
		} else if (error.status) {
			// If it's a network or other error with status
			errorData.details = {
				...errorData.details,
				url: error.url,
				status: error.status,
				statusText: error.statusText,
				originalError: error.message
			};
		} else {
			// For other types of errors
			errorData.details = {
				...errorData.details,
				originalError: error.message || 'Unknown error'
			};
		}

		currentError = errorData;
		showErrorDialog = true;
	}

	function goToDiscord() {
		window.open('https://discord.gg/lycoriscafe', '_blank');
		showErrorDialog = false;
	}

	async function fetchVideoLink(link, shouldUseBurst = false) {
		try {
			if (shouldUseBurst) {
				link = episodeInfo.burstSource;
			}
			const endpoint = shouldUseBurst ? 'getBurstLink' : 'getLink';
			const fetchLink = await fetch(`${origin}/api/watch/${endpoint}?link=${encodeURIComponent(link)}`);

			const contentType = fetchLink.headers.get('content-type');
			if (contentType && contentType.includes('application/json')) {
				const responseData = await fetchLink.json();

				if (!fetchLink.ok) {
					throw responseData;
				}
				return responseData.downloadUrl;
			} else {
				const textResponse = await fetchLink.text();
				if (!fetchLink.ok) {
					throw new Error(`Video link fetch failed: ${fetchLink.statusText}`);
				}
				if (!textResponse) {
					throw new Error('Received empty video link');
				}
				return decodeVideoLink(textResponse);
			}
		} catch (err) {
			console.error('[fetchVideoLink] Error:', err);

			if (err.errorType) {
				throw err;
			} else {
				const error = {
					message: 'Failed to fetch video',
					timestamp: new Date().toISOString(),
					errorType: 'NETWORK_ERROR',
					details: {
						userMessage: 'Could not connect to the server. Please check your internet connection and try again.',
						originalError: err.message,
						context: 'fetchVideoLink'
					}
				};
				throw error;
			}
		}
	}

	// Function to embed external player
	function embedExternalPlayer() {
		// First check if we have a direct external player link from the new data structure
		if (episodeInfo.external_player_link && episodeInfo.player_source) {
			console.log(`Using external player from episode data: ${episodeInfo.player_source} with URL: ${episodeInfo.external_player_link}`);

			// Create an iframe element to embed the player
			const playerContainer = document.getElementById('player');
			if (playerContainer) {
				// Clear the container
				playerContainer.innerHTML = '';

				// Create and append the iframe
				const iframe = document.createElement('iframe');
				iframe.src = episodeInfo.external_player_link;
				iframe.width = '100%';
				iframe.height = '100%';
				iframe.frameBorder = '0';
				iframe.allowFullscreen = true;

				playerContainer.appendChild(iframe);

				// Set up global functions for embedded players
				window.OpenWatchParty = function () {
					let WPLink = $page.url.href.replace('embed', 'watchParty');
					window.open(WPLink, '_blank');
				};

				window.copyEmbedURL = function () {
					let embedURL = `https://www.lycoris.cafe/embed?id=${episodeInfo.anilist_id}&episode=${Number(episodeInfo.number)}`;
					navigator.clipboard.writeText(embedURL);
				};

				return true;
			}
		}
		// Fall back to the old method if we have selectedPlayerType and selectedPlayerUrl
		// But skip lycoris.cafe as it should use the custom player, not iframe embedding
		else if (selectedPlayerType && selectedPlayerUrl && selectedPlayerType !== 'lycoris') {
			console.log(`Using selected player: ${selectedPlayerType} with URL: ${selectedPlayerUrl}`);

			// Create an iframe element to embed the player
			const playerContainer = document.getElementById('player');
			if (playerContainer) {
				// Clear the container
				playerContainer.innerHTML = '';

				// Create and append the iframe
				const iframe = document.createElement('iframe');
				iframe.src = selectedPlayerUrl;
				iframe.width = '100%';
				iframe.height = '100%';
				iframe.frameBorder = '0';
				iframe.allowFullscreen = true;

				playerContainer.appendChild(iframe);

				// Set up global functions for embedded players
				window.OpenWatchParty = function () {
					let WPLink = $page.url.href.replace('embed', 'watchParty');
					window.open(WPLink, '_blank');
				};

				window.copyEmbedURL = function () {
					let embedURL = `https://www.lycoris.cafe/embed?id=${episodeInfo.anilist_id}&episode=${Number(episodeInfo.number)}`;
					navigator.clipboard.writeText(embedURL);
				};

				return true;
			}
		}
		return false;
	}

	// React to changes in selectedPlayerUrl and selectedPlayerType
	$: if (selectedPlayerUrl && selectedPlayerType && player && selectedPlayerType !== 'lycoris' && selectedPlayerType !== 'lycoris.cafe') {
		console.log('Reactive statement triggered - switching to external player:', selectedPlayerType);
		// If we already have a player initialized, we need to clean it up
		try {
			// Only destroy player if NOT Firefox
			const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');
			if (!isFirefox) {
				player.api('destroy');
			}
		} catch (e) {
			console.error('Error destroying player:', e);
		}

		// Embed the external player (but not for lycoris.cafe)
		embedExternalPlayer();
	}

	onMount(async () => {
		// Debug: Output episode data and currently selected player
		console.log('=== EPISODE DATA DEBUG ===');
		console.log('Episode Info:', episodeInfo);
		console.log('Selected Group:', selectedGroup);
		console.log('Selected Player Type:', selectedPlayerType);
		console.log('Selected Player URL:', selectedPlayerUrl);
		console.log('Episode external_player_link:', episodeInfo.external_player_link);
		console.log('Episode player_source:', episodeInfo.player_source);
		console.log('Episode translating_group:', episodeInfo.translating_group);
		console.log('Episode secondarySource:', episodeInfo.secondarySource);
		console.log('Has secondary source:', episodeInfo.secondarySource && Object.keys(episodeInfo.secondarySource).length > 0);
		console.log('=== END EPISODE DATA DEBUG ===');

		window.addEventListener('beforeunload', (event) => {
			// event.preventDefault();
			localStorage.setItem(`lycorisCafe_resumeTime_${episodeInfo.anilist_id}_${episode}`, globalTime);
		});

		// Check if we should use lycoris.cafe custom player
		if (selectedGroup === 'lycoris.cafe' || selectedPlayerType === 'lycoris' || selectedPlayerType === 'lycoris.cafe') {
			console.log('Using lycoris.cafe custom player - skipping external player logic');
			// Skip external player logic and proceed to custom player initialization
		}
		// Check for external player link from the new data structure first
		else if (episodeInfo.external_player_link && episodeInfo.player_source) {
			console.log('Using external player link from episode data');
			if (embedExternalPlayer()) {
				// Return early as we've handled the player embedding
				return;
			}
		}
		// Fall back to the old method if selectedPlayerType and selectedPlayerUrl are provided
		// But skip lycoris.cafe as it should use the custom player
		else if (selectedPlayerType && selectedPlayerUrl && selectedPlayerType !== 'lycoris' && selectedPlayerType !== 'lycoris.cafe') {
			console.log('Using selected player type and URL');
			if (embedExternalPlayer()) {
				// Return early as we've handled the player embedding
				return;
			}
		}

		// Only initialize the custom player if we're not using an external player
		const savedProgress = progressStore.getProgress(episodeInfo.anilist_id, episodeInfo.number);
		if (savedProgress?.currentTime) {
			setTimeout(() => {
				player.api('seek', savedProgress.currentTime);
			}, 1000); // Small delay to ensure player is ready
		}

		if (nextEpisodeData) {
			nextEpisode = [
				{
					title: nextEpisodeData.title,
					poster: nextEpisodeData.poster,
					link: nextEpisodeData.link
				}
			];
		}

		window.OpenWatchParty = function () {
			let WPLink = $page.url.href.replace('embed', 'watchParty');
			window.open(WPLink, '_blank');
			player.api('pause');
			umami.track('openWatchParty', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode
			});
		};

		window.copyEmbedURL = function () {
			let embedURL = `https://www.lycoris.cafe/embed?id=${episodeInfo.anilist_id}&episode=${Number(episodeInfo.number)}`;
			navigator.clipboard.writeText(embedURL);
			umami.track('copyEmbedUrl', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode
			});
		};

		window.PlayerjsMenu1 = function (id, title, item) {
			if (item === 0) {
				player.api('menu1', 'none');
				umami.track('openDiscordLinkInPlayer', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				window.open('https://discord.gg/lycoriscafe', '_blank');
			}

			if (item === 1) {
				player.api('menu1', 'none');
				umami.track('openDiscordLinkInPlayer', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				window.open('https://discord.gg/lycoriscafe', '_blank');
			}
		};

		window.PlayerjsMenu2 = function (id, title, item) {
			if (item === 0) {
				player.api('design', 1);
				umami.track('changePlayerSkin', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode,
					skin: 'pixel'
				});
			}

			if (item === 1) {
				player.api('design', 2);
				umami.track('changePlayerSkin', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode,
					skin: 'modern'
				});
			}
		};

		window.PlayerjsMenu3 = function (id, title, item) {
			umami.track('openKeybindsInfo', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode
			});
			window.open(`https://rentry.co/o38otzio`, '_blank').focus();
		};

		let fileString = `[480p]${PHVideo},[720p]${PHVideo},[1080p]${PHVideo}`;
		player = new Playerjs({
			id: 'player',
			file: fileString,
			title: `[lycoris.cafe] ${episode}. ${title.replaceAll('_', ' ')}`,
			poster: thumb,
			default_quality: '1080p',
			thumbnails: thumbnailFile,
			points: [
				{
					time: openingStart,
					width: `${openingEnd - openingStart}s`,
					opacity: 0.8,
					color: '#ff4747',
					text: 'Opening',
					textstyle: 'font-size:150%;color: #000000'
				},
				{
					time: endingStart,
					width: `${endingEnd - endingStart}s`,
					opacity: 0.8,
					color: '#ff4747',
					text: 'Ending',
					textstyle: 'font-size:150%;color: #000000'
				}
			],
			skip: `${openingStart}-${openingEnd},${endingStart}-${endingEnd}`,
			menu1: 'none',
			rel: nextEpisode
		});

		document.getElementById('player').addEventListener('play', onPlay);
		document.getElementById('player').addEventListener('pause', onPause);
		document.getElementById('player').addEventListener('stop', onStop);
		document.getElementById('player').addEventListener('init', onInit);
		document.getElementById('player').addEventListener('time', onTimeChange);
		document.getElementById('player').addEventListener('start', onStart);
		document.getElementById('player').addEventListener('finish', onFinish);
		document.getElementById('player').addEventListener('fullscreen', onFullscreen);
		document.getElementById('player').addEventListener('exitfullscreen', onFullscreen);
		document.getElementById('player').addEventListener('quality', onQualityChange);
		// document.getElementById('player').addEventListener('error', onError);
		// document.getElementById('player').addEventListener('loaderror', onError);

		return () => {
			// Clear intervals
			if (playingTimer) clearInterval(playingTimer);
			if (checkPlayingTimeTimer) clearInterval(checkPlayingTimeTimer);

			// Remove event listeners
			const playerElement = document.getElementById('player');
			if (playerElement) {
				playerElement.removeEventListener('userplay', onPlay);
				playerElement.removeEventListener('pause', onPause);
				playerElement.removeEventListener('stop', onStop);
				playerElement.removeEventListener('init', onInit);
				playerElement.removeEventListener('time', onTimeChange);
				playerElement.removeEventListener('start', onStart);
				playerElement.removeEventListener('finish', onFinish);
				playerElement.removeEventListener('fullscreen', onFullscreen);
				playerElement.removeEventListener('exitfullscreen', onFullscreen);
				playerElement.removeEventListener('quality', onQualityChange);
			}

			// Save current time before destroying
			if (player && globalTime) {
				localStorage.setItem(`lycorisCafe_resumeTime_${title}_${episode}`, globalTime);
			}

			// Destroy the player instance
			// if (player) {
			// 	// Only destroy player if NOT Firefox
			// 	const isFirefox = navigator.userAgent.toLowerCase().includes('firefox') || navigator.userAgent.toLowerCase().includes('iphone');
			// 	if (!isFirefox) {
			// 		player.api('destroy');
			// 	}
			// }

			// Clean up global window functions
			delete window.OpenWatchParty;
			delete window.PlayerjsMenu1;
			delete window.PlayerjsMenu2;
			delete window.PlayerjsMenu3;
		};
	});

	onDestroy(() => {
		if (progressUpdateInterval) {
			clearInterval(progressUpdateInterval);
		}

		if (player) {
			// Only destroy player if NOT Firefox
			const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');
			if (!isFirefox) {
				player.api('destroy');
			}
		}
	});

	async function onStart() {
		try {
			const quality = player.api('quality');
			const currentTime = player.api('time');
			const savedTime = localStorage.getItem(`lycorisCafe_resumeTime_${title}_${episode}`);

			await new Promise((resolve) => setTimeout(resolve, 3000));

			// Try burst link first (only if episode is newer than 24 hours)
			videoLink = await tryFetchBurstLink();

			// If burst fails or isn't available, try secondary
			if (!videoLink || videoLink === '') {
				videoLink = await tryFetchSecondarySource();
				// If secondary fails, try primary
				if (!videoLink || JSON.stringify(videoLink) === '{}') {
					videoLink = await tryFetchPrimarySource();
					if (!videoLink) {
						throw {
							message: 'All video sources failed',
							timestamp: new Date().toISOString(),
							errorType: 'SOURCE_ERROR',
							details: {
								userMessage: 'Unable to load video from any source. Please try again later.',
								context: 'onStart'
							}
						};
					}
				}
			}

			let fileString = createFileString(videoLink);

			// Add seek time
			if (currentTime > 0) {
				fileString += `[seek:${currentTime}]`;
			} else if (savedTime) {
				fileString += `[seek:${savedTime}]`;
			}

			player.api('play', fileString);
		} catch (err) {
			handleError(err);
		}
	}

	async function verifyLinkAccessible(url) {
		try {
			const response = await fetch(url, { method: 'HEAD' });
			return response.ok;
		} catch (error) {
			return false;
		}
	}

	function getErrorMessage(errorType) {
		return errorMessages[errorType] || errorMessages.DEFAULT;
	}

	// Add this to the handleError function in embed/+page.svelte

	function handleError(err) {
		const { title, message } = getErrorMessage(err.errorType || 'DEFAULT');
		currentError = {
			message: message,
			timestamp: new Date().toISOString(),
			errorType: err.errorType || 'DEFAULT',
			details: {
				userMessage: err.details?.userMessage || message,
				originalError: err.message,
				context: err.details?.context || 'general'
			}
		};

		// Clear any existing progress update intervals
		if (progressUpdateInterval) {
			clearInterval(progressUpdateInterval);
			progressUpdateInterval = null;
		}

		if (checkPlayingTimeTimer) {
			clearInterval(checkPlayingTimeTimer);
			checkPlayingTimeTimer = null;
		}

		showErrorDialog = true;

		// Add a flag to the current episode progress to indicate an error occurred
		if (provider && episodeInfo) {
			const animeId = provider === 'anilist' ? episodeInfo.anilist_id : episodeInfo.mal_id;
			const episodeNumber = episodeInfo.number;

			// We don't actually update the progress here, just marking that we had an error
			// This prevents future progress updates until page reload
			progressStore.getProgress(animeId, episodeNumber);
		}
	}

	function isEpisodeNewerthan24Hours() {
		const airDate = new Date(episodeInfo.airDate);
		const now = new Date();
		const timeDiff = now - airDate;
		const hoursAfterAiring = timeDiff / (1000 * 60 * 60);
		return hoursAfterAiring < 24;
	}

	async function tryFetchBurstLink() {
		try {
			// Return false if episode is older than 24 hours or no burst source exists
			if (!isEpisodeNewerthan24Hours() || !episodeInfo.burstSource) {
				return false;
			}

			if (!episodeInfo.burstSource) {
				return false;
			}

			const response = await fetch(`${origin}/api/watch/getBurstLink?link=${encodeURIComponent(episodeInfo.burstSource)}`);

			if (!response.ok) {
				throw new Error(`Burst source fetch failed: ${response.statusText}`);
			}

			let data = await response.text();
			if (!data || data.downloadUrl === '') {
				throw new Error('No burst source link received');
			}

			let decodedLinks = decodeVideoLink(data);
			decodedLinks = JSON.parse(decodedLinks);

			// Verify FHD link if it exists
			if (decodedLinks.downloadUrl) {
				const isAccessible = await verifyLinkAccessible(decodedLinks.downloadUrl);
				if (!isAccessible) {
					throw new Error('Burst source link is not accessible');
				}
			} else {
				throw new Error('Burst source link is not accessible');
			}
			umami.track('burstLinkFetched', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode
			});
			return decodedLinks.downloadUrl;
		} catch (error) {
			return false;
		}
	}

	async function tryFetchPrimarySource(quality) {
		if (!quality) {
			quality = '1080p';
		}
		try {
			if (!episodeInfo.primarySource.FHD) {
				throw new Error('No primary source URL available');
			}

			switch (quality) {
				case '1080p':
					quality = 'FHD';
					break;
				case '720p':
					quality = 'HD';
					break;
				case '480p':
					quality = 'SD';
					break;
				default:
					break;
			}

			const videoLink = await fetchVideoLink(quality ? episodeInfo.primarySource[quality] : episodeInfo.primarySource.FHD);
			if (!videoLink) {
				throw new Error('Failed to fetch video link');
			}

			const isAccessible = await verifyLinkAccessible(videoLink);
			if (!isAccessible) {
				throw new Error('Primary source link is not accessible');
			}

			umami.track('primaryLinkFetched', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode
			});
			return videoLink;
		} catch (error) {
			return false;
		}
	}

	async function tryFetchSecondarySource(quality) {
		if (!quality) {
			quality = '1080p';
		}
		try {
			const response = await fetch(`${origin}/api/watch/getSecondaryLink?id=${episodeID}`);

			if (!response.ok) {
				throw new Error(`Secondary source fetch failed: ${response.statusText}`);
			}

			let data = await response.text();
			if (!data || data.downloadUrl === '') {
				throw new Error('No secondary source link received');
			}

			let decodedLinks = decodeVideoLink(data);
			decodedLinks = JSON.parse(decodedLinks);

			// Verify FHD link if it exists
			if (decodedLinks.FHD) {
				const isAccessible = await verifyLinkAccessible(decodedLinks.FHD);
				if (!isAccessible) {
					throw new Error('Secondary source link is not accessible');
				}
			}
			umami.track('secondaryLinkFetched', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode
			});
			return decodedLinks;
		} catch (error) {
			return false;
		}
	}

	function createFileString(videoLink) {
		if (videoLink.FHD) {
			return `[480p]${videoLink.SD},[720p]${videoLink.HD},[1080p]${videoLink.FHD}`;
		}
		return `[480p]${videoLink},[720p]${videoLink},[1080p]${videoLink}`;
	}

	function onTimeChange(event) {
		globalTime = event.info.toString();
	}

	async function onQualityChange(event) {
		umami.track('qualityChanged', {
			is_embed: false,
			anime_title: animeTitle,
			anime_episode: episode,
			quality: evnet.info
		});
		try {
			const quality = event.info;

			// Try burst link first (only if episode is newer than 24 hours)
			videoLink = await tryFetchBurstLink();

			// If burst fails or isn't available, try secondary
			if (!videoLink || videoLink === '') {
				videoLink = await tryFetchSecondarySource(quality);
				// If secondary fails, try primary
				if (!videoLink || JSON.stringify(videoLink) === '{}') {
					videoLink = await tryFetchPrimarySource(quality);
					if (!videoLink) {
						throw {
							message: 'Quality change failed - no valid source',
							timestamp: new Date().toISOString(),
							errorType: 'QUALITY_SOURCE_ERROR',
							details: {
								userMessage: 'Failed to change quality. Please try again.',
								quality
							}
						};
					}
				}
			}

			const fileString = createFileString(videoLink) + `[seek:${globalTime}]`;
			player.api('play', fileString);
		} catch (err) {
			handleError(err);
		}
	}

	async function onInit() {
		umami.track('playerInit', {
			is_embed: false,
			anime_title: animeTitle,
			anime_episode: episode
		});
		try {
			// Initialize UI elements with error handling
			try {
				playerTitleBar = findPjsdivByStyles('font-size: 14px; line-height: 1em; pointer-events: none; display: block; visibility: visible;');
				playerTitleBar2Chrome = findPjsdivByStyles('position: absolute; pointer-events: none; opacity: 1; transition: opacity 0.1s linear 0s; color: rgb(255, 255, 255); font-size: 25px; font-family: vt323; letter-spacing: 0px; padding: 0px 3px; white-space: nowrap; transform: scale(1);');
				playerTitleBar2Firefox = findPjsdivByStyles('position: absolute; pointer-events: none; opacity: 1; transition: opacity 0.1s linear; color: rgb(255, 255, 255); font-size: 25px; font-family: vt323; letter-spacing: 0px; padding: 0px 3px; white-space: nowrap; transform: scale(1);');
				playerTimelineFirefox = findPjsdivByStyles('position: absolute; left: 0px; top: 0px; width: 100%; height: 100%; transition: top 0.3s ease-out; pointer-events: none;');
				playerTimelineChrome = findPjsdivByStyles('position: absolute; left: 0px; top: 0px; width: 100%; height: 100%; transition: top 0.3s ease-out 0s; pointer-events: none;');
				playerTimeline2 = findPjsdivByStyles('height: 98px; display: block; margin-left: 0px; margin-right: 0px; border-radius: 0px;');
			} catch (uiError) {
				console.error('Failed to initialize UI elements:', uiError);
			}
		} catch (error) {
			const errorDetails = {
				message: error.description || 'UI initialization failed',
				context: 'player initialization',
				timestamp: new Date().toISOString(),
				errorType: 'INIT_ERROR',
				details: {
					userMessage: 'Failed to initialize the video player interface. Please refresh the page.',
					originalError: error.toString(),
					stack: error.stack
				}
			};

			currentError = errorDetails;
			showErrorDialog = true;
			console.error('Initialization error:', errorDetails);
		}
	}

	function replaceHasloText() {
		// Get all pjsdiv elements
		const pjsdivs = document.getElementsByTagName('pjsdiv');

		// Convert to array and iterate through each pjsdiv
		Array.from(pjsdivs).forEach((div) => {
			// Get all text nodes within this pjsdiv
			const walker = document.createTreeWalker(div, NodeFilter.SHOW_TEXT, null, false);

			// Check each text node
			let node;
			while ((node = walker.nextNode())) {
				if (node.textContent.includes('Hasło')) {
					node.textContent = node.textContent.replace('Hasło', 'CAPTCHA: Wciśnij Enter W Białym Polu');
				}
			}
		});
	}

	async function refreshAnilistToken() {
		try {
			const response = await fetch('/api/anilist/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			// Update user store with new token
			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					anilist_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing token:', error);
			toast.error('Nie udało się odświeżyć tokena');
			return null;
		}
	}

	async function onPlay() {
		playingTimer = setInterval(() => {
			userPlayingSeconds += 1;
		}, 1000);

		progressUpdateInterval = setInterval(() => {
			if (player) {
				const currentTime = player.api('time');
				const duration = player.api('duration');

				// Only update progress if both values are valid and no error has occurred
				if (currentTime && duration && !showErrorDialog) {
					progressStore.updateProgress(
						provider === 'anilist' ? episodeInfo.anilist_id : episodeInfo.mal_id,
						episodeInfo.number,
						currentTime,
						duration,
						episodeInfo.thumbnail,
						false // No error
					);
				}
			}
		}, 5000); // Update every 5 seconds

		async function makeRequest(url) {
			try {
				const response = await fetch(url);
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}
			} catch (error) {
				console.error('Error making request:', error);
			}
		}

		async function updateAniListProgress() {
			// console.log('updating anilist');
			try {
				const MUTATION = `
      mutation ($mediaId: Int, $progress: Int) {
        SaveMediaListEntry (mediaId: $mediaId, progress: $progress) {
          id 
          progress
        }
      }`;

				let token = $userStore.user_metadata?.anilist_token;
				const tokenExpiry = $userStore.user_metadata?.token_expiry;

				// Check token expiry and refresh if needed
				if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
					token = await refreshAnilistToken();
					if (!token) {
						umami.track('alUpdateError', {
							is_embed: false,
							anime_title: animeTitle,
							anime_episode: episode,
							message: 'Could not refresh token'
						});
						throw new Error('Could not refresh token');
					}
				}

				if (!token) {
					throw new Error('No AniList authentication available');
				}

				const response = await fetch(ANILIST_API, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Accept: 'application/json',
						Authorization: `Bearer ${token}`
					},
					body: JSON.stringify({
						query: MUTATION,
						variables: {
							mediaId: parseInt(episodeInfo.anilist_id),
							progress: parseInt(episodeInfo.number)
						}
					})
				});

				if (!response.ok) {
					const errorData = await response.json();
					umami.track('alUpdateError', {
						is_embed: false,
						anime_title: animeTitle,
						anime_episode: episode,
						message: errorData.errors?.[0]?.message
					});
					throw new Error(errorData.errors?.[0]?.message || 'Failed to update AniList progress');
				}

				const data = await response.json();

				if (data.errors) {
					umami.track('alUpdateError', {
						is_embed: false,
						anime_title: animeTitle,
						anime_episode: episode,
						message: data.errors[0]?.message
					});
					throw new Error(data.errors[0]?.message || 'AniList API returned errors');
				}
				// console.log('updated anilist');
				umami.track('alUpdate', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				return data.data.SaveMediaListEntry;
			} catch (error) {
				console.error('Error updating AniList progress:', error);
				toast.error('Nie udało się zaktualizować postępu na AniList');
				return null;
			}
		}

		async function fetchUserSettings() {
			if (!$userStore || $userStore.role !== 'authenticated') return;

			try {
				const response = await fetch('/api/user/settings');
				if (response.ok) {
					userSettings = (await response.json()) || {};
				}
			} catch (error) {
				console.error('Error fetching user settings:', error);
			}
		}

		async function updateProgressByProvider() {
			if (!isLoggedIn) return null;

			try {
				await fetchUserSettings();
			} catch (error) {
				return null;
			}

			if (!userSettings || userSettings.episodeAutoUpdate !== true) {
				console.log('user has disabled auto-update');
				return null;
			}

			const user = $userStore;
			const provider = user?.user_metadata?.provider;

			// Check if anime is completed
			const isCompleted = await checkIfAnimeCompleted();
			if (isCompleted) {
				console.log('Skipping update for completed anime');
				return null;
			}

			if (provider === 'anilist') {
				return await updateAniListProgress();
			} else if (provider === 'mal') {
				return await updateMALProgress();
			}
		}

		async function checkIfAnimeCompleted() {
			try {
				const provider = $userStore?.user_metadata?.provider;

				if (provider === 'anilist') {
					return await checkIfAnimeCompletedAniList();
				} else if (provider === 'mal') {
					return await checkIfAnimeCompletedMAL();
				}

				return false;
			} catch (error) {
				console.error('Error checking completion status:', error);
				return false;
			}
		}

		async function checkIfAnimeCompletedAniList() {
			try {
				const QUERY = `
      query ($mediaId: Int, $userId: Int) {
        MediaList(userId: $userId, mediaId: $mediaId) {
          status
        }
      }`;

				let token = $userStore.user_metadata?.anilist_token;
				const userId = parseInt($userStore.user_metadata?.id);
				const tokenExpiry = $userStore.user_metadata?.token_expiry;

				if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
					token = await refreshAnilistToken();
					if (!token) {
						throw new Error('Could not refresh token');
					}
				}

				if (!token || !userId) {
					return false;
				}

				const response = await fetch(ANILIST_API, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Accept: 'application/json',
						Authorization: `Bearer ${token}`
					},
					body: JSON.stringify({
						query: QUERY,
						variables: {
							mediaId: parseInt(episodeInfo.anilist_id),
							userId: userId
						}
					})
				});

				const data = await response.json();
				return data.data.MediaList?.status === 'COMPLETED';
			} catch (error) {
				console.error('Error checking AniList status:', error);
				return false;
			}
		}

		async function checkIfAnimeCompletedMAL() {
			try {
				let token = $userStore.user_metadata?.mal_token;
				const tokenExpiry = $userStore.user_metadata?.token_expiry;

				if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
					token = await refreshMALToken();
					if (!token) {
						throw new Error('Could not refresh token');
					}
				}

				if (!token) {
					return false;
				}

				const malId = episodeInfo.mal_id;
				if (!malId) {
					return false;
				}

				// Use server proxy instead of direct MAL API call
				const response = await fetch('/api/mal/proxy/anime-details', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						animeId: malId,
						token
					})
				});

				if (!response.ok) {
					throw new Error('Failed to get anime details from MAL');
				}

				const data = await response.json();
				return data.my_list_status?.status === 'completed';
			} catch (error) {
				console.error('Error checking MAL status:', error);
				return false;
			}
		}

		async function updateMALProgress() {
			// console.log('updating mal');
			try {
				let token = $userStore.user_metadata?.mal_token;
				const tokenExpiry = $userStore.user_metadata?.token_expiry;

				// Check token expiry and refresh if needed
				if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
					token = await refreshMALToken();
					if (!token) {
						umami.track('malUpdateError', {
							is_embed: false,
							anime_title: animeTitle,
							anime_episode: episode,
							message: 'Could not refresh token'
						});
						throw new Error('Could not refresh token');
					}
				}

				if (!token) {
					throw new Error('No MAL authentication available');
				}

				// IMPORTANT: Make sure we're using MAL ID, not AniList ID
				const malId = episodeInfo.mal_id;

				if (!malId) {
					console.error('Missing MAL ID in episodeInfo:', episodeInfo);
					throw new Error('No MAL ID available for anime');
				}

				// console.log(`Updating MAL progress for anime ID: ${malId}, episode: ${episodeInfo.number}`);

				// Use server proxy instead of direct MAL API call
				const response = await fetch('/api/mal/proxy/update-progress', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						animeId: malId,
						episodeNumber: parseInt(episodeInfo.number),
						token
					})
				});

				if (!response.ok) {
					const errorData = await response.json();
					umami.track('malUpdateError', {
						is_embed: false,
						anime_title: animeTitle,
						anime_episode: episode,
						message: errorData.error
					});
					throw new Error(errorData.error || 'Failed to update MAL progress');
				}
				// console.log('updated mal');
				umami.track('malUpdate', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				return await response.json();
			} catch (error) {
				console.error('Error updating MAL progress:', error);
				toast.error('Nie udało się zaktualizować postępu na MAL');
				return null;
			}
		}

		async function refreshMALToken() {
			try {
				const response = await fetch('/api/mal/refresh-token', {
					method: 'POST'
				});

				if (!response.ok) {
					throw new Error('Failed to refresh token');
				}

				const { access_token } = await response.json();

				// Update user store with new token
				userStore.update((currentUser) => ({
					...currentUser,
					user_metadata: {
						...currentUser.user_metadata,
						mal_token: access_token
					}
				}));

				return access_token;
			} catch (error) {
				console.error('Error refreshing MAL token:', error);
				toast.error('Nie udało się odświeżyć tokena MAL. Możesz spróbować wylogować i zalogować się ponownie aby naprawić ten błąd.', {
					duration: Number.POSITIVE_INFINITY
				});
				return null;
			}
		}

		function invalidateCaches(updatedAnime) {
			const mediaCacheKey = `${cacheKeys.ANILIST_MEDIA}${updatedAnime.id}`;
			const continueWatchingCacheKeyAL = `${cacheKeys.ANILIST_WATCHING}${updatedAnime.id}`;
			const continueWatchingCacheKeyMAL = `${cacheKeys.MAL_WATCHING}${updatedAnime.mal_id}`;
			const continueWatchingCacheKeyMAL2 = `${cacheKeys.MAL_WATCHING}`;
			let HomeCW = JSON.parse(localStorage.getItem(cacheKeys.HOME_DATA));
			// Find entry of id "anime.id" and update its current_episode
			if (HomeCW && HomeCW.continueWatchingData) {
				HomeCW.continueWatchingData = HomeCW.continueWatchingData.map((item) => {
					if (parseInt(item.id) === parseInt(updatedAnime.anilist_id)) {
						return {
							...item,
							current_episode: updatedAnime.number,
							updated_at: new Date().toISOString()
						};
					}
					return item;
				});
				// console.log(HomeCW.continueWatchingData);
				localStorage.setItem(cacheKeys.HOME_DATA, JSON.stringify(HomeCW));
			}

			localStorage.removeItem(mediaCacheKey);
			localStorage.removeItem(continueWatchingCacheKeyAL);
			localStorage.removeItem(continueWatchingCacheKeyMAL);
			localStorage.removeItem(continueWatchingCacheKeyMAL2);
		}

		checkPlayingTimeTimer = setInterval(async () => {
			totalDuration = player.api('duration');
			watchedThreshold = (totalDuration / 10) * 75; // 75%
			// watchedThreshold = 10;
			// console.log(userPlayingSeconds, watchedThreshold);
			// console.log(userPlayingSeconds > watchedThreshold && !userWatchedEpisode);
			if (userPlayingSeconds > watchedThreshold && !userWatchedEpisode) {
				posthog.capture('user_completed_watching', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				umami.track('user_completed_watching', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});

				// Mark episode as watched in AniList
				await updateProgressByProvider();

				invalidateCaches(episodeInfo);

				// Update local progress store to 100%
				progressStore.updateProgress(provider === 'anilist' ? episodeInfo.anilist_id : episodeInfo.mal_id, episodeInfo.number, totalDuration, totalDuration, episodeInfo.thumbnail);

				userWatchedEpisode = true;
			} else if (userPlayingSeconds > viewedThreshold && !userViewedEpisode) {
				posthog.capture('user_started_watching', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				umami.track('user_started_watching', {
					is_embed: false,
					anime_title: animeTitle,
					anime_episode: episode
				});
				userViewedEpisode = true;
			}
		}, 10000); // Check every second, but only make requests every 10 seconds at most
	}

	function onPause() {
		clearInterval(playingTimer);
		clearInterval(checkPlayingTimeTimer);
		clearInterval(progressUpdateInterval);
	}

	function onStop() {
		clearInterval(playingTimer);
		clearInterval(checkPlayingTimeTimer);
		clearInterval(progressUpdateInterval);
	}

	function convertToSeconds(timestamp) {
		const [hours, minutes, seconds] = timestamp.split(':');
		const [sec, milliseconds] = seconds.split('.');

		const totalSeconds = parseInt(hours, 10) * 3600 + parseInt(minutes, 10) * 60 + parseInt(sec, 10) + parseInt(milliseconds, 10) / 1000;

		return totalSeconds;
	}

	function sleep(ms) {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	async function captureScreenshot() {
		let videoFrameImg;
		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d');
		const quality = player.api('quality');
		let qualityName;
		switch (quality) {
			case '1080p':
				qualityName = 'FHD';
				break;
			case '720p':
				qualityName = 'HD';
				break;
			case '480p':
				qualityName = 'SD';
				break;
			default:
				break;
		}

		const videoFrame = document.querySelector(`[src="${videoLink[qualityName]}"]`);
		if (videoFrame) {
			canvas.width = videoFrame.offsetWidth;
			canvas.height = videoFrame.offsetHeight;

			try {
				const videoFrameCanvas = await html2canvas(videoFrame, {
					backgroundColor: null,
					ignoreElements: (element) => {
						// Ignore elements with oklch color values
						const style = window.getComputedStyle(element);
						const hasOklch = style.backgroundColor.includes('oklch') || style.color.includes('oklch') || style.borderColor.includes('oklch');
						return hasOklch;
					}
				});
				videoFrameImg = videoFrameCanvas;
			} catch (err) {
				// console.error('Screenshot capture error:', err);
				// Fallback to direct video frame capture
				try {
					// Try to use video element directly if it's a video
					if (videoFrame.tagName === 'VIDEO') {
						canvas.width = videoFrame.videoWidth || videoFrame.offsetWidth;
						canvas.height = videoFrame.videoHeight || videoFrame.offsetHeight;
						ctx.drawImage(videoFrame, 0, 0, canvas.width, canvas.height);
						videoFrameImg = null; // We've drawn directly to canvas
					}
				} catch (fallbackErr) {
					console.error('Fallback screenshot method failed:', fallbackErr);
					toast?.error('Nie można zapisać zrzutu ekranu');
					return;
				}
			}
		}

		if (videoFrameImg) {
			ctx.drawImage(videoFrameImg, 0, 0, canvas.width, canvas.height);
		}

		// Trigger the download of the canvas content
		try {
			const img = canvas.toDataURL('image/png');
			const link = document.createElement('a');
			const currentTime = player.api('time');
			const formattedTime = formatTimestamp(currentTime);
			link.download = `${title.replaceAll('_', ' ')} ${episode.toString().padStart(2, '0')} - ${formattedTime}0.png`;
			link.href = img;
			umami.track('captureScreenshot', {
				is_embed: false,
				anime_title: animeTitle,
				anime_episode: episode,
				time: formattedTime
			});
			link.click();
		} catch (err) {
			console.error('Failed to create screenshot:', err);
			toast?.error('Nie można zapisać zrzutu ekranu');
		}
	}

	function formatTimestamp(time) {
		const hours = Math.floor(time / 3600);
		const minutes = Math.floor((time % 3600) / 60);
		const seconds = (time % 60).toFixed(2);
		return `${padZero(hours)}.${padZero(minutes)}.${padZero(seconds)}`;
		function padZero(num) {
			return num.toString().padStart(2, '0');
		}
	}

	function findPjsdivWithBackgroundImage(url) {
		// Get all pjsdiv elements in the document
		const pjsdivs = document.querySelectorAll('pjsdiv');

		// Filter them to find those with the specified background-image URL
		const matchedDivs = Array.from(pjsdivs).filter((div) => {
			const backgroundImage = div.style.backgroundImage;
			return backgroundImage === 'url("' + url + '")';
		});

		return matchedDivs;
	}

	function findPjsdivByStyles(requiredStyles) {
		// Convert the requiredStyles string to an array of individual style rules for easier checks
		const styleRules = requiredStyles
			.split(';')
			.map((rule) => rule.trim())
			.filter((rule) => rule);

		// Get all pjsdiv elements in the document
		const pjsdivs = document.querySelectorAll('pjsdiv');

		// Filter them to find those that match the provided style properties
		const matchedDivs = Array.from(pjsdivs).filter((div) => {
			return styleRules.every((rule) => {
				const [property, value] = rule.split(':').map((part) => part.trim());
				// Check if the div's style property matches the required value
				return div.style[property] === value;
			});
		});

		matchedDivs.forEach((div) => {
			div.addEventListener('mouseenter', onRelEnter);
			div.addEventListener('mouseleave', onRelLeave);
		});

		return matchedDivs;
	}

	const onRelEnter = (event) => {
		if (relElement) {
			relText[0].style.opacity = '1';
		}
	};

	// Function to handle mouse leaving the element
	const onRelLeave = (event) => {
		if (relElement) {
			relText[0].style.opacity = '1';
		}
	};

	async function onFinish() {
		if (nextEpisode[0]) {
			await sleep(100);
			relElement = findPjsdivWithBackgroundImage(nextEpisode[0].poster);
			relElement[0].style.width = '65%';
			relElement[0].style.height = '65%';
			relText = findPjsdivByStyles('position: relative; top: 0px; left: 0px; width: 90%; height: 90%; display: block; padding: 5%');
			relText[0].style.opacity = '1';
			relText[0].style.fontFamily = 'vt323';
			relText[0].style.fontSize = '50px';
			relText[0].style.lineHeight = '50px';
			relText[0].innerText = '';
			relText[0].innerText = `Następny odcinek:\n${nextEpisode[0].title}`;
		}
	}

	async function onFullscreen() {
		if (relElement) {
			relText[0].style.opacity = '1';
			relElement[0].style.width = '65%';
			relElement[0].style.height = '65%';
			relText = findPjsdivByStyles('position: relative; top: 0px; left: 0px; width: 90%; height: 90%; display: block; padding: 5%');
			relText[0].style.opacity = '1';
			relText[0].style.fontFamily = 'vt323';
			if (player.api('isfullscreen') === true) {
				relText[0].style.fontSize = '50px';
				relText[0].style.lineHeight = '50px';
			} else {
				relText[0].style.fontSize = '25px';
				relText[0].style.lineHeight = '25px';
			}
			relText[0].innerText = '';
			relText[0].innerText = `Następny odcinek:\n${nextEpisode[0].title}`;
		}
	}

	async function onKeyDown(e) {
		let key = e.key.toString().toLowerCase();
		switch (key) {
			case 'z':
				if (e.ctrlKey) {
					let time = player.api('time');
					switch (true) {
						case time < openingStart + 1:
							player.api('seek', 0);
							umami.track('seekToBegining', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
						case time < openingEnd + 1:
							player.api('seek', openingStart);
							umami.track('skipToOpening', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
						case time < endingStart + 1:
							umami.track('skipOpening', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							player.api('seek', openingEnd);
							break;
						case time < endingEnd + 1:
							umami.track('skipToEnding', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							player.api('seek', endingStart);
							umami.track('skipToEnding', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
					}
				} else if (!e.ctrlKey) {
					let time = player.api('time');
					switch (true) {
						case time < openingStart:
							player.api('seek', openingStart);
							umami.track('skipToOpening', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
						case time < openingEnd:
							player.api('seek', openingEnd);
							umami.track('skipOpening', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
						case time < endingStart:
							player.api('seek', endingStart);
							umami.track('skipToEnding', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
						case time < endingEnd:
							player.api('seek', endingEnd);
							umami.track('skipEnding', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
						case time > endingEnd:
							let duration = player.api('duration');
							player.api('seek', duration - 0.5);
							umami.track('skipToEnd', {
								is_embed: false,
								anime_title: animeTitle,
								anime_episode: episode
							});
							break;
					}
				}
				break;
			case 's':
				if (e.ctrlKey) {
					e.preventDefault();
					await captureScreenshot();
				}
				break;
			// case 'n':
			// 	e.preventDefault();
			// 	await captureScreenshot();
			// 	break;
			// case 'c':
			// 	if (e.ctrlKey) {
			// 		if (!playerTimelineFirefox[0]) {
			// 			playerTimeline[0] = playerTimelineChrome[0];
			// 		}
			// 		if (!playerTimelineChrome[0]) {
			// 			playerTimeline[0] = playerTimelineFirefox[0];
			// 		}

			// 		if (!playerTitleBar2Firefox[0]) {
			// 			playerTitleBar2[0] = playerTitleBar2Chrome[0];
			// 		}
			// 		if (!playerTitleBar2Chrome[0]) {
			// 			playerTitleBar2[0] = playerTitleBar2Firefox[0];
			// 		}

			// 		isUIHidden = !isUIHidden;
			// 		isUIHidden ? (playerTitleBar[0].style.display = 'none') : (playerTitleBar[0].style.display = 'block');
			// 		isUIHidden ? (playerTitleBar2[0].style.display = 'none') : (playerTitleBar2[0].style.display = 'block');
			// 		isUIHidden ? (playerTimeline[0].style.display = 'none') : (playerTimeline[0].style.display = 'block');
			// 		isUIHidden ? (playerTimeline2[0].style.display = 'none') : (playerTimeline2[0].style.display = 'block');
			// 	}
			// 	break;
			case ',':
			case '<':
				let currentTimeBack = player.api('time');
				player.api('seek', currentTimeBack - frameStep);
				break;
			case '.':
			case '>':
				let currentTimeForward = player.api('time');
				player.api('seek', currentTimeForward + frameStep);
				break;
			case '1':
			case '!':
				let duration1 = player.api('duration') / 10;
				player.api('seek', duration1 * 1);
				break;

			case '2':
			case '@':
				let duration2 = player.api('duration') / 10;
				player.api('seek', duration2 * 2);
				break;

			case '3':
			case '#':
				let duration3 = player.api('duration') / 10;
				player.api('seek', duration3 * 3);
				break;

			case '4':
			case '$':
				let duration4 = player.api('duration') / 10;
				player.api('seek', duration4 * 4);
				break;

			case '5':
			case '%':
				let duration5 = player.api('duration') / 10;
				player.api('seek', duration5 * 5);
				break;

			case '6':
			case '^':
				let duration6 = player.api('duration') / 10;
				player.api('seek', duration6 * 6);
				break;

			case '7':
			case '&':
				let duration7 = player.api('duration') / 10;
				player.api('seek', duration7 * 7);
				break;

			case '8':
			case '*':
				let duration8 = player.api('duration') / 10;
				player.api('seek', duration8 * 8);
				break;

			case '9':
			case '(':
				let duration9 = player.api('duration') / 10;
				player.api('seek', duration9 * 9);
				break;

			case '0':
			case ')':
				player.api('seek', 0);
				break;
		}
	}
</script>

<svelte:window on:keydown={onKeyDown} />
<div id="player" class="h-full" />

{#if showErrorDialog}
	<button on:click={() => (showErrorDialog = false)} class="fixed inset-0 z-40 w-full h-full bg-black/50" aria-label="Zamknij" />
	<div class="fixed top-1/2 left-1/2 z-50 w-[90%] max-w-md -translate-x-1/2 -translate-y-1/2 rounded-lg bg-gray-900 p-6 text-white">
		<div class="text-2xl font-bold text-primary-500">
			{getErrorMessage(currentError?.errorType || 'DEFAULT').title}
		</div>
		<div class="mt-2 text-gray-400">
			<p class="mb-4">
				{getErrorMessage(currentError?.errorType || 'DEFAULT').message}
			</p>

			{#if currentError?.errorType !== 'EPISODE_UNAVAILABLE'}
				<p class="mb-4">Możesz zgłosić ten problem na naszym serwerze Discord (discord.gg/splendourcafe). Wyślij szczegóły techniczne w prywatnej wiadomości do @4lajf.</p>
			{/if}

			{#if currentError}
				<div class="mt-4">
					<button on:click={() => (showTechnicalDetails = !showTechnicalDetails)} class="mb-2 cursor-pointer text-primary-600 hover:underline">
						{showTechnicalDetails ? 'Ukryj' : 'Pokaż'} szczegóły techniczne
					</button>

					{#if showTechnicalDetails}
						<div class="p-3 mt-2 text-sm bg-gray-800 rounded">
							<p class="text-gray-300">Typ błędu: {currentError.errorType}</p>
							<p class="text-gray-300">Kontekst: {currentError.context}</p>
							<p class="text-gray-300">Czas: {new Date(currentError.timestamp).toLocaleString()}</p>

							{#if currentError.details}
								<div class="mt-2">
									<p class="mt-2 font-semibold text-gray-200">Szczegóły techniczne:</p>
									<pre class="p-2 mt-1 overflow-x-auto text-xs text-gray-200 bg-gray-700 rounded">
                        {JSON.stringify(currentError.details, null, 2)}
                      </pre>
								</div>
							{/if}
						</div>
					{/if}
				</div>
			{/if}
		</div>

		<div class="flex justify-end mt-6 space-x-2">
			<Button variant="default" on:click={() => (showErrorDialog = false)} class="text-gray-400 bg-gray-700 hover:bg-gray-600">Zamknij</Button>
			<Button variant="default" on:click={goToDiscord} class="text-gray-100 bg-blue-400 hover:bg-blue-500">Discord</Button>
		</div>
	</div>
{/if}

{#if showIOSWarning}
	<button on:click={() => (showIOSWarning = false)} class="fixed inset-0 z-40 w-full h-full bg-black/50" aria-label="Close dialog" />
	<div class="fixed top-1/2 left-1/2 z-50 flex max-h-[70vh] w-[90%] max-w-md -translate-x-1/2 -translate-y-1/2 flex-col overflow-hidden rounded-lg bg-gray-900 text-white">
		<div class="flex-1 p-4 overflow-y-auto">
			<div class="text-sm text-gray-400 sm:text-base">
				<p class="mb-2 sm:mb-4">Ten odtwarzacz może nie działać na telefonie z systemem iOS. Pobierz odcinek z naszego Discorda (discord.gg/splendourcafe) bądź oglądaj z Chrome na komputerze.</p>
			</div>
		</div>

		<Button variant="default" on:click={() => (showIOSWarning = false)} class="w-full px-4 py-2 text-xs text-gray-400 bg-gray-700 hover:bg-gray-600 sm:text-sm">Zamknij</Button>
	</div>
{/if}

<style>
	@font-face {
		font-family: 'vt323';
		src: url('https://pixeldrain.com/api/file/pPZPBSgr') format('woff2');
		font-weight: normal;
		font-style: normal;
	}
	@font-face {
		font-family: 'roboto';
		src: url('https://pixeldrain.com/api/file/SQMHh4vJ') format('woff2');
		font-weight: normal;
		font-style: normal;
	}
</style>
