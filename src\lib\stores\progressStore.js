// src/lib/stores/progressStore.js
import { writable, get } from 'svelte/store';
import { browser } from '$app/environment';
import { userStore } from '$lib/stores/userLogin';
import { toast } from 'svelte-sonner';

async function isAnimeCompleted(animeId) {
  try {
    const user = get(userStore);
    if (!user?.user_metadata?.provider) {
      return false;
    }

    const provider = user.user_metadata.provider;

    // Check anime status from appropriate source
    if (provider === 'anilist') {
      return await isAnimeCompletedAniList(animeId);
    } else if (provider === 'mal') {
      return await isAnimeCompletedMAL(animeId);
    }

    return false;
  } catch (error) {
    console.error('Error checking anime completion status:', error);
    return false;
  }
}

async function isAnimeCompletedAniList(animeId) {
  const ANILIST_API = 'https://graphql.anilist.co';
  const QUERY = `
    query ($mediaId: Int, $userId: Int) {
      MediaList(userId: $userId, mediaId: $mediaId) {
        status
      }
    }`;

  try {
    const user = get(userStore);
    if (!user?.user_metadata?.anilist_token || !user?.user_metadata?.id) {
      return false;
    }

    let token = user.user_metadata.anilist_token;
    const tokenExpiry = user.user_metadata.token_expiry;
    const userId = parseInt(user.user_metadata.id);

    // Check token expiry and refresh if needed
    if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
      token = await refreshAnilistToken();
      if (!token) {
        throw new Error('Could not refresh token');
      }
    }

    const response = await fetch(ANILIST_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        query: QUERY,
        variables: {
          mediaId: parseInt(animeId),
          userId: userId
        }
      })
    });

    const data = await response.json();
    if (data.errors) {
      throw new Error(data.errors[0]?.message || 'AniList API returned errors');
    }

    return data.data.MediaList?.status === 'COMPLETED';
  } catch (error) {
    console.error('Error checking AniList completion status:', error);
    return false;
  }
}

async function isAnimeCompletedMAL(animeId) {
  try {
    const user = get(userStore);
    if (!user?.user_metadata?.mal_token) {
      return false;
    }

    let token = user.user_metadata.mal_token;
    const tokenExpiry = user.user_metadata.token_expiry;

    // Check token expiry and refresh if needed
    if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
      token = await refreshMALToken();
      if (!token) {
        throw new Error('Could not refresh token');
      }
    }

    // Use server proxy instead of direct MAL API call
    const response = await fetch('/api/mal/proxy/anime-details', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        animeId: animeId,
        token
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get anime details from MAL');
    }

    const data = await response.json();
    return data.my_list_status?.status === 'completed';
  } catch (error) {
    console.error('Error checking MAL completion status:', error);
    return false;
  }
}

function createProgressStore() {
  const { subscribe, set, update } = writable({});
  let syncTimeout;

  // Load initial state from localStorage
  if (browser) {
    const stored = localStorage.getItem('animeProgress');
    if (stored) {
      set(JSON.parse(stored));
    }
  }

  async function refreshAnilistToken() {
    try {
      const response = await fetch('/api/anilist/refresh-token', {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const { access_token } = await response.json();

      // Update user store with new token
      userStore.update((currentUser) => ({
        ...currentUser,
        user_metadata: {
          ...currentUser.user_metadata,
          anilist_token: access_token
        }
      }));

      return access_token;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return null;
    }
  }

  async function refreshMALToken() {
    try {
      const response = await fetch('/api/mal/refresh-token', {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const { access_token } = await response.json();

      // Update user store with new token
      userStore.update((currentUser) => ({
        ...currentUser,
        user_metadata: {
          ...currentUser.user_metadata,
          mal_token: access_token
        }
      }));

      return access_token;
    } catch (error) {
      console.error('Error refreshing MAL token:', error);
      return null;
    }
  }

  async function syncToDatabase(progress) {
    const user = get(userStore);
    if (user?.role !== 'authenticated') return
    try {
      const response = await fetch('/api/profile/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ continue_watching: progress }),
      });
      if (!response.ok) {
        throw new Error('Failed to sync progress');
      }
    } catch (error) {
      console.error('Error syncing progress:', error);
    }
  }

  async function syncToAniList(animeId, episodeNumber) {
    const ANILIST_API = 'https://graphql.anilist.co';
    const MUTATION = `
      mutation ($mediaId: Int, $progress: Int) {
        SaveMediaListEntry (mediaId: $mediaId, progress: $progress) {
          id 
          progress
        }
      }`;

    try {
      const user = get(userStore);
      if (!user?.user_metadata?.anilist_token) {
        return;
      }

      let token = user.user_metadata.anilist_token;
      const tokenExpiry = user.user_metadata.token_expiry;

      // Check token expiry and refresh if needed
      if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
        token = await refreshAnilistToken();
        if (!token) {
          throw new Error('Could not refresh token');
        }
      }

      const response = await fetch(ANILIST_API, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          query: MUTATION,
          variables: {
            mediaId: parseInt(animeId),
            progress: parseInt(episodeNumber)
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.errors?.[0]?.message || 'Failed to sync with AniList');
      }

      const data = await response.json();

      if (data.errors) {
        throw new Error(data.errors[0]?.message || 'AniList API returned errors');
      }

      return data.data.SaveMediaListEntry;
    } catch (error) {
      console.error('Error syncing with AniList:', error);
      toast.error('Nie udało się zaktualizować postępu na AniList');
    }
  }

  async function syncToMAL(animeId, episodeNumber) {
    try {
      const user = get(userStore);
      if (!user?.user_metadata?.mal_token || user?.user_metadata?.provider !== 'mal') {
        return;
      }

      let token = user.user_metadata.mal_token;
      const tokenExpiry = user.user_metadata.token_expiry;

      // Check token expiry and refresh if needed
      if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
        token = await refreshMALToken();
        if (!token) {
          throw new Error('Could not refresh token');
        }
      }

      // Use server proxy instead of direct MAL API call
      const response = await fetch('/api/mal/proxy/update-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          animeId: animeId,
          episodeNumber: parseInt(episodeNumber),
          token
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to sync with MAL');
      }

      return await response.json();
    } catch (error) {
      console.error('Error syncing with MAL:', error);
      toast.error('Nie udało się zaktualizować postępu na MAL');
    }
  }

  return {
    subscribe,
    updateProgress: (animeId, episodeNumber, currentTime, duration, thumbnail, hasError = false) => {
      // Skip updating progress if there's an error with the video
      if (hasError) {
        console.warn('Skipping progress update due to video error');
        return;
      }

      update(store => {
        const newStore = { ...store };
        if (!newStore[animeId]) {
          newStore[animeId] = {};
        }

        const progress = Math.floor((currentTime / duration) * 100);
        newStore[animeId][episodeNumber] = {
          progress,
          currentTime,
          duration,
          updatedAt: new Date().toISOString(),
        };

        // Save to localStorage
        if (browser) {
          localStorage.setItem('animeProgress', JSON.stringify(newStore));
        }

        // Debounce database sync
        if (syncTimeout) {
          clearTimeout(syncTimeout);
        }
        syncTimeout = setTimeout(async () => {
          syncToDatabase(newStore);

          // Sync to appropriate service if progress is over 85%
          if (progress > 85) {
            const user = get(userStore);
            const provider = user?.user_metadata?.provider;

            try {
              // First check user settings
              const settingsResponse = await fetch('/api/user/settings');
              let userSettings = {};
              if (settingsResponse.ok) {
                userSettings = await settingsResponse.json() || {};
              }

              // Only update if autoUpdate is enabled (or setting not specified)
              if (userSettings?.episodeAutoUpdate !== false) {
                // Then check if anime is already completed
                const completed = await isAnimeCompleted(animeId);

                // Only update if not completed
                if (!completed) {
                  if (provider === 'anilist') {
                    syncToAniList(animeId, episodeNumber);
                  } else if (provider === 'mal') {
                    syncToMAL(animeId, episodeNumber);
                  }
                } else {
                  // console.log('Skipping progress update for completed anime:', animeId);
                }
              }
            } catch (error) {
              console.error('Error during progress sync:', error);
            }
          }
        }, 5000); // Sync after 5 seconds of no updates

        return newStore;
      });
    },

    getProgress: (animeId, episodeNumber) => {
      const store = get(progressStore);
      return store[animeId]?.[episodeNumber] || null;
    },
    clearProgress: (animeId, episodeNumber) => {
      update(store => {
        const newStore = { ...store };
        if (newStore[animeId]) {
          delete newStore[animeId][episodeNumber];
          if (Object.keys(newStore[animeId]).length === 0) {
            delete newStore[animeId];
          }
        }

        if (browser) {
          localStorage.setItem('animeProgress', JSON.stringify(newStore));
        }
        syncToDatabase(newStore);
        return newStore;
      });
    },
    importProgress: (progress) => {
      update(store => {
        const newStore = { ...store, ...progress };
        if (browser) {
          localStorage.setItem('animeProgress', JSON.stringify(newStore));
        }
        return newStore;
      });
    }
  };
}

export const progressStore = createProgressStore();