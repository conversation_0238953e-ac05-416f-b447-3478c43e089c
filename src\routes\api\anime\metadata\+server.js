import { json } from '@sveltejs/kit';

export async function POST({ request, locals: { supabase } }) {
  try {
    const { animeIds, provider = 'anilist' } = await request.json();

    if (!Array.isArray(animeIds) || animeIds.length === 0) {
      return json({ metadata: [] });
    }

    let metadata;
    
    // Query different ID field based on provider
    if (provider === 'mal') {
      // Query by MAL IDs
      const { data, error } = await supabase
        .from('anime_metadata')
        .select('mal_id, anilist_id, cover_image')
        .in('mal_id', animeIds)
        .eq('hidden', false);

      if (error) {
        console.error('Error fetching anime metadata by MAL ID:', error);
        throw error;
      }
      
      // Map the results with consistent field names
      metadata = data.map(item => ({
        mal_id: item.mal_id,
        anilist_id: item.anilist_id,
        cover_image: item.cover_image
      }));
    } else {
      // Default: Query by AniList IDs
      const { data, error } = await supabase
        .from('anime_metadata')
        .select('anilist_id, mal_id, cover_image')
        .in('anilist_id', animeIds)
        .eq('hidden', false);

      if (error) {
        console.error('Error fetching anime metadata by AniList ID:', error);
        throw error;
      }
      
      // Map the results with consistent field names
      metadata = data.map(item => ({
        anilist_id: item.anilist_id,
        mal_id: item.mal_id,
        cover_image: item.cover_image
      }));
    }

    return json({ metadata: metadata || [] });

  } catch (error) {
    console.error('Error in /api/anime/metadata:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch anime metadata',
      details: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}