// src/routes/api/mal/proxy/watching/+server.js
import { json } from '@sveltejs/kit';

export async function POST({ request }) {
  const { token } = await request.json();

  if (!token) {
    return json({ error: 'No token provided' }, { status: 400 });
  }

  try {
    // MAL API pagination handling
    let allEntries = [];
    let nextUrl = 'https://api.myanimelist.net/v2/users/@me/animelist?status=watching&limit=100&fields=list_status,num_episodes,title,main_picture,media_type,status,start_date';

    while (nextUrl) {
      const response = await fetch(nextUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`MAL API returned ${response.status}`);
      }

      const data = await response.json();
      allEntries = [...allEntries, ...data.data];

      // Check if there's a next page
      nextUrl = data.paging?.next || null;
    }

    return json({ entries: allEntries });
  } catch (error) {
    console.error('Error fetching MAL watching list:', error);
    return json({ error: error.message }, { status: 500 });
  }
}