// src/routes/api/admin/groups/delete/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminByRole } from '$lib/utils/roleUtils';

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function DELETE({ request, locals }) {
  // Check if user is authenticated
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is an admin
  const hasAdminRole = await isAdminByRole(user.id);

  if (!hasAdminRole) {
    return json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    const { episodeId, groupName } = await request.json();

    if (!episodeId || !groupName) {
      return json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // The other_groups field doesn't exist in anime_new table
    // This functionality has been replaced by the new group management system
    return json({ error: 'This API endpoint is deprecated. Use /api/anime/update-episode-sources instead.' }, { status: 410 });


  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
