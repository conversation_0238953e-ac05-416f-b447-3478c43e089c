<script>
	//src/routes/search/+page.svelte
	import NavbarDesktop from '$lib/components/sections/navbar/NavbarDesktop.svelte';
	import NavbarMobile from '$lib/components/sections/navbar/NavbarMobile.svelte';
	import DataTable from './data-table.svelte';
	import UserNav from './user-nav.svelte';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import { page } from '$app/stores';
	import { writable } from 'svelte/store';
	import { browser } from '$app/environment';
	import MoreModal from '$lib/components/sections/shared/MoreModal.svelte';

	export let data;
	let preferRomaji;
	if (!data.userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = data.userSettings.titleLanguage === 'romaji';
	}
	const animeData = writable(data.animeData);
	const totalCount = writable(data.totalCount);
	const filterCounts = writable(data.filterCounts);
	const isLoading = writable(false);
	const isLoadingMore = writable(false);

	let hasMore = data.hasMore;
	let currentPage = data.currentPage;
	let mounted = false;
	let isMobileLandscape = false;

	const currentFilters = writable({
		search: '',
		genres: [],
		status: [],
		format: [],
		year: [],
		season: [],
		source: [],
		// Add sorting parameters with defaults
		sortField: 'popularity',
		sortDirection: 'desc'
	});

	async function fetchAnimeData(filters, page = 1) {
		try {
			if (page === 1) {
				isLoading.set(true);
			} else {
				isLoadingMore.set(true);
			}

			const params = new URLSearchParams();

			// Handle search parameter
			if (filters.search) {
				params.set('search', filters.search);
			}

			// Handle genre filters (multiple selection)
			if (filters.genres && filters.genres.length > 0) {
				params.set('genres', filters.genres.join(','));
			}

			// Handle single-selection filters
			const singleSelectionFilters = ['status', 'format', 'year', 'season', 'source'];

			singleSelectionFilters.forEach((filter) => {
				if (filters[filter] && filters[filter].length === 1) {
					params.set(filter, filters[filter][0]);
				}
			});

			// Set pagination and sorting
			params.set('page', page.toString());
			params.set('pageSize', '12');

			// Ensure sort parameters are sent
			params.set('sortField', filters.sortField || 'popularity');
			params.set('sortDirection', filters.sortDirection || 'desc');

			// Add preferRomaji parameter
			params.set('preferRomaji', preferRomaji.toString());

			const response = await fetch(`/api/search?${params}`);
			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const newData = await response.json();

			if (page === 1) {
				// Reset data for new searches
				animeData.set(newData.data);
				filterCounts.set(newData.filterCounts);
				currentPage = 1;
			} else {
				// Append data for infinite loading
				animeData.update((currentData) => [...currentData, ...newData.data]);
			}

			totalCount.set(newData.total);
			hasMore = newData.hasMore;
			currentPage = page;

			return newData.hasMore;
		} catch (error) {
			console.error('Error fetching anime data:', error);
			return false;
		} finally {
			isLoading.set(false);
			isLoadingMore.set(false);
		}
	}

	async function handleLoadMore({ detail: { loaded, complete } }) {
		try {
			if (!hasMore || $isLoadingMore) {
				complete();
				return;
			}

			const nextPage = currentPage + 1;
			const moreAvailable = await fetchAnimeData($currentFilters, nextPage);

			if (moreAvailable) {
				loaded();
			} else {
				complete();
			}
		} catch (error) {
			console.error('Error loading more:', error);
			complete();
		}
	}

	async function handleFilterChange(newFilters) {
		currentFilters.set(newFilters);
		hasMore = true;
		await fetchAnimeData(newFilters, 1);
	}

	function checkMobileLandscape() {
		const isMobile = window.innerWidth <= 768;
		isMobileLandscape = isMobile && window.innerWidth > window.innerHeight;
	}

	onMount(() => {
		mounted = true;

		if ($page.state && $page.state.scrollY) {
			setTimeout(() => {
				window.scrollTo({
					top: $page.state.scrollY
				});
			}, 0);
		}

		checkMobileLandscape();
		window.addEventListener('resize', checkMobileLandscape);

		return () => {
			window.removeEventListener('resize', checkMobileLandscape);
		};
	});

	$: if (mounted) {
		if (isMobileLandscape) {
			document.body.classList.add('landscape-overlay');
		} else {
			document.body.classList.remove('landscape-overlay');
		}
	}

	// Define reactive title and description
	$: title = 'Wyszukiwarka Anime - Lycoris';
	$: description = 'Znajdź swoje ulubione anime w naszej bazie danych. Przeglądaj, filtruj i odkrywaj nowe serie na lycoris.cafe';
	$: imageUrl = 'https://pixeldrain.com/api/file/nzfyjq8f';
	$: canonicalUrl = browser ? window.location.href : 'https://lycoris.cafe/search';

	// Add state for the Stremio modal
	let showStremioModal = false;

	function handleCloseStremioModal() {
		showStremioModal = false;
	}
</script>

<svelte:head>
	<!-- Basic metadata -->
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- OpenGraph tags -->
	<meta name="twitter:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="website" />
	<meta property="og:site_name" content="Lycoris" />

	<!-- Twitter Card tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:site" content="@lycoris_cafe" />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={imageUrl} />
	<meta name="twitter:image:alt" content="Lycoris.cafe - Wyszukiwarka Anime" />

	<!-- Canonical URL -->
	<link rel="canonical" href={canonicalUrl} />

	<!-- Additional SEO metadata -->
	<meta name="keywords" content="anime, wyszukiwarka anime, baza danych anime, lycoris, lycoris cafe, filtrowanie anime" />
	<meta name="theme-color" content="#ee8585" />
	<meta name="author" content="Lycoris" />
	<meta name="robots" content="index, follow" />
</svelte:head>

{#if mounted}
	<div in:fade={{ duration: 500 }}>
		<main class="no-select flex h-full flex-1 flex-col overflow-x-hidden bg-gray-900 opacity-100 md:ml-16">
			<NavbarDesktop on:showStremioModal={() => (showStremioModal = true)} />
			<NavbarMobile />

			<div class="flex-1 overflow-y-auto">
				<div class="space-y-4 p-4 md:space-y-8 md:p-8" in:fade={{ duration: 150, delay: 150 }} out:fade={{ duration: 150 }}>
					<DataTable
						{preferRomaji}
						data={$animeData}
						totalCount={$totalCount}
						filterCounts={$filterCounts}
						onFilterChange={handleFilterChange}
						currentFilters={$currentFilters}
						onLoadMore={handleLoadMore}
						isLoading={$isLoading}
						isLoadingMore={$isLoadingMore}
						{hasMore}
					/>
				</div>
			</div>
		</main>
	</div>
{/if}

{#if showStremioModal}
	<MoreModal {handleCloseStremioModal} />
{/if}

<style>
	/* Prevent scrolling when overlay is active */
	:global(body.landscape-overlay) {
		overflow: hidden;
		position: fixed;
		width: 100%;
		height: 100%;
	}

	.no-select {
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}
</style>
