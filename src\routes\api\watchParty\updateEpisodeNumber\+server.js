import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request }) {
	const { roomCode, episodeNumber } = await request.json();

	if (!roomCode || !episodeNumber) {
		return new Response(JSON.stringify({ error: 'Room code and channel data are required' }), {
			status: 400,
			headers: { 'Content-Type': 'application/json' }
		});
	}

	const result = await setChannelDataForRoom(roomCode, episodeNumber);

	if (result.error) {
		return new Response(JSON.stringify(result), {
			status: 400,
			headers: { 'Content-Type': 'application/json' }
		});
	}

	return new Response(JSON.stringify(result), {
		status: 200,
		headers: { 'Content-Type': 'application/json' }
	});
}

async function setChannelDataForRoom(roomCode, episodeNumber) {
	const { data, error } = await supabase.from('rooms').update({ anime_episode: episodeNumber }).eq('room_code', roomCode).select();
	if (error) {
		console.error(error);
		return { error: 'Failed to update channel data' };
	}

	if (!data || data.length === 0) {
		return { error: `Room ${roomCode} does not exist` };
	}

	return { success: true, message: 'Channel data updated successfully' };
}
