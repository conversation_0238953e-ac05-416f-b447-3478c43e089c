// src/routes/api/mal/proxy/update-progress/+server.js
import { json } from '@sveltejs/kit';

export async function POST({ request }) {
  const { animeId, episodeNumber, token } = await request.json();

  if (!animeId || !episodeNumber || !token) {
    return json({ error: 'Missing required parameters' }, { status: 400 });
  }

  try {
    // console.log(`Attempting to update MAL anime ID: ${animeId}, episode: ${episodeNumber}`);

    // Use PUT with status parameter which will create the entry if it doesn't exist
    const response = await fetch(`https://api.myanimelist.net/v2/anime/${animeId}/my_list_status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${token}`
      },
      body: new URLSearchParams({
        status: 'watching',
        num_watched_episodes: episodeNumber
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`MAL API error (${response.status}): ${errorText}`);
      throw new Error(`MAL API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (error) {
    console.error('Error updating progress on MAL:', error);
    return json({ error: error.message }, { status: 500 });
  }
}