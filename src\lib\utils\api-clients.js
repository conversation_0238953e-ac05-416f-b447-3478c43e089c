/**
 * Client-side API utility functions for external APIs
 * These functions call external APIs directly from the client to avoid server-side rate limiting
 */

/**
 * Search for anime on Jikan API (unofficial MyAnimeList API)
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Search results array
 */
export async function searchJikan(query) {
  try {
    // Use the Jikan v4 API - get 10 results
    const response = await fetch(`https://api.jikan.moe/v4/anime?q=${encodeURIComponent(query)}&limit=10`);

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error(`Zbyt wiele zapytań do API. Odczekaj chwilę i spróbuj ponownie.`);
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Return all results
    if (data.data && data.data.length > 0) {
      return data.data;
    }

    return null;
  } catch (error) {
    console.error('Error searching Jikan:', error);
    throw error;
  }
}

/**
 * Search for anime on AniList
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Search results array
 */
export async function searchAniList(query) {
  try {
    const response = await fetch('https://graphql.anilist.co', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query ($search: String) {
            Page(page: 1, perPage: 10) {
              media(search: $search, type: ANIME) {
                id
                idMal
                title {
                  romaji
                  english
                  native
                }
                type
                episodes
                coverImage {
                  medium
                }
                siteUrl
                synonyms
              }
            }
          }
        `,
        variables: {
          search: query
        }
      })
    });

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error(`Zbyt wiele zapytań do API. Odczekaj chwilę i spróbuj ponownie.`);
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.errors) {
      throw new Error(data.errors[0].message);
    }

    return data.data.Page.media;
  } catch (error) {
    console.error('Error searching AniList:', error);
    throw error;
  }
}
