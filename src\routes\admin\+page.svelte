<!-- src/routes/admin/+page.svelte -->
<script>
	import { page } from '$app/stores';
	import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '$lib/components/ui/dialog';
	import { Input } from '$lib/components/ui/input';
	import { Ban } from 'lucide-svelte';
	import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '$lib/components/ui/tabs';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { AlertCircle, CheckCircle, XCircle, Trash2 } from 'lucide-svelte';
	import { toast } from 'svelte-sonner';
	import { Slider } from '$lib/components/ui/slider';

	export let data;

	let activeTab = 'recent';
	let comments = [];
	let spamQueue = [];
	let reportQueue = [];
	let minReportThreshold = 5;

	let showBanDialog = false;
	let banReason = '';
	let userToBan = null;

	$: {
		if (data) {
			comments = data.recentComments;
			spamQueue = data.spamQueue;
			reportQueue = data.reportQueue;
		}
	}

	$: filteredReportQueue = reportQueue.filter((report) => report.reportCount >= minReportThreshold);

	async function handleMarkAsSpoiler(commentId) {
		try {
			const response = await fetch('/api/admin/comments/mark-spoiler', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ commentId })
			});

			if (!response.ok) throw new Error('Failed to mark comment as spoiler');

			// Update UI to show the comment is now marked as spoiler
			comments = comments.map((c) => (c.id === commentId ? { ...c, is_spoiler: true } : c));
			spamQueue = spamQueue.map((c) => (c.id === commentId ? { ...c, is_spoiler: true } : c));
			reportQueue = reportQueue.map((r) => (r.comment.id === commentId ? { ...r, comment: { ...r.comment, is_spoiler: true } } : r));

			toast.success('Comment marked as spoiler');
		} catch (error) {
			toast.error('Failed to mark comment as spoiler');
		}
	}

	async function handleBan(userId) {
		if (!banReason.trim()) {
			toast.error('Please provide a reason for banning');
			return;
		}

		try {
			const response = await fetch('/api/admin/users/ban', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					userId,
					reason: banReason
				})
			});

			if (!response.ok) throw new Error('Failed to ban user');

			// Remove comments from banned user
			comments = comments.filter((c) => c.user_id !== userId);
			spamQueue = spamQueue.filter((c) => c.user_id !== userId);
			reportQueue = reportQueue.filter((r) => r.comment.user_id !== userId);

			showBanDialog = false;
			banReason = '';
			userToBan = null;
			toast.success('User banned successfully');
		} catch (error) {
			toast.error('Failed to ban user');
		}
	}

	async function handleDelete(commentId) {
		try {
			const response = await fetch('/api/admin/comments/delete', {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ commentId })
			});

			if (!response.ok) throw new Error('Failed to delete comment');

			// Remove comment from respective lists
			comments = comments.filter((c) => c.id !== commentId);
			spamQueue = spamQueue.filter((c) => c.id !== commentId);
			reportQueue = reportQueue.filter((c) => c.id !== commentId);

			toast.success('Comment deleted successfully');
		} catch (error) {
			toast.error('Failed to delete comment');
		}
	}

	async function handleApprove(commentId) {
		try {
			const response = await fetch('/api/admin/comments/approve', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ commentId })
			});

			if (!response.ok) throw new Error('Failed to approve comment');

			// Find the comment in report queue
			const reportedComment = reportQueue.find((r) => r.comment.id === commentId);
			if (reportedComment) {
				// Add to recent comments if not already there
				const isInRecentComments = comments.some((c) => c.id === reportedComment.comment.id);
				if (!isInRecentComments) {
					comments = [reportedComment.comment, ...comments];
				}
				// Remove from report queue
				reportQueue = reportQueue.filter((r) => r.comment.id !== commentId);
			}

			toast.success('Comment approved successfully');
		} catch (error) {
			toast.error('Failed to approve comment');
		}
	}

	async function handleDeny(commentId) {
		try {
			const response = await fetch('/api/admin/comments/deny', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ commentId })
			});

			if (!response.ok) throw new Error('Failed to deny comment');

			// Remove from all lists
			comments = comments.filter((c) => c.id !== commentId);
			spamQueue = spamQueue.filter((c) => c.id !== commentId);
			reportQueue = reportQueue.filter((r) => r.comment.id !== commentId);

			toast.success('Comment denied successfully');
		} catch (error) {
			toast.error('Failed to deny comment');
		}
	}
</script>

<div class="container mx-auto p-4">
	<h1 class="mb-6 text-2xl font-bold">Admin Dashboard</h1>


	<Tabs value={activeTab} onValueChange={(value) => (activeTab = value)}>
		<TabsList>
			<TabsTrigger value="recent" class="cursor-pointer hover:bg-gray-900">
				Recent Comments
				<Badge variant="secondary" class="ml-2">{comments.length}</Badge>
			</TabsTrigger>
			<TabsTrigger value="spam" class="cursor-pointer hover:bg-gray-900">
				Spam Queue
				<Badge variant="destructive" class="ml-2">{spamQueue.length}</Badge>
			</TabsTrigger>
			<TabsTrigger value="reports" class="cursor-pointer hover:bg-gray-900">
				Report Queue
				<Badge variant="destructive" class="ml-2">{filteredReportQueue.length}</Badge>
			</TabsTrigger>
		</TabsList>

		<TabsContent value="recent">
			<Card>
				<CardHeader>
					<CardTitle>Recent Comments</CardTitle>
					<CardDescription>Latest comments across the platform</CardDescription>
				</CardHeader>
				<CardContent>
					{#each comments as comment}
						<div class="mb-4 rounded-lg border p-4">
							<div class="flex items-center justify-between">
								<div class="flex items-center gap-2">
									<img src={comment.avatar} alt="" class="h-8 w-8 rounded-full" />
									<span class="font-semibold">{comment.author}</span>
									{#if comment.english_title}
										<span class="text-sm text-gray-500">on {comment.english_title}</span>
									{/if}
									{#if comment.is_spoiler}
										<Badge variant="secondary">Spoiler</Badge>
									{/if}
								</div>
								<div class="flex gap-2">
									<Button variant="secondary" size="sm" on:click={() => handleMarkAsSpoiler(comment.id)} disabled={comment.is_spoiler}>
										<AlertCircle class="h-4 w-4 cursor-pointer" />
									</Button>
									<Button variant="destructive" size="sm" on:click={() => handleDelete(comment.id)}>
										<Trash2 class="h-4 w-4 cursor-pointer" />
									</Button>
									<Button
										variant="destructive"
										size="sm"
										on:click={() => {
											userToBan = comment;
											showBanDialog = true;
										}}
									>
										<Ban class="h-4 w-4 cursor-pointer" />
									</Button>
								</div>
							</div>
							<p class="mt-2">{comment.content}</p>
							<div class="mt-2 text-sm text-gray-500">
								{new Date(comment.created_at).toLocaleString()}
							</div>
						</div>
					{/each}
				</CardContent>
			</Card>
		</TabsContent>

		<TabsContent value="spam">
			<Card>
				<CardHeader>
					<CardTitle>Spam Queue</CardTitle>
					<CardDescription>Comments flagged as potential spam</CardDescription>
				</CardHeader>
				<CardContent>
					{#each spamQueue as comment}
						<div class="mb-4 rounded-lg border p-4">
							<div class="flex items-center justify-between">
								<div class="flex items-center gap-2">
									<img src={comment.avatar} alt="" class="h-8 w-8 rounded-full" />
									<span class="font-semibold">{comment.author}</span>
								</div>
								<div class="flex gap-2">
									<Button variant="default" size="sm" class="cursor-pointer" on:click={() => handleApprove(comment.id)}>
										<CheckCircle class="h-4 w-4" />
									</Button>
									<Button variant="destructive" class="cursor-pointer" size="sm" on:click={() => handleDeny(comment.id)}>
										<XCircle class="h-4 w-4" />
									</Button>
									<Button
										variant="destructive"
										size="sm"
										class="cursor-pointer"
										on:click={() => {
											userToBan = comment;
											showBanDialog = true;
										}}
									>
										<Ban class="h-4 w-4" />
									</Button>
								</div>
							</div>
							<p class="mt-2">{comment.content}</p>
							<div class="mt-2 text-sm text-gray-500">
								{new Date(comment.created_at).toLocaleString()}
							</div>
						</div>
					{/each}
				</CardContent>
			</Card>
		</TabsContent>

		<TabsContent value="reports">
			<Card>
				<CardHeader>
					<CardTitle>Report Queue</CardTitle>
					<CardDescription>Comments reported by users</CardDescription>
					<div class="mt-4">
						<!-- svelte-ignore a11y-label-has-associated-control -->
						<label class="text-sm font-medium">
							Minimum Reports Threshold: {minReportThreshold}
						</label>
						<Slider value={[minReportThreshold]} onValueChange={(values) => (minReportThreshold = values[0])} min={1} max={20} step={1} class="mt-2" />
					</div>
				</CardHeader>
				<CardContent>
					{#each filteredReportQueue as report}
						<div class="mb-4 rounded-lg border p-4">
							<div class="flex items-center justify-between">
								<div class="flex items-center gap-2">
									<img src={report.comment.avatar} alt="" class="h-8 w-8 rounded-full" />
									<span class="font-semibold">{report.comment.author}</span>
									<Badge variant="destructive" class="ml-2">
										{report.reportCount}
										{report.reportCount === 1 ? 'report' : 'reports'}
									</Badge>
								</div>
								<div class="flex gap-2">
									<Button variant="default" size="sm" class="cursor-pointer" on:click={() => handleApprove(report.comment.id)}>
										<CheckCircle class="h-4 w-4" />
									</Button>
									<Button variant="destructive" size="sm" class="cursor-pointer" on:click={() => handleDeny(report.comment.id)}>
										<XCircle class="h-4 w-4" />
									</Button>
									<Button
										variant="destructive"
										size="sm"
										class="cursor-pointer"
										on:click={() => {
											userToBan = report.comment;
											showBanDialog = true;
										}}
									>
										<Ban class="h-4 w-4" />
									</Button>
								</div>
							</div>
							<p class="mt-2">{report.comment.content}</p>
							<div class="mt-2 flex flex-wrap gap-2">
								{#each [...new Set(report.reasons)] as reason}
									<Badge variant="destructive">
										{reason} ({report.reasons.filter((r) => r === reason).length})
									</Badge>
								{/each}
							</div>
							<div class="mt-2 text-sm text-gray-500">
								{new Date(report.created_at).toLocaleString()}
							</div>
						</div>
					{/each}
					{#if filteredReportQueue.length === 0}
						<div class="py-4 text-center text-gray-500">
							No comments with {minReportThreshold} or more reports
						</div>
					{/if}
				</CardContent>
			</Card>
		</TabsContent>
	</Tabs>

	<Dialog bind:open={showBanDialog}>
		<DialogContent>
			<DialogHeader>
				<DialogTitle>Ban User</DialogTitle>
				<DialogDescription>
					Are you sure you want to ban {userToBan?.author}? This will remove all their comments and make them unable to comment again.
				</DialogDescription>
			</DialogHeader>
			<div class="mt-4">
				<label for="banReason" class="text-sm font-medium">Ban Reason</label>
				<Input id="banReason" bind:value={banReason} placeholder="Enter reason for ban..." class="mt-2" />
			</div>
			<DialogFooter class="mt-4">
				<Button
					variant="outline"
					class="cursor-pointer"
					on:click={() => {
						showBanDialog = false;
						banReason = '';
						userToBan = null;
					}}
				>
					Cancel
				</Button>
				<Button variant="destructive" class="cursor-pointer" on:click={() => handleBan(userToBan?.user_id)}>Ban User</Button>
			</DialogFooter>
		</DialogContent>
	</Dialog>
</div>
