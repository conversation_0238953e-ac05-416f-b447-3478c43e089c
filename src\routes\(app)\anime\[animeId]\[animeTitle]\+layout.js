import { currentAnime } from '$lib/stores/animeStore';
import { browser } from '$app/environment';
import { error } from '@sveltejs/kit';

export async function load({ params, fetch }) {
  try {
    const response = await fetch(`/api/anime/${params.animeId}`);

    if (!response.ok) {
      throw error(response.status, 'Failed to fetch anime data');
    }

    const data = await response.json();

    if (browser) {
      currentAnime.set(data.anime);
    }

    return data;

  } catch (e) {
    console.error('Error in layout load function:', e);

    // If the error already has a status code, use it (preserves 404s)
    if (e.status) {
      throw error(e.status, e.body?.message || 'Failed to fetch anime data');
    }

    // Default to 500 for unexpected errors
    throw error(500, 'Failed to load anime data');
  }
}