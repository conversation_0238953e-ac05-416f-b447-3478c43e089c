import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request }) {
	try {
		const {
			episodeId,
			translatingGroup,
			playerSource,
			externalPlayerLink,
			quality = 'HD',
			audioLanguage = 'jp',
			subtitleLanguage = 'pl'
		} = await request.json();

		if (!episodeId || !translatingGroup || !playerSource || !externalPlayerLink) {
			return json({ 
				error: 'Missing required fields: episodeId, translatingGroup, playerSource, externalPlayerLink' 
			}, { status: 400 });
		}

		// First, get the episode details to copy basic information
		const { data: existingEpisode, error: fetchError } = await supabase
			.from('anime_new')
			.select('*')
			.eq('id', episodeId)
			.single();

		if (fetchError || !existingEpisode) {
			return json({ error: 'Episode not found' }, { status: 404 });
		}

		// Verify that the translating group exists in the approved list
		const { data: groupExists, error: groupError } = await supabase
			.from('translating_groups')
			.select('id')
			.eq('name', translatingGroup)
			.single();

		if (groupError || !groupExists) {
			return json({ 
				error: 'Translating group not found in approved list' 
			}, { status: 400 });
		}

		// Verify that the player source exists in the approved list
		const { data: playerExists, error: playerError } = await supabase
			.from('player_sources')
			.select('id')
			.eq('name', playerSource)
			.single();

		if (playerError || !playerExists) {
			return json({ 
				error: 'Player source not found in approved list' 
			}, { status: 400 });
		}

		// Check if this exact combination already exists
		const { data: duplicateCheck, error: duplicateError } = await supabase
			.from('anime_new')
			.select('id')
			.eq('anilist_id', existingEpisode.anilist_id)
			.eq('episode_number', existingEpisode.episode_number)
			.eq('translating_group', translatingGroup)
			.eq('external_player_link', externalPlayerLink);

		if (duplicateError) {
			console.error('Error checking for duplicates:', duplicateError);
			return json({ error: 'Failed to check for duplicates' }, { status: 500 });
		}

		if (duplicateCheck && duplicateCheck.length > 0) {
			return json({ 
				error: 'This player already exists for this episode and group' 
			}, { status: 409 });
		}

		// Create new episode entry
		const newEpisodeData = {
			anilist_id: existingEpisode.anilist_id,
			mal_id: existingEpisode.mal_id,
			episode_number: existingEpisode.episode_number,
			episode_title: existingEpisode.episode_title,
			thumbnail_link: null, // Set to null for new players
			preview_file: null, // Set to null for new players
			burst_source: null, // Only for lycoris.cafe
			primary_source: null, // Only for lycoris.cafe
			secondary_source: null, // Only for lycoris.cafe
			subtitleLinks: null,
			markerPeriods: null, // Set to null for new players
			thumbnailFile: existingEpisode.thumbnailFile,
			needs_update: false,
			date_added: new Date().toISOString(),
			translating_group: translatingGroup,
			player_source: playerSource,
			external_player_link: externalPlayerLink,
			quality: quality,
			audio_language: audioLanguage,
			subtitle_language: subtitleLanguage
		};

		const { data: newEpisode, error: insertError } = await supabase
			.from('anime_new')
			.insert([newEpisodeData])
			.select()
			.single();

		if (insertError) {
			console.error('Error inserting new episode player:', insertError);
			return json({ error: 'Failed to add new player' }, { status: 500 });
		}

		return json({
			success: true,
			episode: newEpisode
		});

	} catch (error) {
		console.error('Error adding episode player:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
}
