// src/routes/search/+page.js
export async function load({ fetch }) {
  try {
    // Include the sort parameters in the initial request
    const response = await fetch('/api/search?page=1&pageSize=12&sortField=popularity&sortDirection=desc');
    const initialData = await response.json();

    return {
      animeData: initialData.data,
      totalCount: initialData.total,
      hasMore: initialData.hasMore,
      currentPage: 1,
      filterCounts: initialData.filterCounts || {}
    };
  } catch (error) {
    console.error('Load Error:', error);
    return {
      animeData: [],
      totalCount: 0,
      hasMore: false,
      currentPage: 1,
      filterCounts: {},
      error: error.message
    };
  }
}