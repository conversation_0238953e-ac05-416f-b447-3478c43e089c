// src/routes/api/profile/progress/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY, REPORT_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
export async function POST({ request, locals }) {
  try {
    const { continue_watching } = await request.json();
    const { session } = await locals.safeGetSession();
    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }
    const { data, error } = await supabase
      .from('profiles')
      .update({
        continue_watching: continue_watching,
        updated_at: new Date().toISOString()
      })
      .eq('id', session.user.id)
      .select()
    if (error) throw error;
    return json({ success: true });
  } catch (error) {
    console.error('Error updating progress:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}