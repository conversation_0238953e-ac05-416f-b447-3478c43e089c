import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminOrMod } from '$lib/utils/roleUtils';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function DELETE({ request, locals }) {
    const { session, user } = await locals.safeGetSession();

    // Check if user is authenticated
    if (!session || !user) {
        throw error(401, 'Unauthorized');
    }

    // Check if user is an admin or moderator
    const hasAdminRole = await isAdminOrMod(user.id);

    if (!hasAdminRole) {
        throw error(403, 'Forbidden');
    }

    const { commentId } = await request.json();

    const { error: deleteError } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);

    if (deleteError) throw error(500, 'Failed to delete comment');

    return json({ success: true });
}
