// src/lib/utils/cacheUtils.js

const CACHE_PREFIX = 'lycoris_cache_';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export const cacheKeys = {
  HOME_DATA: `${CACHE_PREFIX}home_data`,
  ANILIST_WATCHING: `${CACHE_PREFIX}anilist_watching_`,
  ANILIST_LATEST: `${CACHE_PREFIX}anilist_latest_`,
  ANILIST_METADATA: `${CACHE_PREFIX}anilist_metadata_`,
  ANILIST_MEDIA: `${CACHE_PREFIX}anilist_media_`,
  ANILIST_FRIENDS: `${CACHE_PREFIX}anilist_friends_`,
  CACHE_TIMESTAMP: `${CACHE_PREFIX}timestamp`,
  MAL_METADATA: `${CACHE_PREFIX}mal_metadata_`,
  MAL_WATCHING: `${CACHE_PREFIX}mal_watching_`,
  MAL_LATEST: `${CACHE_PREFIX}mal_latest_`,
  MAL_MEDIA: `${CACHE_PREFIX}mal_media_`,
  USER_SETTINGS: `${CACHE_PREFIX}user_settings_`,
};

export function setCachedData(key, data) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    localStorage.setItem(cacheKeys.CACHE_TIMESTAMP, Date.now().toString());
  } catch (error) {
    console.warn('Error setting cache:', error);
  }
}

export function getCachedData(key) {
  try {
    const timestamp = parseInt(localStorage.getItem(cacheKeys.CACHE_TIMESTAMP));
    // console.log(Date.now() - timestamp > CACHE_DURATION)
    // console.log(Date.now() - timestamp)
    // console.log(CACHE_DURATION)
    if (!timestamp || Date.now() - timestamp > CACHE_DURATION) {
      clearCache();
      return null;
    }

    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.warn('Error getting cache:', error);
    return null;
  }
}

export function clearCache() {
  try {
    // Clear all predefined cache keys
    Object.values(cacheKeys).forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear all media cache keys that include anime IDs
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(cacheKeys.ANILIST_MEDIA) ||
          key.startsWith(cacheKeys.MAL_MEDIA) ||
          key.startsWith(cacheKeys.ANILIST_FRIENDS)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('Error clearing cache:', error);
  }
}