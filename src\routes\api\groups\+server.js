import { json } from '@sveltejs/kit';

export async function GET({ locals: { supabase } }) {
  try {
    // Fetch both translating groups and player sources
    const [translatingGroupsResponse, playerSourcesResponse] = await Promise.all([
      supabase
        .from('translating_groups')
        .select('*')
        .order('name'),
      supabase
        .from('player_sources')
        .select('*')
        .order('name')
    ]);

    if (translatingGroupsResponse.error) {
      console.error('Error fetching translating groups:', translatingGroupsResponse.error);
      return json({ error: 'Failed to fetch translating groups' }, { status: 500 });
    }

    if (playerSourcesResponse.error) {
      console.error('Error fetching player sources:', playerSourcesResponse.error);
      return json({ error: 'Failed to fetch player sources' }, { status: 500 });
    }

    return json({
      translatingGroups: translatingGroupsResponse.data || [],
      playerSources: playerSourcesResponse.data || []
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
