<!-- components/ReportModal.svelte -->
<script>
	import { Button } from '$lib/components/ui/button';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Dialog from '$lib/components/ui/dialog';
	import { X } from 'lucide-svelte';

	export let close = () => {};
	export let onSubmit;
	export let commentId;

	let selectedReason = '';
	let customReason = '';

	const reportReasons = [
		{ id: 'spam', label: 'Spam' },
		{ id: 'harassment', label: 'Nękanie lub zastraszanie' },
		{ id: 'hate_speech', label: '<PERSON><PERSON> niena<PERSON>' },
		{ id: 'inappropriate', label: 'Nieodpowiednia treść' },
		{ id: 'spoiler', label: 'Niezaznaczony spoiler' },
		{ id: 'misinformation', label: 'Dezinformacja' },
		{ id: 'other', label: 'Inny powód' }
	];

	async function handleSubmit() {
		const reason = selectedReason === 'other' ? customReason : reportReasons.find((r) => r.id === selectedReason)?.label;

		if (!reason) return;

		await onSubmit(commentId, reason);
		close();
	}
</script>

<Dialog.Root open={true} onOpenChange={close}>
	<Dialog.Portal>
		<Dialog.Overlay class="bg-background/80 fixed inset-0 backdrop-blur-xs" />
		<Dialog.Content class="bg-background fixed top-1/2 left-1/2 w-full max-w-lg -translate-x-1/2 -translate-y-1/2 rounded-lg border p-6 shadow-lg">
			<Dialog.Header class="flex items-center justify-between">
				<Dialog.Title class="text-lg font-semibold">Zgłoś komentarz</Dialog.Title>
			</Dialog.Header>

			<div class="mt-4 space-y-4">
				<div class="space-y-2">
					{#each reportReasons as reason}
						<label class="flex items-center space-x-2">
							<input type="radio" name="reason" value={reason.id} bind:group={selectedReason} class="h-4 w-4 cursor-pointer rounded border-gray-300" />
							<span class="text-sm">{reason.label}</span>
						</label>
					{/each}
				</div>

				{#if selectedReason === 'other'}
					<Textarea bind:value={customReason} placeholder="Opisz powód zgłoszenia..." class="min-h-[100px]" />
				{/if}
			</div>

			<Dialog.Footer class="mt-6 flex justify-end gap-2">
				<Button variant="outline" class="cursor-pointer" on:click={close}>Anuluj</Button>
				<Button variant="destructive" class="cursor-pointer" on:click={handleSubmit} disabled={!selectedReason || (selectedReason === 'other' && !customReason.trim())}>Zgłoś</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Portal>
</Dialog.Root>
