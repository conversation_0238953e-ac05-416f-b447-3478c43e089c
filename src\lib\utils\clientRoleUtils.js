/**
 * Client-side utility functions for role-based authorization
 * These functions work with the user's profile data that's already loaded in the client
 */
import { userStore } from '$lib/stores/userLogin';
import { get } from 'svelte/store';

/**
 * Get the user's role from the user store
 * @returns {string|null} - The user's role or null if not authenticated
 */
export function getUserRole() {
  const user = get(userStore);
  console.log('User in getUserRole:', user);
  console.log('User metadata:', user?.user_metadata);
  console.log('User profile:', user?.user_metadata?.profile);
  console.log('User role:', user?.user_metadata?.profile?.role);

  if (!user || user.role !== 'authenticated') {
    console.log('User not authenticated or not found');
    return null;
  }

  const role = user.user_metadata?.profile?.role || null;
  console.log('Returning role:', role);
  return role;
}

/**
 * Check if a user has a specific role
 * @param {string} role - The role to check for ('admin', 'mod', or 'user')
 * @returns {boolean} - True if the user has the specified role, false otherwise
 */
export function hasRole(role) {
  const userRole = getUserRole();
  return userRole === role;
}

/**
 * Check if a user is an admin
 * @returns {boolean} - True if the user is an admin, false otherwise
 */
export function isAdmin() {
  console.log('Checking if user is admin');
  const user = get(userStore);
  console.log('User in isAdmin check:', user?.id);

  // Direct check for admin role in user metadata
  const isAdminFromMetadata = user?.user_metadata?.profile?.role === 'admin';
  console.log('Admin check from metadata:', isAdminFromMetadata);

  // Check using hasRole function
  const isAdminFromHasRole = hasRole('admin');
  console.log('Admin check from hasRole:', isAdminFromHasRole);

  return isAdminFromMetadata || isAdminFromHasRole;
}

/**
 * Check if a user is a moderator
 * @returns {boolean} - True if the user is a moderator, false otherwise
 */
export function isMod() {
  return hasRole('mod');
}

/**
 * Check if a user is an admin or moderator
 * @returns {boolean} - True if the user is an admin or moderator, false otherwise
 */
export function isAdminOrMod() {
  const userRole = getUserRole();
  return userRole === 'admin' || userRole === 'mod';
}
