<script>
	import { page } from '$app/stores';

	$: errorType = determineErrorType($page.status);
	$: errorDetails = $page.error?.details || {};
	$: customMessage = $page.error?.message || '';
	$: timestamp = $page.error?.timestamp;
	$: stackTrace = $page.error?.stackTrace;
	$: originalError = errorDetails.originalError || {};

	function determineErrorType(status) {
		if (status === 404) return 'notFound';
		if (status === 403) return 'forbidden';
		if (status === 429) return 'rateLimit';
		if (status >= 500) return 'serverError';
		return 'generalError';
	}

	$: errorTitle = {
		notFound: '404',
		forbidden: '403',
		rateLimit: '429',
		serverError: '500',
		generalError: 'Error'
	}[errorType];

	$: errorMessage = {
		notFound: customMessage || 'Page not found',
		forbidden: customMessage || 'Access forbidden',
		rateLimit: customMessage || '<PERSON>byt wiele zapytań',
		serverError: customMessage || 'Server error',
		generalError: customMessage || 'Something went wrong'
	}[errorType];

	let showTechnicalDetails = false;
</script>

<section class="flex min-h-screen items-center bg-gray-900">
	<div class="mx-auto max-w-(--breakpoint-xl) px-4 py-8 lg:px-6 lg:py-16">
		<div class="mx-auto max-w-(--breakpoint-lg) text-center">
			<h1 class="text-primary-500 mb-4 text-7xl font-extrabold tracking-tight lg:text-9xl">:(</h1>
			<p class="mb-4 text-3xl font-bold tracking-tight text-white md:text-4xl">
				{errorMessage}
			</p>

			{#if errorType === 'notFound'}
				<p class="mb-4 text-lg font-light text-gray-400">Przepraszamy, nie mogliśmy znaleźć strony, której szukasz.</p>
			{:else if errorType === 'forbidden'}
				<p class="mb-4 text-lg font-light text-gray-400">Przepraszamy, nie masz uprawnień do dostępu do tej strony.</p>
			{:else if errorType === 'rateLimit'}
				<p class="mb-4 text-lg font-light text-gray-400">Przepraszamy, wykonałeś zbyt wiele zapytań w krótkim czasie.</p>
				<p class="mb-4 text-lg font-light text-gray-400">Odczekaj chwilę i spróbuj ponownie. Jeśli problem będzie się powtarzał, skontaktuj się z nami.</p>
			{:else if errorType === 'serverError'}
				<p class="mb-4 text-lg font-light text-gray-400">Przepraszamy, wystąpił błąd serwera. Spróbuj odświeżyć stronę.</p>
				<p class="mb-4 text-lg font-light text-gray-400">
					Daj nam znać na naszym <a href="https://discord.gg/lycoriscafe" class="text-red-400 hover:underline" target="_blank" rel="noopener noreferrer">Discordzie</a>. Wyślij wiadomość prywatną do
					@4lajf z szczegółami technicznymi poniżej. Oraz na jakiej stronie napotkany został błąd.
				</p>
			{:else}
				<p class="mb-4 text-lg font-light text-gray-400">Przepraszamy, wystąpił nieoczekiwany błąd. Spróbuj odświeżyć stronę lub wróć później.</p>
				<p class="mb-4 text-lg font-light text-gray-400">
					Daj nam znać na naszym <a href="https://discord.gg/lycoriscafe" class="text-red-400 hover:underline" target="_blank" rel="noopener noreferrer"> Discordzie </a>. Wyślij wiadomość prywatną
					do @4lajf z szczegółami technicznymi poniżej.
				</p>
			{/if}

			<div class="mt-8">
				<button on:click={() => (showTechnicalDetails = !showTechnicalDetails)} class="mb-4 cursor-pointer text-red-400 hover:underline">
					{showTechnicalDetails ? 'Ukryj' : 'Pokaż'} Szczegóły Techniczne
				</button>

				{#if showTechnicalDetails}
					<div class="mt-4 rounded-lg bg-gray-800 p-4 text-left">
						<p class="text-sm text-gray-300">Kod Błędu: {$page.status}</p>
						<p class="text-sm text-gray-300">Komunikat Błędu: {customMessage}</p>

						{#if timestamp}
							<p class="text-sm text-gray-300">Czas: {timestamp}</p>
						{/if}

						{#if errorDetails.errorType}
							<p class="text-sm text-gray-300">Typ Błędu: {errorDetails.errorType}</p>
						{/if}

						{#if errorDetails.details}
							<div class="mt-2">
								<p class="text-sm font-semibold text-gray-200">Szczegóły Błędu Bazy Danych:</p>
								<pre class="mt-1 overflow-x-auto rounded bg-gray-700 p-2 text-xs text-gray-200">
																	{JSON.stringify(errorDetails.details, null, 2)}
															</pre>
							</div>
						{/if}

						{#if errorDetails.searchParams}
							<div class="mt-2">
								<p class="text-sm font-semibold text-gray-200">Parametry Wyszukiwania:</p>
								<pre class="mt-1 overflow-x-auto rounded bg-gray-700 p-2 text-xs text-gray-200">
																	{JSON.stringify(errorDetails.searchParams, null, 2)}
															</pre>
							</div>
						{/if}

						{#if originalError && Object.keys(originalError).length > 0}
							<div class="mt-2">
								<p class="text-sm font-semibold text-gray-200">Oryginalny Błąd:</p>
								<pre class="mt-1 overflow-x-auto rounded bg-gray-700 p-2 text-xs text-gray-200">
																	{JSON.stringify(originalError, null, 2)}
															</pre>
							</div>
						{/if}
					</div>
				{/if}
			</div>

			<p class="mb-4 text-lg font-light text-gray-500">
				Możesz sprawdzić status strony na <a href="https://status.lycoris.cafe" class="text-red-400 hover:underline" target="_blank" rel="noopener noreferrer"> status.lycoris.cafe </a>
			</p>

			<a href="/" class="hover:bg-primary-800 focus:ring-primary-900 my-4 inline-flex rounded-lg bg-red-400 px-5 py-2.5 text-center text-sm font-medium text-white focus:ring-4 focus:outline-hidden">
				Wróć do Strony Głównej
			</a>

			<a
				href="https://discord.gg/lycoriscafe"
				class="hover:bg-primary-800 focus:ring-primary-900 my-4 inline-flex rounded-lg bg-red-400 px-5 py-2.5 text-center text-sm font-medium text-white focus:ring-4 focus:outline-hidden"
				target="_blank"
			>
				Zgłoś Błąd na Discordzie
			</a>
		</div>
	</div>
</section>
