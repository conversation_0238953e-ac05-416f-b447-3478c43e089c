import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET() {
	try {
		const { data: groups, error } = await supabase
			.from('translating_groups')
			.select('name, link')
			.order('name');

		if (error) {
			console.error('Error fetching translating groups:', error);
			return json({ error: 'Failed to fetch translating groups' }, { status: 500 });
		}

		return json(groups || []);
	} catch (error) {
		console.error('Error in translating groups API:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
}
