export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const encodedLink = searchParams.get('link');

        // Validate input parameters
        if (!encodedLink) {
            return createErrorResponse(400, 'Missing link parameter', {
                errorType: 'MISSING_PARAMETER',
                context: 'parameter validation',
                location: 'GET handler',
                action: 'parameter validation',
                trigger: 'missing link parameter',
                parameter: 'link',
                userMessage: 'Could not process the request due to missing link parameter.',
                requestInfo: {
                    parameterReceived: Object.fromEntries(searchParams)
                }
            });
        }

        // Decode the link
        const decodedLink = decodeVideoLink(encodedLink);
        if (!decodedLink) {
            return createErrorResponse(400, 'Invalid encoded link', {
                errorType: 'DECODE_ERROR',
                context: 'link decoding',
                location: 'GET handler',
                action: 'decoding link',
                trigger: 'decode failure',
                userMessage: 'Could not decode the provided link.',
                requestInfo: {
                    encodedLink,
                    timestamp: new Date().toISOString()
                }
            });
        }

        try {
            const videoLink = await fetchVideoLink(decodedLink);
            const encodedResponse = encodeVideoLink(videoLink);

            if (!encodedResponse) {
                throw new Error('Failed to encode response URL');
            }

            return new Response(encodedResponse, {
                headers: {
                    'Cache-Control': 'public, max-age=5, must-revalidate',
                    'Content-Type': 'text/plain'
                },
                status: 200
            });
        } catch (error) {
            console.log(error);
            return createErrorResponse(500, 'Failed to fetch video', {
                errorType: 'VIDEO_FETCH_ERROR',
                context: 'video retrieval',
                location: 'GET handler',
                action: 'fetching video',
                trigger: 'fetch failure',
                userMessage: 'Could not fetch the video. Please try again later.',
                requestInfo: {
                    encodedLink,
                    decodedLink,
                    timestamp: new Date().toISOString()
                },
                error: {
                    message: error.message,
                    stack: error.stack
                }
            });
        }
    } catch (err) {
        console.error('Server error:', err);
        return createErrorResponse(500, 'Internal server error', {
            errorType: 'UNEXPECTED_ERROR',
            context: 'request handling',
            location: 'GET handler',
            action: 'processing request',
            trigger: 'unexpected error',
            userMessage: 'An unexpected error occurred. Please try again later.',
            requestInfo: {
                timestamp: new Date().toISOString()
            },
            error: {
                message: err.message,
                stack: err.stack
            }
        });
    }
}

function createErrorResponse(status, message, details = {}) {
    return new Response(
        JSON.stringify({
            status,
            message,
            timestamp: new Date().toISOString(),
            errorType: details.errorType || 'SERVER_ERROR',
            context: details.context || 'server',
            details: {
                ...details,
                userMessage: details.userMessage || message,
                requestInfo: details.requestInfo || {},
                errorContext: {
                    location: details.location || 'unknown',
                    action: details.action || 'unknown',
                    trigger: details.trigger || 'unknown'
                }
            }
        }), {
        status,
        headers: {
            'Content-Type': 'application/json'
        }
    });
}

function encodeVideoLink(url) {
    if (!url) return null;

    try {
        // Base64 encode
        const base64 = btoa(url);

        // Apply scrambling
        const encoded = base64
            .split('')
            .map((char) => {
                const code = char.charCodeAt(0);
                return String.fromCharCode(code + 7); // Shift forward
            })
            .reverse() // Reverse
            .join('');

        // Add signature
        return encoded + 'LC';
    } catch (error) {
        console.error('Error encoding URL:', error);
        return null;
    }
}

function decodeVideoLink(encodedUrl) {
    if (!encodedUrl) return null;

    // Check for our signature
    if (!encodedUrl.endsWith('LC')) {
        return encodedUrl;
    }

    // Remove signature
    encodedUrl = encodedUrl.slice(0, -2);

    try {
        // Reverse the scrambling
        const decoded = encodedUrl
            .split('')
            .reverse() // Reverse back
            .map((char) => {
                const code = char.charCodeAt(0);
                return String.fromCharCode(code - 7); // Shift back
            })
            .join('');

        // Decode base64
        return atob(decoded);
    } catch (error) {
        console.error('Error decoding URL:', error);
        return null;
    }
}

async function fetchVideoLink(initialUrl) {
    try {
        // Validate URL format
        if (!initialUrl.startsWith('https://od.lk/')) {
            return initialUrl;
        }

        // Make request with redirect following disabled
        const response = await fetch(initialUrl, {
            redirect: 'manual',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });

        // If we get a redirect, follow it
        if (response.status === 302 || response.status === 301) {
            const finalUrl = response.headers.get('location');

            // Validate the redirect URL
            if (!finalUrl || !finalUrl.startsWith('http')) {
                throw new Error('Invalid redirect URL received');
            }

            // Make one more request to verify the final URL is accessible
            const finalResponse = await fetch(finalUrl, {
                method: 'HEAD',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            });

            if (!finalResponse.ok) {
                throw new Error(`Final URL returned status ${finalResponse.status}`);
            }

            return finalUrl;
        }

        // If no redirect, check if the original URL is valid
        if (!response.ok) {
            throw new Error(`OpenDrive URL returned status ${response.status}`);
        }

        return initialUrl;
    } catch (error) {
        throw new Error(`Failed to resolve video URL: ${error.message}`);
    }
}