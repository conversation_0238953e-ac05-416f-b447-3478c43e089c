// src/routes/api/mal/proxy/update-entry/+server.js
import { json } from '@sveltejs/kit';

export async function POST({ request }) {
  const data = await request.json();
  const {
    animeId,
    token,
    status,
    score,
    numWatchedEpisodes,
    numTimesRewatched,
    comments,
    startDate,
    finishDate
  } = data;

  if (!animeId || !token) {
    return json({ error: 'Missing required parameters' }, { status: 400 });
  }

  try {
    // Prepare the form data for MAL API - use only the exact field names MAL expects
    const formData = new URLSearchParams();

    // Add only fields that are provided and with correct MAL API field names
    if (status) formData.append('status', status);
    if (score !== undefined) formData.append('score', score);
    if (numWatchedEpisodes !== undefined) formData.append('num_watched_episodes', numWatchedEpisodes);
    if (numTimesRewatched !== undefined) formData.append('num_times_rewatched', numTimesRewatched);
    if (comments) formData.append('comments', comments);
    if (startDate) formData.append('start_date', startDate);
    if (finishDate) formData.append('finish_date', finishDate);

    // Make the request to MAL API through the server
    const response = await fetch(`https://api.myanimelist.net/v2/anime/${animeId}/my_list_status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || `MAL API error: ${response.status}`);
      } catch (e) {
        throw new Error(`MAL API error: ${response.status} - ${errorText || 'No details available'}`);
      }
    }

    const responseData = await response.json();
    return json(responseData);
  } catch (error) {
    console.error('Error updating entry on MAL:', error);
    return json({ error: error.message }, { status: 500 });
  }
}