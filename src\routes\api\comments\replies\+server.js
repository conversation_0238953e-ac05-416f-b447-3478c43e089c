import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function getAllReplies(parentIds, userId = null) {
  if (!parentIds || !parentIds.length) return [];

  // Fetch replies with reaction counts
  const { data: replies, error } = await supabase
    .from('comments')
    .select(`
      *,
      anime:anime_metadata(
        anilist_id,
        romaji_title
      )
    `)
    .in('parent_id', parentIds);

  if (error) throw error;
  if (!replies.length) return [];

  // Get reaction counts for all replies
  const replyIds = replies.map(reply => reply.id);

  // Fetch like counts
  const { data: likeCounts, error: likeError } = await supabase
    .from('comment_reactions')
    .select('comment_id, reaction_type')
    .in('comment_id', replyIds)
    .eq('reaction_type', 'like');

  if (likeError) throw likeError;

  // Fetch dislike counts
  const { data: dislikeCounts, error: dislikeError } = await supabase
    .from('comment_reactions')
    .select('comment_id, reaction_type')
    .in('comment_id', replyIds)
    .eq('reaction_type', 'dislike');

  if (dislikeError) throw dislikeError;

  // Create maps for like and dislike counts
  const likeCountMap = {};
  const dislikeCountMap = {};

  // Count likes for each comment
  likeCounts.forEach(like => {
    if (!likeCountMap[like.comment_id]) {
      likeCountMap[like.comment_id] = 0;
    }
    likeCountMap[like.comment_id]++;
  });

  // Count dislikes for each comment
  dislikeCounts.forEach(dislike => {
    if (!dislikeCountMap[dislike.comment_id]) {
      dislikeCountMap[dislike.comment_id] = 0;
    }
    dislikeCountMap[dislike.comment_id]++;
  });

  // Get user reactions for replies if user is logged in
  let repliesWithReactions = [...replies];
  if (userId && replies.length > 0) {
    const { data: userReactions, error: reactionsError } = await supabase
      .from('comment_reactions')
      .select('comment_id, reaction_type')
      .eq('user_id', userId)
      .in('comment_id', replyIds);

    if (reactionsError) throw reactionsError;

    // Create a map of comment_id to reactions
    const reactionsMap = {};
    if (userReactions) {
      userReactions.forEach(reaction => {
        if (!reactionsMap[reaction.comment_id]) {
          reactionsMap[reaction.comment_id] = [];
        }
        reactionsMap[reaction.comment_id].push(reaction.reaction_type);
      });
    }

    // Add user_reactions, likes, and dislikes to each reply
    repliesWithReactions = replies.map(reply => ({
      ...reply,
      likes: likeCountMap[reply.id] || 0,
      dislikes: dislikeCountMap[reply.id] || 0,
      user_reactions: reactionsMap[reply.id] || []
    }));
  } else {
    // If no user is logged in, just add the like and dislike counts
    repliesWithReactions = replies.map(reply => ({
      ...reply,
      likes: likeCountMap[reply.id] || 0,
      dislikes: dislikeCountMap[reply.id] || 0,
      user_reactions: []
    }));
  }

  // Recursively get deeper replies
  const deeperReplies = await getAllReplies(replies.map(r => r.id), userId);

  // Make sure we preserve the likes, dislikes, and user_reactions when adding anime_title
  return [...repliesWithReactions, ...deeperReplies].map(reply => ({
    ...reply,
    anime_title: reply.anime?.romaji_title,
    likes: reply.likes || 0,
    dislikes: reply.dislikes || 0,
    user_reactions: reply.user_reactions || []
  }));
}

export async function POST({ request, locals }) {
  const { parentIds } = await request.json();
  let userId = null;

  // Get current user if logged in
  const { session, user } = await locals.safeGetSession();
  if (session && user) {
    userId = user.id;
  }

  if (!parentIds || !parentIds.length) {
    return json({ replies: [] });
  }

  try {
    const replies = await getAllReplies(parentIds, userId);
    return json({ replies });
  } catch (error) {
    console.error('Error fetching replies:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}