//src/routes/api/comments/delete/+server.js
import { json, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function DELETE({ request, locals }) {
  try {
    // Verify user is authenticated
    const { session, user } = await locals.safeGetSession();
    if (!session || !user) {
      throw error(401, 'Must be logged in to delete comments');
    }

    const { commentId } = await request.json();
    if (!commentId) {
      throw error(400, 'Comment ID is required');
    }

    // First verify the comment exists and belongs to the user
    const { data: comment, error: commentError } = await supabase
      .from('comments')
      .select('user_id, deleted_at')
      .eq('id', commentId)
      .single();

    if (commentError) {
      throw error(404, 'Comment not found');
    }

    // Check if comment is already deleted
    if (comment.deleted_at) {
      throw error(400, 'Comment is already deleted');
    }

    // Verify user owns the comment
    if (comment.user_id !== user.id) {
      throw error(403, 'Not authorized to delete this comment');
    }

    // Check for replies that aren't deleted
    const { data: replies, error: repliesError } = await supabase
      .from('comments')
      .select('id')
      .eq('parent_id', commentId)
      .is('deleted_at', null);

    if (repliesError) throw repliesError;

    // If no replies exist, permanently delete
    if (!replies || replies.length === 0) {
      // Delete reactions first
      const { error: reactionsError } = await supabase
        .from('comment_reactions')
        .delete()
        .eq('comment_id', commentId);

      if (reactionsError) throw reactionsError;

      // Delete reports
      const { error: reportsError } = await supabase
        .from('comment_reports')
        .delete()
        .eq('comment_id', commentId);

      if (reportsError) throw reportsError;

      // Finally delete the comment
      const { error: deleteError } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', user.id); // Extra safety check

      if (deleteError) throw deleteError;

      return json({ deleted: true, permanent: true });
    }

    // If replies exist, soft delete
    const { error: updateError } = await supabase
      .from('comments')
      .update({
        deleted_at: new Date().toISOString(),
        author: '[deleted]',
        content: '[redacted]',
        likes: 0,
        dislikes: 0,
        user_id: null,
        avatar: '/default-avatar.svg'
      })
      .eq('id', commentId)
      .eq('user_id', user.id); // Extra safety check

    if (updateError) throw updateError;

    // Clean up reactions for soft-deleted comment
    await supabase
      .from('comment_reactions')
      .delete()
      .eq('comment_id', commentId);

    return json({ deleted: true, permanent: false });
  } catch (err) {
    console.error('Error deleting comment:', err);
    throw error(err.status || 500, err.message || 'Error deleting comment');
  }
}