//src/routes/api/comments/dislike/+server.js
import { json, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
export async function POST({ request, locals }) {
  try {
    const { session, user } = await locals.safeGetSession();
    if (!session || !user) {
      throw error(401, 'Must be logged in to dislike comments');
    }

    const { commentId } = await request.json();

    const { data: existing, error: checkError } = await supabase
      .from('comment_reactions')
      .select('reaction_type')
      .eq('comment_id', commentId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    const { data, error: transactionError } = await supabase.rpc('toggle_dislike', {
      p_comment_id: commentId,
      p_user_id: user.id,
      had_previous_reaction: !!existing,
      previous_reaction_type: existing?.reaction_type || null
    });

    if (transactionError) throw transactionError;

    return json({ likes: data.likes, dislikes: data.dislikes });
  } catch (err) {
    console.error('Error disliking comment:', err);
    throw error(err.status || 500, err.message || 'Error processing reaction');
  }
}