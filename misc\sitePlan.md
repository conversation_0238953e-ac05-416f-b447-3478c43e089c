TODO: limit vertical view to 15, horizontal to unlimited (make it load infinietly from database)
???: make the navbar disapper when mouse outside of a screen
If user is watching on an iframe than make a notification that says "Zob<PERSON>z pełną wersję naszej strony! <PERSON><PERSON><PERSON><PERSON> odcinków, oce<PERSON> zna<PERSON>, zapisywanie postępu i więcej. Kliknij tutaj =).

# TODO
- add search filters in URL bar
- karty chowaja sie pod kontener na przyblizeniu
- reply to comments

## Jak jesteś zalogowany:

- Nowe wydania - poziome
- Kontynuuj oglądanie - rzeczy które masz w watchingu pionowe
- Ju<PERSON> niedługo! - lista odcinkow ktore maja wyjsc juz za moment, sortowane po dacie wyjscia - poziome
- Najnowsze komenatarze - poziome
- Popularne w tym sezonie - lista rzeczy w tym sezonie sortowania po popularności - pionowe

### --- To ju<PERSON> troch<PERSON> zapych<PERSON>, da<PERSON><PERSON> zna<PERSON> czy warto robić takie sekcje na stronie ---

- Najbardziej popularne - lista bajek sortowana po popularności - pionowe
- Romans - lista romansów sortowana po popularności - pionowe
- Akcja - pionowe
- Przygoda - pionowe
- Fantasy - pionowe
- Komedia - pionowe
  i tak dalej... - pionowe

## Jak nie jesteś zalogownay

- Nowe wydania - poziome
- Popularne w tym sezonie - lista rzeczy w tym sezonie sortowania po popularności
- Już niedługo! - lista odcinkow ktore maja wyjsc juz za moment, sortowane po dacie wyjscia - pionowe
- Najnowsze komenatarze - poziome
- Najbardziej popularne - lista bajek sortowana po popularności - pionowe

### --- I w sumie to nie ma co więcej robić oprócz dodawać fillery xD ---

- Romans - lista romansów sortowana po popularności - pionowe
- Akcja - pionowe
- Przygoda - pionowe
- Fantasy - pionowe
- Komedia - pionowe
  i tak dalej... - pionowe

Miałem plan by dodac jeszcze

- Byś to już dokończył :( - Rzeczy z paused sortowane po popularności - pionowe
  nie wiem czy jest sens jak jest kontynuuj ogląanie (które w zamierzeniu ma brać tylko z watching ale still)
