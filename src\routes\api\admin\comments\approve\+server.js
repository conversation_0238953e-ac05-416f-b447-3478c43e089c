// src/routes/api/admin/comments/approve/+server.js
import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminOrMod } from '$lib/utils/roleUtils';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request, locals }) {
  const { session, user } = await locals.safeGetSession();

  // Check if user is authenticated
  if (!session || !user) {
    throw error(401, 'Unauthorized');
  }

  // Check if user is an admin or moderator
  const hasAdminRole = await isAdminOrMod(user.id);

  if (!hasAdminRole) {
    throw error(403, 'Forbidden');
  }

  const { commentId } = await request.json();

  const { error: updateError } = await supabase
    .from('comments')
    .update({
      is_spam: false,
      is_pending_review: false,
      reviewed_at: new Date().toISOString(),
      reviewed_by: user.id
    })
    .eq('id', commentId);

  if (updateError) throw error(500, 'Failed to approve comment');

  await supabase
    .from('comment_reports')
    .update({
      status: 'resolved',
      resolved_at: new Date().toISOString(),
      resolver_id: user.id,
      resolution_notes: 'Comment approved by admin'
    })
    .eq('comment_id', commentId);

  return json({ success: true });
}
