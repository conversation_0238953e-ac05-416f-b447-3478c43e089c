import Ably from 'ably';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET(params) {
	let roomCode = params.url.searchParams.get('roomCode');
	let connectionId = params.url.searchParams.get('id'); //Only not-null when someone leaves the channel
	await setChannelUsers(roomCode, connectionId);
	if (connectionId) {
		deleteInactiveRooms();
	}
	return new Response(JSON.stringify({ status: '200' }));
}

async function setChannelUsers(roomCode, connectionId) {
	const ablyKey = ABLY_ADMIN_API_KEY;
	const client = new Ably.Rest(ablyKey);
	const channel = client.channels.get(roomCode);

	try {
		// Get presence information
		let presenceMembers = await channel.presence.get();
		presenceMembers = presenceMembers.items;
		let membersArray = presenceMembers.map((member) => member.clientId);

		if (connectionId) {
			membersArray = membersArray.filter((item) => item !== connectionId);
		}

		// Update Supabase
		const { data, error } = await supabase
			.from('rooms')
			.update({ users: { members: membersArray } })
			.eq('room_code', roomCode);

		if (error) {
			console.error('Error updating Supabase:', error);
		}
	} catch (error) {
		console.error('Error in setChannelUsers:', error);
	}
}

async function deleteInactiveRooms() {
	const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
	const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

	const { data, error } = await supabase
		.from('rooms')
		.delete()
		.or(`created_at.lt.${oneDayAgo},users->members.eq.[],users->members.eq.["updating..."],users.is.null`)
		.lt('created_at', fiveMinutesAgo);
	console.log('Room cleanup: ', data, error);
	if (error) {
		console.error('Error deleting old records:', error);
	} else {
		// console.log('Successfully deleted inactive rooms');
	}
}