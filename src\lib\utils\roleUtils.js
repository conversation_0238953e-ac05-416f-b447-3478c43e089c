/**
 * Utility functions for role-based authorization
 */
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client with service role key
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

/**
 * Check if a user has a specific role
 * @param {string} userId - The user ID to check
 * @param {string} role - The role to check for ('admin', 'mod', or 'user')
 * @returns {Promise<boolean>} - True if the user has the specified role, false otherwise
 */
export async function hasRole(userId, role) {
  if (!userId) return false;

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking user role:', error);
      return false;
    }

    return data?.role === role;
  } catch (error) {
    console.error('Error in hasRole function:', error);
    return false;
  }
}

/**
 * Check if a user is an admin
 * @param {string} userId - The user ID to check
 * @returns {Promise<boolean>} - True if the user is an admin, false otherwise
 */
export async function isAdminByRole(userId) {
  return await hasRole(userId, 'admin');
}

/**
 * Check if a user is a moderator
 * @param {string} userId - The user ID to check
 * @returns {Promise<boolean>} - True if the user is a moderator, false otherwise
 */
export async function isModByRole(userId) {
  return await hasRole(userId, 'mod');
}

/**
 * Check if a user is an admin or moderator
 * @param {string} userId - The user ID to check
 * @returns {Promise<boolean>} - True if the user is an admin or moderator, false otherwise
 */
export async function isAdminOrMod(userId) {
  if (!userId) return false;

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking user role:', error);
      return false;
    }

    return data?.role === 'admin' || data?.role === 'mod';
  } catch (error) {
    console.error('Error in isAdminOrMod function:', error);
    return false;
  }
}

/**
 * Check if a user is an admin
 * @param {string} userId - The user ID to check
 * @returns {Promise<boolean>} - True if the user is an admin, false otherwise
 */
export async function isAdmin(userId) {
  return await isAdminByRole(userId);
}
