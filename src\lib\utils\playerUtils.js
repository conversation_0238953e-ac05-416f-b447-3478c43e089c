/**
 * Utility functions for handling video players
 */

/**
 * Get video URL from a player
 * @param {string} playerType - The type of player (e.g., 'vk', 'cda', etc.)
 * @param {string} embedUrl - The embed URL for the player
 * @returns {Promise<Object>} The response from the player API
 */
export async function getVideoUrl(playerType, embedUrl) {
  try {
    const response = await fetch(`/api/player?player=${encodeURIComponent(playerType)}&url=${encodeURIComponent(embedUrl)}`);

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 429) {
        throw new Error('Zbyt wiele zapytań do serwera. Odczekaj chwilę i spróbuj ponownie.');
      }
      throw new Error(errorData.message || `Failed to get video URL: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error getting video URL for ${playerType}:`, error);
    throw error;
  }
}

/**
 * Process a group's players to get video URLs
 * @param {Object} group - The group object containing players
 * @returns {Promise<Object>} Object with processed player URLs
 */
export async function processGroupPlayers(group) {
  if (!group || !group.players || !Array.isArray(group.players)) {
    throw new Error('Invalid group data');
  }

  const results = {};

  for (const player of group.players) {
    const playerType = Object.keys(player)[0];
    const embedUrl = player[playerType];

    try {
      const videoData = await getVideoUrl(playerType, embedUrl);
      results[playerType] = videoData;
    } catch (error) {
      console.error(`Error processing ${playerType} player:`, error);
      results[playerType] = { error: error.message, originalUrl: embedUrl };
    }
  }

  return results;
}

/**
 * Get the best available player from processed group players
 * @param {Object} processedPlayers - The processed player URLs
 * @returns {Object|null} The best player data or null if none available
 */
export function getBestPlayer(processedPlayers) {
  // Priority order for players (customize as needed)
  const playerPriority = ['vk', 'gdrive', 'dailymotion', 'cda', 'okru', 'rumble', 'sibnet'];

  for (const playerType of playerPriority) {
    if (processedPlayers[playerType] && !processedPlayers[playerType].error) {
      return {
        type: playerType,
        ...processedPlayers[playerType]
      };
    }
  }

  // If no priority players are available, return the first working one
  for (const playerType in processedPlayers) {
    if (!processedPlayers[playerType].error) {
      return {
        type: playerType,
        ...processedPlayers[playerType]
      };
    }
  }

  return null;
}
