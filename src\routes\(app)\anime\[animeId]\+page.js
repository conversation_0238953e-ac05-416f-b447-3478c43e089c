// src/routes/anime/[animeId]/+page.js
import { redirect } from '@sveltejs/kit';
import { generateAnimeUrl } from '$lib/myUtils';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export const load = async ({ params }) => {
  try {
    // Fetch anime metadata to get the title for proper URL generation
    const { data: anime, error } = await supabase
      .from('anime_metadata')
      .select('anilist_id, romaji_title')
      .eq('anilist_id', params.animeId)
      .single();

    if (error || !anime) {
      // If anime not found, redirect to home
      throw redirect(307, '/');
    }

    // Generate proper URL with title
    throw redirect(307, generateAnimeUrl({
      anilist_id: anime.anilist_id,
      title: anime.romaji_title
    }));
  } catch (e) {
    // If there's any error, redirect to home
    throw redirect(307, '/');
  }
};