import { error } from '@sveltejs/kit';

/**
 * DEPRECATED: This server-side API is deprecated and will be removed in a future version.
 * Please use the client-side API function from '$lib/utils/api-clients.js' instead.
 *
 * Search for anime on Jikan API (unofficial MyAnimeList API)
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Search results array
 */
async function searchJikan(query) {
  try {
    // Use the Jikan v4 API - get 10 results
    const response = await fetch(`https://api.jikan.moe/v4/anime?q=${encodeURIComponent(query)}&limit=10`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Return all results
    if (data.data && data.data.length > 0) {
      return data.data;
    }

    return null;
  } catch (error) {
    console.error('Error searching Jikan:', error);
    throw error;
  }
}

export async function POST({ request }) {
  try {
    console.warn('DEPRECATED: The server-side Jikan search API is deprecated. Please use the client-side API function from "$lib/utils/api-clients.js" instead.');

    const data = await request.json();
    const { title } = data;

    if (!title) {
      throw error(400, 'Missing title parameter');
    }

    const result = await searchJikan(title);

    if (!result) {
      return new Response(JSON.stringify({ error: 'No results found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    return new Response(JSON.stringify(result), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=3600' // Cache for 1 hour
      }
    });
  } catch (err) {
    console.error('Error in Jikan search API:', err);
    throw error(500, err.message || 'Failed to search Jikan');
  }
}
