<script>
    import { createEventDispatcher } from 'svelte';
    import * as Dialog from '$lib/components/ui/dialog';
    import { Button } from '$lib/components/ui/button';
    import ShindenExportModal from './ShindenExportModal.svelte';
    import AnimezoneExportModal from './AnimezoneExportModal.svelte';
    import { Info, HelpCircle, UserPlus } from 'lucide-svelte';

    // Eager loading to make the modal appear faster

    export let open = false;
    export let onClose = () => {};

    const dispatch = createEventDispatcher();

    let showShindenExportModal = false;
    let showAnimezoneExportModal = false;

    function handleClose() {
        open = false;
        onClose();
    }

    function showShindenExport() {
        showShindenExportModal = true;
    }

    function showAnimezoneExport() {
        showAnimezoneExportModal = true;
    }
</script>

<Dialog.Root bind:open on:close={handleClose}>
    <Dialog.Content class="sm:max-w-[425px] max-h-[90vh] overflow-hidden flex flex-col">
        <!-- Header -->
        <Dialog.Header class="flex-shrink-0">
            <Dialog.Title class="text-2xl font-bold">Eksport listy anime</Dialog.Title>
        </Dialog.Header>

        <!-- Scrollable content -->
        <div class="flex-grow pr-1 overflow-y-auto">
            <div class="flex flex-col items-center text-center">
                <!-- Export buttons -->
                <div class="grid w-full grid-cols-1 gap-4 mt-2">
                    <Button on:click={showShindenExport} class="flex w-full cursor-pointer items-center justify-center bg-[#6a5acd] text-white hover:bg-[#7b68ee] h-12 text-base font-medium">
                        <img src="/shinden-icon.png" alt="Shinden" class="w-5 h-5 mr-2" onerror="this.style.display='none'" />
                        Shinden
                    </Button>

                    <Button on:click={showAnimezoneExport} class="flex w-full cursor-pointer items-center justify-center bg-[#4a7bcc] text-white hover:bg-[#5a9de0] h-12 text-base font-medium">
                        <img src="/animezone-icon.png" alt="AnimeZone" class="w-5 h-5 mr-2" onerror="this.style.display='none'" />
                        AnimeZone
                    </Button>
                </div>
                <p class="mt-4 text-sm text-gray-400">Wybierz platformę do eksportu listy anime</p>

                <!-- Info section with all content visible -->
                <div class="w-full mt-6 space-y-4">
                    <!-- Why section -->
                    <div class="p-4 text-left border border-gray-700 rounded-lg bg-gray-800/70">
                        <div class="flex items-center mb-2">
                            <Info class="h-4 w-4 text-[#ee8585] mr-2 flex-shrink-0" />
                            <h3 class="text-sm font-medium text-white">Dlaczego warto przenieść swoją listę?</h3>
                        </div>
                        <p class="text-xs leading-relaxed text-gray-300">
                            Dzięki wtyczce <a href="https://malsync.moe/" target="_blank" rel="noopener noreferrer" class="text-[#ee8585] hover:text-[#8ec3f4] hover:underline">MalSync</a>,
                            posiadając listę AniList lub MyAnimeList możesz z niej korzystać także na Shindenie, AnimeZone, OglądajAnime,
                            Docchi oraz wielu innych w tym oczywiście na naszej stronie. Pod koniec oglądania każdego odcinka
                            zostanie on automatycznie zaktualizowany na twojej liście.
                        </p>
                    </div>

                    <!-- How section -->
                    <div class="p-4 text-left border border-gray-700 rounded-lg bg-gray-800/70">
                        <div class="flex items-center mb-2">
                            <HelpCircle class="h-4 w-4 text-[#8ec3f4] mr-2 flex-shrink-0" />
                            <h3 class="text-sm font-medium text-white">Jak zaimportować wyeksportowaną listę?</h3>
                        </div>
                        <ul class="pl-5 space-y-2 text-xs text-gray-300 list-disc">
                            <li>
                                <b>Anilist</b> - Kliknij <a href="https://anilist.co/settings/import" target="_blank" rel="noopener noreferrer" class="text-[#0089d0] font-medium hover:underline">[tutaj]</a> będąc zalogowanym, następnie upuść plik XML do okienka "MyAnimeList Import".
                            </li>
                            <li>
                                <b>MyAnimeList</b> - Kliknij <a href="https://myanimelist.net/import.php" target="_blank" rel="noopener noreferrer" class="font-medium text-blue-400 hover:underline">[tutaj]</a> będac zalogowanym, następnie wybierz opcję "MyAnimeList Import" i wybierz plik XML do importu.
                            </li>
                        </ul>
                    </div>

                    <!-- Account section -->
                    <div class="p-4 text-left border border-gray-700 rounded-lg bg-gray-800/70">
                        <div class="flex items-center mb-2">
                            <UserPlus class="flex-shrink-0 w-4 h-4 mr-2 text-green-400" />
                            <h3 class="text-sm font-medium text-white">Nie masz konta?</h3>
                        </div>
                        <p class="text-xs text-gray-300">
                            Załóż konto na <a href="https://anilist.co/signup" target="_blank" rel="noopener noreferrer" class="text-[#0089d0] font-medium hover:underline">AniList</a> lub
                            <a href="https://myanimelist.net/register.php?from=%2F&" target="_blank" rel="noopener noreferrer" class="font-medium text-blue-400 hover:underline">MyAnimeList</a>
                            aby móc importować i synchronizować swoją listę anime.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer with close button -->
        <Dialog.Footer class="flex-shrink-0 mt-6">
            <Button variant="outline" on:click={handleClose} class="w-full h-10 cursor-pointer hover:bg-gray-800">Zamknij</Button>
        </Dialog.Footer>
    </Dialog.Content>
</Dialog.Root>

<ShindenExportModal bind:open={showShindenExportModal} />
<AnimezoneExportModal bind:open={showAnimezoneExportModal} />
