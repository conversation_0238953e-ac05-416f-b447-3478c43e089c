import { sequence } from '@sveltejs/kit/hooks';
import * as Sentry from '@sentry/sveltekit';
import { createServerClient } from '@supabase/ssr';
import { redirect } from '@sveltejs/kit';
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { isAdminByRole } from '$lib/utils/roleUtils';


Sentry.init({
	release: 'lycoris.cafe@2.0.0',
	dsn: 'https://<EMAIL>/4508156592455760',
	tracesSampleRate: 0.1,
	profilesSampleRate: 0.1,
	beforeSend(event) {
		if (event.request && event.request.headers && event.request.headers['user-agent']) {
			const userAgent = event.request.headers['user-agent'].toLowerCase();

			const filteredKeywords = ['iphone', 'ipad', 'ipod', 'macintosh', 'safari', 'apple', 'playstation', 'ps4', 'ps5'];

			if (filteredKeywords.some((keyword) => userAgent.includes(keyword))) {
				return null;
			}
		}

		if (event.tags) {
			const filteredTags = ['iPhone', 'iPad', 'Safari', 'PlayStation'];
			if (filteredTags.some((tag) => event.tags.device === tag || event.tags.browser === tag)) {
				return null;
			}
		}

		return event;
	}
});

export const handleError = Sentry.handleErrorWithSentry();


const supabase = async ({ event, resolve }) => {
	event.locals.supabase = createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
		cookies: {
			getAll: () => event.cookies.getAll(),
			setAll: (cookiesToSet) => {
				cookiesToSet.forEach(({ name, value, options }) => {
					event.cookies.set(name, value, { ...options, path: '/' });
				});
			}
		}
	});

	event.locals.safeGetSession = async () => {
		const {
			data: { session }
		} = await event.locals.supabase.auth.getSession();
		if (!session) {
			return { session: null, user: null };
		}

		const {
			data: { user },
			error
		} = await event.locals.supabase.auth.getUser();
		if (error) {
			return { session: null, user: null };
		}

		// Create a Supabase client with service role to fetch the user's role
		const serviceClient = createServerClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
			cookies: {
				getAll: () => event.cookies.getAll(),
				setAll: () => {}
			}
		});

		try {
			// Fetch the user's role from the profiles table
			const { data: profileData, error: profileError } = await serviceClient
				.from('profiles')
				.select('role')
				.eq('id', user.id)
				.single();

			if (!profileError && profileData) {
				// Add the role to the user metadata
				user.user_metadata = {
					...user.user_metadata,
					profile: {
						...user.user_metadata?.profile,
						role: profileData.role
					}
				};

				// Log the updated user metadata for debugging
				// console.log('Updated user metadata in safeGetSession:', user.user_metadata);
				// console.log('User role from profiles table:', profileData.role);
			} else {
				console.error('Failed to fetch user role:', profileError);
			}
		} catch (err) {
			console.error('Error fetching user role:', err);
		}

		return { session, user };
	};

	return resolve(event, {
		filterSerializedResponseHeaders(name) {
			return name === 'content-range' || name === 'x-supabase-api-version';
		}
	});
};

const authGuard = async ({ event, resolve }) => {
	const { session, user } = await event.locals.safeGetSession();
	event.locals.session = session;
	event.locals.user = user;

	const privateRoutes = ['/admin', '/api/admin'];
	const isPrivateRoute = privateRoutes.some((route) => event.url.pathname.startsWith(route));

	// Check if user is authenticated
	if (!event.locals.session && isPrivateRoute) {
		redirect(303, '/');
	}

	// If authenticated, check if user has admin role
	if (user && isPrivateRoute) {
		// Check database role
		const hasAdminRole = await isAdminByRole(user.id);

		// If user is not an admin, redirect
		if (!hasAdminRole) {
			redirect(303, '/');
		}
	}

	return resolve(event);
};

export const handle = sequence(
	Sentry.sentryHandle(),
	supabase,
	authGuard
);