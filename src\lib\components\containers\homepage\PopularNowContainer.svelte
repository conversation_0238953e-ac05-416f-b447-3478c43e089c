<script>
	import { onMount, afterUpdate } from 'svelte';
	import PopularShow from '$lib/components/sections/homepage/PopularShow.svelte';
	import InfiniteComments from '$lib/components/containers/comments/InfiniteComments.svelte';
	import { tweened, spring } from 'svelte/motion';
	import { linear } from 'svelte/easing';
	import { ChevronDown, ChevronUp } from 'lucide-svelte';
	import { fade } from 'svelte/transition';
	import { userStore } from '$lib/stores/userLogin.js';
	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;

	// Get data from parent
	export let data, userSettings;
	let preferRomaji;
	if (!userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = userSettings.titleLanguage === 'romaji';
	}
	// Use server-provided data directly
	$: popularNow = data.popularNowData;

	let listenersInitialized = false;
	const FADE_DURATION = 250;

	let isFading = false;
	let containerRef;
	let carouselRef;
	let isVerticalView = false;
	let containerHeight = 0;
	let isDragging = false;
	let startX;
	let startScrollLeft;
	let lastScrollLeft = 0;
	let lastMouseX = 0;
	let velocityX = 0;
	let lastTimestamp = 0;
	let animationFrameId = null;
	let maxScroll = 0;
	let dragDistance = 0;
	let isOverscrolling = false;
	let lastMouseY = 0;
	let initialTouchY;
	let isDraggingStarted = false;
	let startY = 0;
	let isVerticalScroll = false;

	const DRAG_THRESHOLD = 5;
	const rows = 3;
	let itemWidth = 240;
	let itemHeight = 280;

	const containerHeightTweened = tweened(295, {
		duration: 400,
		easing: linear
	});

	$: if (isVerticalView) {
		containerHeightTweened.set(containerHeight / 2);
	} else {
		containerHeightTweened.set(containerHeight / 2.8);
	}

	const leftOverscroll = spring(0, {
		stiffness: 0.1,
		damping: 0.5
	});

	const rightOverscroll = spring(0, {
		stiffness: 1,
		damping: 0.5
	});

	function initializeEventListeners() {
		if (listenersInitialized) return;

		window.addEventListener('mousemove', handleDragMove);
		window.addEventListener('mouseup', handleDragEnd);
		window.addEventListener('touchmove', handleDragMove, { passive: false });
		window.addEventListener('touchend', handleDragEnd);

		listenersInitialized = true;
	}

	function getEventCoords(e) {
		if (e.type.includes('mouse')) {
			return {
				x: e.pageX,
				y: e.pageY
			};
		}
		return {
			x: e.touches[0].pageX,
			y: e.touches[0].pageY
		};
	}

	function handleDragStart(e) {
		if (!carouselRef || isVerticalView) return;

		// Check if this is a middle mouse button click (button 1)
		// If it is, don't start the drag behavior
		if (e.type === 'mousedown' && e.button === 1) {
			return;
		}

		const coords = getEventCoords(e);
		initialTouchY = coords.y;
		startX = coords.x;
		lastMouseX = startX;
		lastMouseY = initialTouchY;
		startScrollLeft = carouselRef.scrollLeft || 0;
		lastTimestamp = Date.now();
		isDraggingStarted = false;
		dragDistance = 0;

		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
		}

		if (carouselRef) {
			carouselRef.style.scrollBehavior = 'auto';
			carouselRef.style.cursor = 'grabbing';
		}
	}

	function handleDragMove(e) {
		if (!carouselRef || isVerticalView) return;

		const coords = getEventCoords(e);

		if (!isDraggingStarted) {
			const deltaX = Math.abs(coords.x - startX);
			const deltaY = Math.abs(coords.y - initialTouchY);

			if (deltaX > 5 && deltaX > deltaY) {
				isDragging = true;
				isDraggingStarted = true;
				if (e.type === 'touchmove') {
					e.preventDefault();
				}
				if (carouselRef) {
					carouselRef.style.scrollBehavior = 'auto';
					carouselRef.style.cursor = 'grabbing';
				}
			} else if (deltaY > 5 && deltaY > deltaX) {
				isDragging = false;
				isDraggingStarted = true;
				return;
			}
			return;
		}

		if (!isDragging) return;

		e.preventDefault();

		const currentTimestamp = Date.now();
		const timeElapsed = currentTimestamp - lastTimestamp;

		const deltaX = coords.x - lastMouseX;
		dragDistance += Math.abs(deltaX);

		if (timeElapsed > 0) {
			velocityX = deltaX / timeElapsed;
		}

		const walkX = startX - coords.x;
		let newScrollLeft = startScrollLeft + walkX;

		if (newScrollLeft < 0) {
			isOverscrolling = true;
			leftOverscroll.set(-newScrollLeft * 0.3);
			rightOverscroll.set(0);
			newScrollLeft = 0;
		} else if (newScrollLeft > maxScroll) {
			isOverscrolling = true;
			rightOverscroll.set((newScrollLeft - maxScroll) * 0.3);
			leftOverscroll.set(0);
			newScrollLeft = maxScroll;
		} else {
			isOverscrolling = false;
			leftOverscroll.set(0);
			rightOverscroll.set(0);
		}

		if (carouselRef) {
			carouselRef.scrollLeft = newScrollLeft;
			lastScrollLeft = newScrollLeft;
		}

		lastMouseX = coords.x;
		lastTimestamp = currentTimestamp;
	}

	function handleDragEnd(e) {
		if (!isDragging || !carouselRef || isVerticalView) return;
		isDragging = false;

		if (carouselRef) {
			carouselRef.style.cursor = 'grab';
		}

		if (isOverscrolling) {
			isOverscrolling = false;
			leftOverscroll.set(0, { hard: false });
			rightOverscroll.set(0, { hard: false });
		}

		if (dragDistance > DRAG_THRESHOLD) {
			e.preventDefault();
			e.stopPropagation();

			const clickPreventionHandler = (e) => {
				e.preventDefault();
				e.stopPropagation();
			};

			carouselRef.addEventListener('click', clickPreventionHandler, { capture: true });
			setTimeout(() => {
				if (carouselRef) {
					carouselRef.removeEventListener('click', clickPreventionHandler, { capture: true });
				}
			}, 100);
		}

		if (carouselRef) {
			if (Math.abs(velocityX) > 0.1) {
				applyMomentumScroll();
			} else {
				carouselRef.style.scrollBehavior = 'smooth';
			}
		}
	}

	function applyMomentumScroll() {
		if (Math.abs(velocityX) < 0.01) {
			carouselRef.style.scrollBehavior = 'smooth';
			return;
		}

		const currentScroll = carouselRef.scrollLeft;
		let newScroll = currentScroll - velocityX * 16;

		if (newScroll < 0) {
			newScroll = 0;
			velocityX = Math.abs(velocityX) * 0.2;
		} else if (newScroll > maxScroll) {
			newScroll = maxScroll;
			velocityX = -Math.abs(velocityX) * 0.2;
		}

		carouselRef.scrollLeft = newScroll;
		velocityX *= 0.95;

		animationFrameId = requestAnimationFrame(applyMomentumScroll);
	}

	function checkMobile() {
		return window.innerWidth < 1024;
	}

	function calculateGrid() {
		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 280;
		} else {
			itemWidth = 240;
			itemHeight = 280;
		}
		if (containerRef) {
			containerHeight = itemHeight * rows;
		}
	}

	function setupScrollBehavior() {
		if (!carouselRef) return;
		maxScroll = carouselRef.scrollWidth - carouselRef.clientWidth;

		carouselRef.removeEventListener('mousedown', handleDragStart);
		carouselRef.removeEventListener('touchstart', handleDragStart);

		if (!isVerticalView) {
			carouselRef.addEventListener('mousedown', handleDragStart);
			carouselRef.addEventListener('touchstart', handleDragStart, { passive: true });
			carouselRef.style.cursor = 'grab';
		} else {
			carouselRef.style.cursor = 'default';
		}
	}

	async function toggleView() {
		isFading = true;
		await new Promise((resolve) => setTimeout(resolve, FADE_DURATION / 2));

		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 280;
		} else {
			itemWidth = 240;
			itemHeight = 280;
		}

		isVerticalView = !isVerticalView;

		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
		}

		if (carouselRef) {
			carouselRef.style.scrollBehavior = 'auto';
			carouselRef.scrollLeft = 0;
			carouselRef.scrollTop = 0;
			leftOverscroll.set(0);
			rightOverscroll.set(0);
		}

		calculateGrid();
		setupScrollBehavior();

		if (isVerticalView) {
			containerHeightTweened.set(containerHeight);
		} else {
			containerHeightTweened.set(containerHeight / 1.8);
		}

		await new Promise((resolve) => setTimeout(resolve, FADE_DURATION / 2));
		isFading = false;

		requestAnimationFrame(() => {
			lazyLoadImages();
			if (carouselRef) {
				carouselRef.style.scrollBehavior = 'smooth';
			}
		});
	}

	function lazyLoadImages(forceLoad = false) {
		if (containerRef) {
			const images = containerRef.querySelectorAll('img[data-src]');
			const containerRect = containerRef.getBoundingClientRect();

			images.forEach((img) => {
				const imgRect = img.getBoundingClientRect();
				if (forceLoad || (imgRect.left < containerRect.right + 200 && imgRect.right > containerRect.left - 200)) {
					if (img.dataset.src && img.src !== img.dataset.src) {
						img.src = img.dataset.src;
						img.removeAttribute('data-src');
					}
				}
			});
		}
	}

	function forceLoadVisibleImages() {
		lazyLoadImages(true);
	}

	function handleScroll() {
		requestAnimationFrame(lazyLoadImages);
	}

	function handleResize() {
		calculateGrid();
		setupScrollBehavior();
		lazyLoadImages();
	}

	function getKey(anime, index) {
		return `${anime.id}-${index}`;
	}

	onMount(() => {
		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 280;
		}

		calculateGrid();
		setupScrollBehavior();
		initializeEventListeners();
		window.addEventListener('resize', handleResize);
		carouselRef?.addEventListener('scroll', handleScroll, { passive: true });

		return () => {
			if (animationFrameId) {
				cancelAnimationFrame(animationFrameId);
			}
			window.removeEventListener('resize', handleResize);
			carouselRef?.removeEventListener('scroll', handleScroll);

			if (listenersInitialized) {
				window.removeEventListener('mousemove', handleDragMove);
				window.removeEventListener('mouseup', handleDragEnd);
				window.removeEventListener('touchmove', handleDragMove);
				window.removeEventListener('touchend', handleDragEnd);
			}
		};
	});

	afterUpdate(() => {
		if (carouselRef) {
			setupScrollBehavior();
		}
		forceLoadVisibleImages();
	});

	$: if (popularNow) {
		setTimeout(forceLoadVisibleImages, 0);
	}
</script>

<section class="popular-now">
	<div class="{isLoggedIn ? '-mt-10' : '-mt-6'} mb-2 flex items-center justify-between pr-4 pl-4 sm:-mt-6 sm:mb-10 md:mt-6 md:mb-10 md:pr-8">
		<h2 class="text-xl font-bold text-white opacity-80 sm:-mb-4 md:-mb-8 md:text-3xl">Popularne</h2>
		<button on:click={toggleView} class="cursor-pointer rounded-full bg-gray-700 p-2 text-white hover:bg-gray-600 sm:-mb-2">
			{#if isVerticalView}
				<ChevronUp size={24} />
			{:else}
				<ChevronDown size={24} />
			{/if}
		</button>
	</div>

	<div class="flex flex-col lg:flex-row">
		<div class="order-2 mt-0 w-full md:mt-6 lg:order-1 lg:mt-0 lg:ml-2 lg:w-[40%]">
			<InfiniteComments {preferRomaji} pageSize={5} context="home" xlHeight="300px" lgHeight="300px" mdHeight="300px" smHeight="300px" enforceMaxDepth={true} />
		</div>
		<div class="order-1 w-full lg:order-2 lg:w-[60%] lg:pr-4">
			<div class="relative overflow-visible transition-[height] duration-400" style="height: {$containerHeightTweened}px">
				<div class="relative h-full w-full">
					{#if !isFading}
						<div class="absolute inset-0" transition:fade={{ duration: FADE_DURATION }}>
							<div class="carousel-container" class:is-vertical={isVerticalView} bind:this={containerRef}>
								<div
									class="carousel-content"
									class:vertical={isVerticalView}
									bind:this={carouselRef}
									style="transform: translate3d({!isVerticalView ? $leftOverscroll - $rightOverscroll : 0}px, 0, 0);"
								>
									<div class="carousel-slides">
										{#each popularNow as anime, index (getKey(anime, index))}
											<div class="carousel-slide">
												<PopularShow
													{preferRomaji}
													show={{
														id: anime.id,
														image: anime.poster,
														title: anime.title,
														english_title: anime.english_title,
														popularity: anime.popularity,
														rankings: anime.popular_rankings,
														rating: anime.rating,
														totalEpisodes: anime.total_episodes, // Changed from episodes.length
														format: anime.format,
														index: index
													}}
													{isDragging}
												/>
											</div>
										{/each}
									</div>
								</div>
							</div>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>
</section>

<style>
	.popular-now {
		position: relative;
		z-index: 10;
	}

	.carousel-container {
		width: 100%;
		height: calc(100% + 40px);
		margin: -20px 0;
		overflow-x: hidden;
		overflow-y: visible;
		-webkit-overflow-scrolling: touch;
		cursor: default;
		touch-action: pan-y;
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
		opacity: 1;
	}

	.carousel-container.is-vertical {
		overflow-y: auto;
		overflow-x: hidden;
		touch-action: pan-y;
	}

	.carousel-content {
		display: flex;
		transition: transform 0.2s ease-out;
		overflow-x: auto;
		overflow-y: visible;
		scrollbar-width: none;
		-ms-overflow-style: none;
		-webkit-overflow-scrolling: touch;
		cursor: grab;
		user-select: none;
		will-change: transform;
		touch-action: pan-y;
		width: 100%;
	}

	.carousel-content:active {
		cursor: grabbing;
	}

	.carousel-content::-webkit-scrollbar {
		display: none;
	}

	.carousel-content.vertical {
		flex-wrap: wrap;
		gap: 14px;
		padding: 14px;
		justify-content: center;
		align-items: flex-start;
		cursor: default;
		overflow-x: hidden;
		overflow-y: visible;
		touch-action: pan-y;
		transform: none !important;
	}

	.carousel-slides {
		display: flex;
		gap: 14px;
		padding: 20px 14px;
		min-width: min-content;
		width: max-content;
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
	}

	.vertical .carousel-slides {
		width: 100%;
		flex-wrap: wrap;
		justify-content: center;
	}

	.carousel-slide {
		flex: 0 0 auto;
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
	}

	@media (min-width: 640px) {
		.carousel-slides {
			gap: 10px;
			padding: 0 10px;
		}

		.carousel-container.is-vertical {
			scrollbar-width: thin;
			scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
		}

		.carousel-container.is-vertical::-webkit-scrollbar {
			display: block;
			width: 6px;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-track {
			background: transparent;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-thumb {
			background-color: rgba(255, 255, 255, 0.2);
			border-radius: 3px;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-thumb:hover {
			background-color: rgba(255, 255, 255, 0.3);
		}
	}

	@media (min-width: 768px) {
		.carousel-slides {
			gap: 14px;
			padding: 0 14px;
		}
	}

	.duration-400 {
		transition-duration: 400ms;
	}

	.vertical .carousel-slide {
		width: calc(50% - 7px);
	}

	@media (min-width: 450px) {
		.vertical .carousel-slide {
			width: auto;
		}
	}
</style>
