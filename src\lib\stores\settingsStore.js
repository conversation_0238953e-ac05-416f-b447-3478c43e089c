import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Initialize with default values - change English to Romaji as default
const initialSettings = {
  titleLanguage: 'romaji',
  episodeAutoUpdate: true,
  enableOneko: false,
  filterCompletedShows: false
};

// Create the store
const settingsStore = writable(initialSettings);

// Function to load settings from API
export async function loadSettings() {
  if (!browser) return;

  try {
    const response = await fetch('/api/user/settings');
    if (response.ok) {
      const data = await response.json();
      settingsStore.set({
        ...initialSettings,
        ...data
      });
    }
  } catch (error) {
    console.error('Error loading settings:', error);
  }
}

// Function to update a specific setting
export async function updateSetting(key, value) {
  // Update local store immediately for responsive UI
  settingsStore.update(settings => ({
    ...settings,
    [key]: value
  }));

  // Then send to server
  if (browser) {
    try {
      const response = await fetch('/api/user/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ [key]: value })
      });

      if (!response.ok) {
        // If server update fails, revert local change
        loadSettings();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating setting:', error);
      // Revert on error
      loadSettings();
      return false;
    }
  }

  return false;
}

export default settingsStore;