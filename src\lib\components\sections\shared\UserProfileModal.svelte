<script>
	// Existing imports
	import { LogOut, Trash2, Download } from 'lucide-svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Avatar from '$lib/components/ui/avatar';
	import * as Tabs from '$lib/components/ui/tabs';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { clearCache } from '$lib/utils/cacheUtils';
	import { browser } from '$app/environment';

	// Add import for settings store
	import settingsStore, { updateSetting as updateStoreSetting, loadSettings } from '$lib/stores/settingsStore';

	// Add new import for Switch component
	import * as Switch from '$lib/components/ui/switch';
	import { toast } from 'svelte-sonner';

	// Import Export Modals
	import ShindenExportModal from './ShindenExportModal.svelte';
	import AnimezoneExportModal from './AnimezoneExportModal.svelte';

	function sleep(ms) {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	// Existing props
	export let open = false;
	export let user;
	export let onClose = () => {};
	export let onLogout = () => {};

	// Add state for user settings
	let userSettings = {};
	let isUpdatingSettings = false;

	// Other existing variables
	let deleteConfirmInput = '';
	let showDeleteConfirm = false;
	let isDeleting = false;
	let showMalDialog = false;
	let anilistLoading = false;
	let malLoading = false;
	let shindenLoading = false;
	let showShindenExportModal = false;
	let showAnimezoneExportModal = false;
	let episodeAutoUpdate;
	let titleLanguage;
	let enableOneko;
	let filterCompletedShows;
	$: titleLanguage = $settingsStore.titleLanguage;
	$: useEnglishTitles = titleLanguage === 'english';
	$: episodeAutoUpdate = $settingsStore.episodeAutoUpdate;
	$: enableOneko = $settingsStore.enableOneko;
	$: filterCompletedShows = $settingsStore.filterCompletedShows;
	const CONFIRMATION_TEXT = 'WYCZYŚĆ DANE';
	const ANILIST_CLIENT_ID = '9530';
	const MAL_CLIENT_ID = '7f24090fc4335cf45b5c338e512395b3';

	async function fetchUserSettings() {
		if (!user) return;
		await loadSettings();
	}

	async function updateSetting(key, value) {
		if (!user) return;

		try {
			isUpdatingSettings = true;
			const success = await updateStoreSetting(key, value);
			if (success) {
				toast.success('Ustawienia zaktualizowane pomyślnie');
			} else {
				toast.error('Nie udało się zaktualizować ustawień');
			}
		} catch (error) {
			console.error('Error updating settings:', error);
			toast.error('Nie udało się zaktualizować ustawień');
		} finally {
			isUpdatingSettings = false;
		}
	}

	async function handleTitleLanguageChange(useEnglish) {
		updateSetting('titleLanguage', useEnglish ? 'english' : 'romaji');
		clearCache();
		await sleep(1000);
		window.location.reload();
	}

	$: if (open && user) {
		fetchUserSettings();
	}

	// Generate a random string for code_verifier (PKCE)
	function generateRandomString(length) {
		const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
		let text = '';
		for (let i = 0; i < length; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	async function signInWithAniList() {
		clearCache();
		anilistLoading = true;
		const authUrl = new URL('https://anilist.co/api/v2/oauth/authorize');
		authUrl.searchParams.set('client_id', ANILIST_CLIENT_ID);
		authUrl.searchParams.set('response_type', 'code');
		authUrl.searchParams.set('redirect_uri', `${window.location.origin}/auth/anilist`);

		window.location.href = authUrl.toString();
	}

	async function signInWithMAL() {
		clearCache();
		malLoading = true;

		// Generate code verifier (random string between 43-128 chars)
		const codeVerifier = generateRandomString(128);

		// For MAL, code_challenge is the same as code_verifier (plain transformation)
		const codeChallenge = codeVerifier;

		// Store the code verifier in a cookie
		document.cookie = `mal_code_verifier=${codeVerifier}; path=/; max-age=3600; SameSite=Lax; Secure`;

		// Generate state parameter for security
		const state = 'RequestID' + Math.random().toString(36).substring(2);

		const authUrl = new URL('https://myanimelist.net/v1/oauth2/authorize');
		authUrl.searchParams.set('client_id', MAL_CLIENT_ID);
		authUrl.searchParams.set('response_type', 'code');
		authUrl.searchParams.set('redirect_uri', `${window.location.origin}/auth/myanimelist`);
		authUrl.searchParams.set('code_challenge', codeChallenge);
		authUrl.searchParams.set('code_challenge_method', 'plain');
		authUrl.searchParams.set('state', state);
		// MAL recommends scopes but doesn't require them to be URL encoded
		authUrl.searchParams.set('scope', 'read_list_on_write_list');

		window.location.href = authUrl.toString();
	}

	function handleMalButtonClick() {
		signInWithMAL();
	}

	function showShindenExport() {
		showShindenExportModal = true;
	}

	function showAnimezoneExport() {
		showAnimezoneExportModal = true;
	}

	async function handleDeleteAccount() {
		if (deleteConfirmInput !== CONFIRMATION_TEXT) return;

		try {
			isDeleting = true;
			const response = await fetch('/auth/delete-account', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (response.ok) {
				window.location.reload();
			} else {
				throw new Error('Nie udało się usunąć konta');
			}
		} catch (error) {
			console.error('Błąd usuwania konta:', error);
		} finally {
			isDeleting = false;
		}
	}
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="sm:max-w-[425px]">
		{#if user}
			<Dialog.Header>
				<Dialog.Title>Profil użytkownika</Dialog.Title>
			</Dialog.Header>

			<Tabs.Root value="profile" class="w-full">
				<Tabs.List class="grid w-full grid-cols-2">
					<Tabs.Trigger value="profile" class="cursor-pointer hover:bg-gray-900">Profil</Tabs.Trigger>
					<Tabs.Trigger value="manage" class="cursor-pointer hover:bg-gray-900">Zarządzaj</Tabs.Trigger>
				</Tabs.List>

				<Tabs.Content value="profile" class="mt-4">
					<div class="flex items-center p-4 space-x-4">
						<Avatar.Root class="w-16 h-16">
							<Avatar.Image src={user?.user_metadata?.avatar} alt={user?.user_metadata?.name} />
							<Avatar.Fallback>{user?.user_metadata?.name?.[0]}</Avatar.Fallback>
						</Avatar.Root>
						<div>
							<h4 class="text-lg font-semibold">{user?.user_metadata?.name}</h4>
						</div>
					</div>

					<!-- Settings section -->
					<div class="px-4 mt-4 space-y-6">
						<h5 class="text-base font-semibold">Ustawienia synchronizacji</h5>

						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium">Automatyczna aktualizacja obejrzanych odcinków</p>
							</div>
							<Switch.Root checked={episodeAutoUpdate} onCheckedChange={(value) => updateSetting('episodeAutoUpdate', value)} disabled={isUpdatingSettings} class="cursor-pointer data-[state=checked]:bg-blue-500">
								<Switch.Thumb />
							</Switch.Root>
						</div>

						<!-- Modified setting for title language preference using a switch -->
						<h5 class="text-base font-semibold">Ustawienia języka</h5>
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium">Użyj angielskich tytułów anime</p>
								<p class="text-xs text-gray-400">Domyślnie wyświetlane są japońskie tytuły (romaji)</p>
							</div>
							<Switch.Root checked={useEnglishTitles} onCheckedChange={handleTitleLanguageChange} disabled={isUpdatingSettings} class="cursor-pointer data-[state=checked]:bg-blue-500">
								<Switch.Thumb />
							</Switch.Root>
						</div>

						<!-- Continue Watching filter setting -->
						<h5 class="text-base font-semibold">Ustawienia listy "Kontynuuj oglądanie"</h5>
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium">Ukryj anime bez nieobejrzanych odcinków</p>
								<p class="text-xs text-gray-400">Filtruj anime, które są w pełni obejrzane</p>
							</div>
							<Switch.Root checked={filterCompletedShows} onCheckedChange={(value) => updateSetting('filterCompletedShows', value)} disabled={isUpdatingSettings} class="cursor-pointer data-[state=checked]:bg-blue-500">
								<Switch.Thumb />
							</Switch.Root>
						</div>

						<!-- Oneko (cat follows mouse) setting -->
						<h5 class="text-base font-semibold">Ustawienia wizualne</h5>
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium">Włącz kotka podążającego za kursorem</p>
								<p class="text-xs text-gray-400">Mały kotek będzie podążał za kursorem myszy</p>
							</div>
							<Switch.Root checked={enableOneko} onCheckedChange={(value) => updateSetting('enableOneko', value)} disabled={isUpdatingSettings} class="cursor-pointer data-[state=checked]:bg-blue-500">
								<Switch.Thumb />
							</Switch.Root>
						</div>
					</div>

					<div class="flex justify-end pt-4 space-x-2 cursor-pointer">
						<Button variant="outline" class="cursor-pointer" on:click={onClose}>Zamknij</Button>
						<Button variant="destructive" class="cursor-pointer bg-[#ee8585] text-black hover:bg-[#8ec3f4]" on:click={onLogout}>
							<LogOut class="w-4 h-4 mr-2" />
							Wyloguj się
						</Button>
					</div>
				</Tabs.Content>

				<Tabs.Content value="manage" class="mt-4">
					<div class="space-y-4">
						{#if !showDeleteConfirm}
							<Alert variant="destructive" class="text-red-300">
								<AlertDescription>Usuń wszystkie dane z naszego serwisu klikając w przycisk poniżej.<br />Dane z połączonego serwisu nie zostaną naruszone. <br /> <br />Usunie to wszystkie twoje komentarze oraz inne dane które mamy zapisane na naszych serwerach jak dane twojego profilu (Nick, link do avatara).<br /><br />Tej operacji nie można cofnąć.</AlertDescription>
							</Alert>
							<div class="flex justify-end">
								<Button variant="destructive" class="cursor-pointer bg-[#ee8585] text-black hover:bg-[#8ec3f4]" on:click={() => (showDeleteConfirm = true)}>
									<Trash2 class="w-4 h-4 mr-2" />
									Wyczyść dane
								</Button>
							</div>
						{:else}
							<Alert variant="destructive" class="text-red-300">
								<AlertDescription>Aby potwierdzić wyczszczenie danych, wpisz <br />"WYCZYŚĆ DANE" w polu poniżej.</AlertDescription>
							</Alert>
							<Input type="text" placeholder="WYCZYŚĆ DANE" bind:value={deleteConfirmInput} />
							<div class="flex justify-end space-x-2">
								<Button
									variant="outline"
									class="cursor-pointer"
									on:click={() => {
										showDeleteConfirm = false;
										deleteConfirmInput = '';
									}}
								>
									Anuluj
								</Button>
								<Button variant="destructive" class="cursor-pointer bg-[#ee8585] text-black hover:bg-[#8ec3f4]" on:click={handleDeleteAccount} disabled={deleteConfirmInput !== CONFIRMATION_TEXT || isDeleting}>
									{#if isDeleting}
										Czyszczenie...
									{:else}
										Potwierdź wyczyszczenie
									{/if}
								</Button>
							</div>
						{/if}
					</div>
				</Tabs.Content>
			</Tabs.Root>
		{:else}
			<!-- Login Prompt -->
			<div class="flex flex-col items-center text-center">
				<h2 class="mb-4 text-2xl font-bold">Zaloguj się</h2>
				<div class="grid w-full grid-cols-1 gap-4">
					<Button on:click={signInWithAniList} class="flex w-full cursor-pointer items-center justify-center bg-[#0089d0] text-white hover:bg-[#00a8ff]" disabled={anilistLoading}>
						{#if anilistLoading}
							<div class="w-4 h-4 mr-2 border-2 border-white rounded-full animate-spin border-t-transparent"></div>
							Logowanie...
						{:else}
							<img src="/anilist-logo.svg" alt="AniList Logo" class="w-6 h-6 mr-2" />
							AniList
						{/if}
					</Button>

					<Button on:click={handleMalButtonClick} class="flex w-full cursor-pointer items-center justify-center bg-[#2E51A2] text-white hover:bg-[#4867B4]">
						<img src="/myanimelist-logo.svg" alt="MyAnimeList Logo" class="w-6 h-6 mr-2" />
						MyAnimeList
					</Button>

					<Button on:click={showShindenExport} class="flex w-full cursor-pointer items-center justify-center bg-[#6a5acd] text-white hover:bg-[#7b68ee]">Shinden</Button>

					<Button on:click={showAnimezoneExport} class="flex w-full cursor-pointer items-center justify-center bg-[#4a7bcc] text-white hover:bg-[#5a9de0]">AnimeZone</Button>
				</div>
				<p class="mt-4 text-gray-400">Wybierz platformę do synchronizacji listy anime</p>
				<!-- Added signup message and links -->
				<p class="mt-4 text-gray-400">
					Nie masz konta?
					<a href="https://anilist.co/signup" target="_blank" rel="noopener noreferrer" class="text-[#0089d0] hover:underline">Kliknij tutaj aby założyć konto AniList</a> lub
					<a href="https://myanimelist.net/register.php?from=%2F&" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:underline">tutaj dla MyAnimeList</a>
				</p>

				<Button variant="outline" on:click={onClose} class="w-full mt-4 cursor-pointer">Zamknij</Button>
			</div>
		{/if}
	</Dialog.Content>
</Dialog.Root>

<ShindenExportModal bind:open={showShindenExportModal} />

<AnimezoneExportModal bind:open={showAnimezoneExportModal} />
