<script>
	import { Button } from '$lib/components/ui/button';
	import CommentBlock from './CommentBlock.svelte';
	import { X, ArrowLeft } from 'lucide-svelte';
	import * as Dialog from '$lib/components/ui/dialog';

	export let close = () => {};
	export let comment = {};
	export let context = 'home';
	export let enforceMaxDepth = true;

	let localEnforceMaxDepth = enforceMaxDepth;
	let currentMaxDepth = window.innerWidth >= 768 ? 10 : 4;
	let threadHistory = [];
	let currentComment = comment;
	let replyingToComment = null;
	let currentCancelReply = null;

	function handleKeydown(event) {
		if (event.key === 'Escape') {
			close();
		}
	}

	function handleResize() {
		currentMaxDepth = window.innerWidth >= 768 ? 10 : 4;
	}

	function continueThread(threadComment) {
		threadHistory = [...threadHistory, currentComment];
		currentComment = threadComment;
	}

	function handleReplyStart(comment) {
		replyingToComment = comment;
	}

	function handleReplyCancel() {
		replyingToComment = null;
	}

	function handleBindCancelReply(cancelFn) {
		currentCancelReply = cancelFn;
	}

	function goBack() {
		if (replyingToComment && currentCancelReply) {
			currentCancelReply();
			replyingToComment = null;
		}

		if (threadHistory.length > 0) {
			currentComment = threadHistory[threadHistory.length - 1];
			threadHistory = threadHistory.slice(0, -1);
		} else {
			close();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} on:resize={handleResize} />

<div class="modal-container">
	<Dialog.Header class="border-border flex items-start justify-between border-b p-6">
		<div class="flex items-center gap-4">
			<Button variant="ghost" size="sm" on:click={goBack} class="flex items-center gap-2">
				<ArrowLeft class="h-4 w-4" />
				{threadHistory.length === 0 ? 'Powrót do komentarzy' : 'Powrót'}
			</Button>
		</div>
		<Button variant="ghost" size="icon" class="absolute top-4 right-4" on:click={close}>
			<X class="h-4 w-4" />
		</Button>
	</Dialog.Header>

	<div class="flex-1 overflow-y-auto p-6 pr-[calc(1.5rem+8px)]">
		<CommentBlock
			comment={currentComment}
			{context}
			depth={0}
			enforceMaxDepth={localEnforceMaxDepth}
			maxDepth={currentMaxDepth}
			hideSubtitle={true}
			parentThreadLines={[]}
			onContinueThread={continueThread}
			onReplyStart={handleReplyStart}
			onReplyCancel={handleReplyCancel}
			bindCancelReply={handleBindCancelReply}
		/>
	</div>

	<Dialog.Footer class="border-border flex justify-end gap-2 border-t p-6">
		<Button variant="outline" on:click={close}>Zamknij</Button>
	</Dialog.Footer>
</div>

<style>
	.modal-container {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 95vw;
		height: 90vh;
		max-width: 1400px;
		background: hsl(var(--background));
		border-radius: var(--radius);
		border: 1px solid hsl(var(--border));
		display: flex;
		flex-direction: column;
	}

	/* Custom scrollbar styles */
	div {
		scrollbar-width: thin;
		scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
	}

	div::-webkit-scrollbar {
		width: 8px;
	}

	div::-webkit-scrollbar-track {
		background: transparent;
	}

	div::-webkit-scrollbar-thumb {
		background-color: hsl(var(--muted-foreground) / 0.3);
		border-radius: 4px;
	}

	@media (max-width: 640px) {
		.modal-container {
			width: 100vw;
			height: 100vh;
			border-radius: 0;
		}
	}
</style>
